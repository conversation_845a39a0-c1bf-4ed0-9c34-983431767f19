# 🏗️ Octane Recoil Scripts - Build Configuration Guide

## 📋 Build Modes Overview

### 🔧 Debug Mode
**Purpose:** Development and testing
**Command:** `build-debug.bat` or `dotnet build --configuration Debug`

**Features Enabled:**
- ✅ Developer tools (F12, Ctrl+Shift+I, Ctrl+Shift+J)
- ✅ Browser console access
- ✅ Text selection and copy/paste
- ✅ Right-click context menu
- ✅ Detailed error messages and stack traces
- ✅ Source maps and debugging symbols
- ✅ Hot reload support
- ✅ Verbose logging to console

**Security Features:**
- ❌ Hardware ID tracking disabled
- ❌ Discord security logging disabled
- ❌ Anti-debugging protection disabled
- ❌ Process monitoring disabled

**Build Output:**
- Multiple files (not single executable)
- Debug symbols included (.pdb files)
- Larger file size
- Faster build time

---

### 🔒 Production Mode
**Purpose:** Distribution to end users
**Command:** `build-production.bat` or `dotnet build --configuration Release`

**Features Disabled:**
- ❌ Developer tools completely blocked
- ❌ Console methods overridden (console.log, etc.)
- ❌ Text selection disabled
- ❌ Right-click context menu disabled
- ❌ Drag and drop disabled
- ❌ View source disabled (Ctrl+U)
- ❌ Browser shortcuts disabled

**Security Features Enabled:**
- ✅ Hardware ID tracking and backend reporting
- ✅ Discord security logging for violations
- ✅ Anti-debugging protection
- ✅ Process monitoring (detects debugging tools)
- ✅ Developer tools detection and blocking
- ✅ Memory protection
- ✅ Tamper detection

**Build Output:**
- Single executable file (.exe)
- Compressed and optimized
- No debug symbols
- Smaller file size
- Longer build time

---

## 🛠️ Build Commands

### Quick Build Commands
```bash
# Debug build
dotnet build RecoilController --configuration Debug

# Production build
dotnet build RecoilController --configuration Release

# Debug with publish
dotnet publish RecoilController --configuration Debug --output builds/debug

# Production with publish
dotnet publish RecoilController --configuration Release --output builds/production
```

### Using Build Scripts
```bash
# Debug build (recommended for development)
build-debug.bat

# Production build (recommended for distribution)
build-production.bat
```

---

## 🔐 Security Implementation Details

### WebView2 Security (Production Only)
- **Dev Tools Blocking:** F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U all blocked
- **Context Menu:** Right-click completely disabled
- **Text Selection:** CSS and JavaScript prevent text selection
- **Console Override:** All console methods return null
- **Eval Blocking:** `eval()` and `Function()` constructor disabled
- **Developer Tools Detection:** Monitors window size changes to detect dev tools

### Hardware Tracking
- **Hardware ID Generation:** SHA256 hash of system characteristics
- **Backend Reporting:** Automatic reporting to VPS backend
- **Consistent ID:** Same algorithm as flasher for consistency

### Discord Security Logging
- **Real-time Alerts:** Security violations sent to Discord webhook
- **Error Reporting:** Application errors logged to Discord
- **Hardware Tracking:** Failed hardware ID reports logged

### Process Monitoring
- **Debugging Tools:** Detects x64dbg, Cheat Engine, IDA Pro, etc.
- **Analysis Tools:** Monitors for Wireshark, Fiddler, Burp Suite
- **Reverse Engineering:** Detects .NET decompilers like dnSpy, ILSpy

---

## 📁 Build Output Structure

### Debug Build (`builds/debug/`)
```
RecoilController.exe          # Main executable
RecoilController.dll          # Application library
RecoilController.pdb          # Debug symbols
RecoilController.deps.json    # Dependencies
RecoilController.runtimeconfig.json
runtimes/                     # Runtime libraries
```

### Production Build (`builds/production/`)
```
RecoilController.exe          # Single self-contained executable
```

---

## 🚀 Distribution Guidelines

### Debug Builds
- **Never distribute** debug builds to end users
- Use only for development and internal testing
- Contains debugging information that could be exploited

### Production Builds
- **Safe for distribution** to end users
- All security features enabled
- Optimized for performance and security
- Hardware ID tracking ensures license compliance

---

## 🔧 Conditional Compilation Symbols

### Debug Mode Symbols
- `DEBUG` - Standard debug symbol
- `TRACE` - Tracing enabled
- `ALLOW_DEV_TOOLS` - WebView2 dev tools allowed

### Production Mode Symbols
- `RELEASE` - Standard release symbol
- `TRACE` - Tracing enabled
- `PRODUCTION_BUILD` - Production-specific features
- `BLOCK_DEV_TOOLS` - WebView2 dev tools blocked
- `ENABLE_SECURITY` - Security features enabled

---

## ⚠️ Important Notes

1. **Always use production builds** for distribution
2. **Test thoroughly** in debug mode before building production
3. **Hardware ID tracking** requires VPS backend to be running
4. **Discord webhooks** must be configured for security logging
5. **Single executable** in production mode includes all dependencies
6. **File size** will be larger in production due to self-contained deployment

---

## 🐛 Troubleshooting

### Build Failures
- Ensure .NET 8.0 SDK is installed
- Check for missing NuGet packages
- Verify project file syntax

### Security Issues
- Check Discord webhook configuration
- Verify VPS backend connectivity
- Ensure hardware ID generation works

### WebView2 Issues
- Install WebView2 runtime on target machines
- Check for Windows version compatibility
- Verify HTML content loads correctly
