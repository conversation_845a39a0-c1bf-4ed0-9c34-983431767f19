/*
 * System Information Implementation
 * Handles system diagnostics, monitoring, and information reporting
 */

#include "system_info.h"
#include <esp_chip_info.h>
#include <esp_flash.h>

SystemInfo::SystemInfo() {
    bootTime = 0;
    diagnosticsEnabled = true;
    lastDiagnosticCheck = 0;
    minFreeHeap = UINT32_MAX;
    maxFreeHeap = 0;
    averageCpuUsage = 0.0f;
    totalResets = 0;
    lastPerformanceCheck = 0;
    systemLoad = 0.0f;
    taskSwitches = 0;
    
    // Initialize metrics
    memset(&metrics, 0, sizeof(SystemMetrics));
}

bool SystemInfo::initialize() {
    Serial.println("📊 Initializing System Information...");
    
    // Record initial metrics
    recordBootTime();
    updateMetrics();
    
    // Get initial heap values
    uint32_t currentHeap = ESP.getFreeHeap();
    minFreeHeap = currentHeap;
    maxFreeHeap = currentHeap;
    
    Serial.println("✅ System Information initialized successfully");
    
    return true;
}

void SystemInfo::recordBootTime() {
    bootTime = millis();
    metrics.boot_time_ms = bootTime;
}

void SystemInfo::printEnhancedSystemInfo() {
    Serial.println("========================================");
    Serial.println("ESP32-S2 Enhanced HID Mouse Firmware");
    Serial.println("========================================");
    Serial.println("Firmware Version: " + String(FIRMWARE_VERSION));
    Serial.println("Hardware Version: " + String(HARDWARE_VERSION));
    Serial.println("Security Level: " + String(SECURITY_LEVEL));
    Serial.println("Build Date: " + String(BUILD_DATE));
    Serial.println("Build Time: " + String(BUILD_TIME));
    Serial.println("----------------------------------------");
    
    // Chip information
    esp_chip_info_t chip_info;
    esp_chip_info(&chip_info);
    
    Serial.println("Chip Model: " + String(ESP.getChipModel()));
    Serial.println("Chip Revision: " + String(ESP.getChipRevision()));
    Serial.println("CPU Cores: " + String(chip_info.cores));
    Serial.println("CPU Frequency: " + String(ESP.getCpuFreqMHz()) + " MHz");
    Serial.println("Flash Size: " + String(ESP.getFlashChipSize() / 1024 / 1024) + " MB");
    Serial.println("Flash Speed: " + String(ESP.getFlashChipSpeed() / 1000000) + " MHz");
    Serial.println("----------------------------------------");
    
    // Memory information
    Serial.println("Free Heap: " + String(ESP.getFreeHeap()) + " bytes");
    Serial.println("Min Free Heap: " + String(minFreeHeap) + " bytes");
    Serial.println("Max Free Heap: " + String(maxFreeHeap) + " bytes");
    Serial.println("Heap Size: " + String(ESP.getHeapSize()) + " bytes");
    Serial.println("----------------------------------------");
    
    // Runtime information
    Serial.println("Uptime: " + String(getUptime()) + " ms");
    Serial.println("Boot Time: " + String(bootTime) + " ms");
    Serial.println("Reset Reason: " + String(ESP.getResetReason()));
    Serial.println("----------------------------------------");
    
    // Feature flags
    Serial.println("Features Enabled:");
    Serial.println("  Anti-Detection: " + String(FEATURE_ANTI_DETECTION_ENABLED ? "YES" : "NO"));
    Serial.println("  Stealth Mode: " + String(FEATURE_STEALTH_MODE_ENABLED ? "YES" : "NO"));
    Serial.println("  Template Recoil: " + String(FEATURE_TEMPLATE_RECOIL_ENABLED ? "YES" : "NO"));
    Serial.println("  Zero Delay: " + String(FEATURE_ZERO_DELAY_ENABLED ? "YES" : "NO"));
    Serial.println("  Security Audit: " + String(FEATURE_SECURITY_AUDIT_ENABLED ? "YES" : "NO"));
    Serial.println("========================================");
}

void SystemInfo::printSystemInfo() {
    Serial.println("System Information:");
    Serial.println("  Firmware: " + String(FIRMWARE_VERSION));
    Serial.println("  Uptime: " + String(getUptime()) + " ms");
    Serial.println("  Free Heap: " + String(ESP.getFreeHeap()) + " bytes");
    Serial.println("  CPU Freq: " + String(ESP.getCpuFreqMHz()) + " MHz");
}

void SystemInfo::printHardwareInfo() {
    Serial.println("Hardware Information:");
    Serial.println("  Model: " + String(ESP.getChipModel()));
    Serial.println("  Revision: " + String(ESP.getChipRevision()));
    Serial.println("  Flash: " + String(ESP.getFlashChipSize() / 1024 / 1024) + " MB");
    Serial.println("  MAC: " + WiFi.macAddress());
}

void SystemInfo::printMemoryInfo() {
    uint32_t freeHeap = ESP.getFreeHeap();
    uint32_t heapSize = ESP.getHeapSize();
    float usagePercent = ((float)(heapSize - freeHeap) / heapSize) * 100.0f;
    
    Serial.println("Memory Information:");
    Serial.println("  Total Heap: " + String(heapSize) + " bytes");
    Serial.println("  Free Heap: " + String(freeHeap) + " bytes");
    Serial.println("  Used Heap: " + String(heapSize - freeHeap) + " bytes");
    Serial.println("  Usage: " + String(usagePercent, 1) + "%");
    Serial.println("  Min Free: " + String(minFreeHeap) + " bytes");
    Serial.println("  Max Free: " + String(maxFreeHeap) + " bytes");
}

void SystemInfo::printPerformanceInfo() {
    Serial.println("Performance Information:");
    Serial.println("  System Load: " + String(systemLoad, 2) + "%");
    Serial.println("  Avg CPU Usage: " + String(averageCpuUsage, 2) + "%");
    Serial.println("  Task Switches: " + String(taskSwitches));
    Serial.println("  Commands Processed: " + String(metrics.total_commands));
    Serial.println("  Successful Commands: " + String(metrics.successful_commands));
    Serial.println("  Failed Commands: " + String(metrics.failed_commands));
    Serial.println("  Avg Response Time: " + String(metrics.average_response_time_us, 2) + " μs");
}

void SystemInfo::updateMetrics() {
    // Update heap tracking
    uint32_t currentHeap = ESP.getFreeHeap();
    if (currentHeap < minFreeHeap) {
        minFreeHeap = currentHeap;
    }
    if (currentHeap > maxFreeHeap) {
        maxFreeHeap = currentHeap;
    }
    
    // Update metrics structure
    metrics.boot_time_ms = bootTime;
    
    // Calculate system load (simplified)
    unsigned long currentTime = millis();
    if (lastPerformanceCheck > 0) {
        unsigned long timeDiff = currentTime - lastPerformanceCheck;
        if (timeDiff > 0) {
            // Simple load calculation based on free heap changes
            systemLoad = ((float)(maxFreeHeap - currentHeap) / maxFreeHeap) * 100.0f;
        }
    }
    lastPerformanceCheck = currentTime;
}

void SystemInfo::performSystemDiagnostics() {
    if (!diagnosticsEnabled) return;
    
    unsigned long currentTime = millis();
    if (currentTime - lastDiagnosticCheck >= 10000) { // Every 10 seconds
        lastDiagnosticCheck = currentTime;
        
        Serial.println("🔍 Performing system diagnostics...");
        
        // Check system health
        checkSystemHealth();
        
        // Update metrics
        updateMetrics();
        
        // Validate system integrity
        if (!validateSystemIntegrity()) {
            Serial.println("⚠️ System integrity check failed");
        }
        
        Serial.println("✅ System diagnostics complete");
    }
}

void SystemInfo::checkSystemHealth() {
    bool healthOK = true;
    
    // Check memory health
    uint32_t freeHeap = ESP.getFreeHeap();
    if (freeHeap < HEAP_RESERVE_MINIMUM) {
        Serial.println("⚠️ Low memory warning: " + String(freeHeap) + " bytes");
        healthOK = false;
    }
    
    // Check uptime for potential overflow
    uint32_t uptime = getUptime();
    if (uptime > 4000000000UL) { // Close to uint32_t overflow
        Serial.println("⚠️ Uptime approaching overflow: " + String(uptime) + " ms");
        healthOK = false;
    }
    
    // Check for excessive resets
    if (totalResets > 10) {
        Serial.println("⚠️ Excessive resets detected: " + String(totalResets));
        healthOK = false;
    }
    
    if (healthOK) {
        Serial.println("✅ System health check passed");
    }
}

bool SystemInfo::validateSystemIntegrity() {
    // Basic integrity checks
    
    // Check if critical constants are intact
    if (String(FIRMWARE_VERSION).length() == 0) {
        return false;
    }
    
    // Check if heap is reasonable
    uint32_t freeHeap = ESP.getFreeHeap();
    if (freeHeap == 0 || freeHeap > ESP.getHeapSize()) {
        return false;
    }
    
    // Check if CPU frequency is reasonable
    uint32_t cpuFreq = ESP.getCpuFreqMHz();
    if (cpuFreq < 80 || cpuFreq > 240) {
        return false;
    }
    
    return true;
}

SystemMetrics SystemInfo::getSystemMetrics() const {
    return metrics;
}

void SystemInfo::resetMetrics() {
    memset(&metrics, 0, sizeof(SystemMetrics));
    metrics.boot_time_ms = bootTime;
    
    // Reset heap tracking
    uint32_t currentHeap = ESP.getFreeHeap();
    minFreeHeap = currentHeap;
    maxFreeHeap = currentHeap;
    
    Serial.println("📊 System metrics reset");
}

uint32_t SystemInfo::getFreeHeap() const {
    return ESP.getFreeHeap();
}

uint32_t SystemInfo::getMinFreeHeap() const {
    return minFreeHeap;
}

uint32_t SystemInfo::getMaxFreeHeap() const {
    return maxFreeHeap;
}

float SystemInfo::getMemoryUsagePercent() const {
    uint32_t heapSize = ESP.getHeapSize();
    uint32_t freeHeap = ESP.getFreeHeap();
    return ((float)(heapSize - freeHeap) / heapSize) * 100.0f;
}

float SystemInfo::getSystemLoad() const {
    return systemLoad;
}

float SystemInfo::getAverageCpuUsage() const {
    return averageCpuUsage;
}

uint32_t SystemInfo::getUptime() const {
    return millis() - bootTime;
}

void SystemInfo::enableDiagnostics(bool enabled) {
    diagnosticsEnabled = enabled;
    Serial.println("🔍 System diagnostics: " + String(enabled ? "ENABLED" : "DISABLED"));
}

void SystemInfo::emergencyDiagnostics() {
    Serial.println("🚨 EMERGENCY DIAGNOSTICS");
    Serial.println("========================");
    
    printEnhancedSystemInfo();
    printMemoryInfo();
    printPerformanceInfo();
    
    // Force garbage collection
    ESP.restart();
}

void SystemInfo::dumpSystemState() {
    Serial.println("💾 SYSTEM STATE DUMP");
    Serial.println("====================");
    
    Serial.println("Timestamp: " + String(millis()));
    Serial.println("Free Heap: " + String(ESP.getFreeHeap()));
    Serial.println("Min Heap: " + String(minFreeHeap));
    Serial.println("Max Heap: " + String(maxFreeHeap));
    Serial.println("CPU Freq: " + String(ESP.getCpuFreqMHz()));
    Serial.println("Flash Size: " + String(ESP.getFlashChipSize()));
    Serial.println("Chip Rev: " + String(ESP.getChipRevision()));
    Serial.println("Reset Reason: " + String(ESP.getResetReason()));
    
    Serial.println("====================");
}
