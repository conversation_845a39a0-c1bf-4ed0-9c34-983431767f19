using System;
using System.Threading.Tasks;
using OctaneFlasher.Core;
using OctaneFlasher.Modes;
using OctaneFlasher.Services;

namespace OctaneFlasher
{
    /// <summary>
    /// Main program entry point for Octane ESP32 Flasher
    /// </summary>
    class Program
    {
        /// <summary>
        /// Main entry point for the application
        /// </summary>
        static async Task Main(string[] args)
        {
            try
            {
                // Load configuration
                await Configuration.LoadConfigurationAsync();

                // Initialize console
                Console.OutputEncoding = System.Text.Encoding.UTF8;
                Console.Title = $"Octane ESP32 Flasher v{Configuration.Version} ({Configuration.BuildMode.ToUpper()})";

                // Parse command line arguments
                var mode = GetArgValue(args, "--mode", "-m") ?? "flash";
                var debugMode = args.Contains("--debug") || args.Contains("-d");
                var testMode = args.Contains("--test") || args.Contains("-t");
                var licenseKeyArg = GetArgValue(args, "--key", "-k");
                var comPortArg = GetArgValue(args, "--port", "-p");

                if (args.Contains("--help") || args.Contains("-h"))
                {
                    ShowHelp();
                    return;
                }

                // Check if mode is allowed in current build
                if (!Configuration.IsModeAllowed(mode))
                {
                    Console.ForegroundColor = ConsoleColor.Red;
                    Console.WriteLine($"❌ Mode '{mode}' is not available in {Configuration.BuildMode} build");
                    Console.ResetColor();
                    Configuration.ShowAllowedModes();
                    return;
                }

                ShowHeader();

                if (debugMode)
                {
                    Console.ForegroundColor = ConsoleColor.Yellow;
                    Console.WriteLine("🐛 DEBUG MODE ENABLED");
                    Console.WriteLine("═══════════════════════");
                    Console.ResetColor();
                }

                var hardwareId = HardwareIdentification.GenerateHardwareId();

                if (debugMode)
                {
                    Console.WriteLine($"🔍 Debug: Hardware ID = {hardwareId}");
                    Console.WriteLine($"🔍 Debug: License Key Arg = {licenseKeyArg ?? "None"}");
                    Console.WriteLine($"🔍 Debug: COM Port Arg = {comPortArg ?? "None"}");
                    Console.WriteLine($"🔍 Debug: Mode = {mode}");
                    Console.WriteLine();
                }

                // Execute based on mode
                switch (mode.ToLower())
                {
                    case "flash":
                        var flashMode = new FlashMode();
                        await flashMode.ExecuteAsync(licenseKeyArg, comPortArg, debugMode);
                        break;
                    case "debug":
                        var debugMode_instance = new DebugMode();
                        await debugMode_instance.ExecuteAsync(licenseKeyArg, comPortArg, debugMode);
                        break;
                    case "monitor":
                        Console.WriteLine("Monitor mode not yet implemented in refactored version");
                        break;
                    case "wireshark":
                        Console.WriteLine("Wireshark mode not yet implemented in refactored version");
                        break;
                    default:
                        Console.ForegroundColor = ConsoleColor.Red;
                        Console.WriteLine($"❌ Unknown mode: {mode}");
                        Console.ResetColor();
                        Configuration.ShowAllowedModes();
                        return;
                }

                Console.WriteLine("✅ Operation completed successfully!");
            }
            catch (Exception ex)
            {
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine($"❌ {ex.Message}");
                if (args.Contains("--debug"))
                {
                    Console.WriteLine($"🔍 Stack Trace: {ex.StackTrace}");
                }
                Console.ResetColor();
            }
            finally
            {
                // Clear sensitive data from memory
                LicenseService.ClearSensitiveData();
                Console.WriteLine();
                Console.WriteLine("Press any key to exit...");
                Console.ReadKey();
            }
        }

        /// <summary>
        /// Show application header
        /// </summary>
        static void ShowHeader()
        {
            Console.Clear();
            Console.ForegroundColor = ConsoleColor.Cyan;
            Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
            Console.WriteLine("║                🔥 OCTANE ESP32 FLASHER 🔥                   ║");
            Console.WriteLine("║                      by Octane Team                         ║");
            Console.WriteLine($"║                     Version {Configuration.Version}                           ║");
            Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
            Console.ResetColor();
            Console.WriteLine();
        }

        /// <summary>
        /// Show help information
        /// </summary>
        static void ShowHelp()
        {
            Console.WriteLine("🔥 OCTANE ESP32 FLASHER - Command Line Options");
            Console.WriteLine("═══════════════════════════════════════════════");
            Console.WriteLine();
            Console.WriteLine("Usage: OctaneFlasher.exe [options]");
            Console.WriteLine();
            Console.WriteLine("Options:");
            Console.WriteLine("  -h, --help           Show this help message");
            Console.WriteLine("  -m, --mode <mode>    Operation mode (flash, debug, monitor, wireshark)");
            Console.WriteLine("  -d, --debug          Enable debug mode with verbose output");
            Console.WriteLine("  -t, --test           Test mode (skip actual flashing)");
            Console.WriteLine("  -k, --key <key>      License key (e.g., UFH-9Q8-1B3)");
            Console.WriteLine("  -p, --port <port>    COM port (e.g., COM10)");
            Console.WriteLine();
            Console.WriteLine("Modes:");
            Console.WriteLine("  flash                Flash firmware to ESP32 (default)");
            Console.WriteLine("  debug                Debug mode with detailed ESP32 communication");
            Console.WriteLine("  monitor              Monitor ESP32 serial output in real-time");
            Console.WriteLine("  wireshark            Wireshark-like packet capture and analysis");
            Console.WriteLine();
            Console.WriteLine("Examples:");
            Console.WriteLine("  OctaneFlasher.exe --key UFH-9Q8-1B3 --mode flash --port COM10");
            Console.WriteLine("  OctaneFlasher.exe --key UFH-9Q8-1B3 --mode debug --port COM10");
            Console.WriteLine("  OctaneFlasher.exe --key UFH-9Q8-1B3 --mode monitor --port COM10");
            Console.WriteLine("  OctaneFlasher.exe --key UFH-9Q8-1B3 --mode wireshark --port COM10");
            Console.WriteLine();
        }

        /// <summary>
        /// Get command line argument value
        /// </summary>
        static string? GetArgValue(string[] args, string longName, string shortName)
        {
            for (int i = 0; i < args.Length - 1; i++)
            {
                if (args[i] == longName || args[i] == shortName)
                {
                    return args[i + 1];
                }
            }
            return null;
        }
    }
}
