// Search functionality
let currentSearchResults = null;

// Initialize search page
document.addEventListener('DOMContentLoaded', function() {
    // Add enter key support for search
    document.getElementById('searchQuery').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch();
        }
    });
    
    // Auto-focus search input
    document.getElementById('searchQuery').focus();
});

// Perform search
async function performSearch() {
    const query = document.getElementById('searchQuery').value.trim();
    const type = document.getElementById('searchType').value;
    
    if (!query) {
        alert('Please enter a search query');
        return;
    }
    
    showLoading(true);
    hideResults();
    
    try {
        const response = await fetch(`/api/search?query=${encodeURIComponent(query)}&type=${type}`);
        const data = await response.json();
        
        if (data.success) {
            currentSearchResults = data.results;
            displaySearchResults(data.results);
        } else {
            showError('Search failed: ' + data.message);
        }
    } catch (error) {
        console.error('Search error:', error);
        showError('Search failed. Please try again.');
    } finally {
        showLoading(false);
    }
}

// Display search results
function displaySearchResults(results) {
    const { licenses, securityEvents, summary } = results;
    
    if (summary.totalResults === 0) {
        showNoResults();
        return;
    }
    
    // Update summary
    document.getElementById('resultsSummary').textContent = 
        `Found ${summary.totalResults} results for "${summary.searchQuery}"`;
    
    // Display licenses
    displayLicenses(licenses);
    
    // Display security events
    displaySecurityEvents(securityEvents);
    
    // Display overview
    displayOverview(results);
    
    // Show results container
    document.getElementById('searchResults').style.display = 'block';
}

// Display licenses
function displayLicenses(licenses) {
    const container = document.getElementById('licensesResults');
    
    if (licenses.length === 0) {
        container.innerHTML = '<p class="no-data">No licenses found</p>';
        return;
    }
    
    container.innerHTML = licenses.map(license => {
        const isExpired = new Date(license.expires_at) < new Date();
        const statusBadge = isExpired ? 'badge-expired' : 'badge-active';
        const statusText = isExpired ? 'Expired' : 'Active';
        
        return `
            <div class="result-item">
                <div class="result-header">
                    <div class="result-title">🔑 ${license.license_key}</div>
                    <div class="result-badge ${statusBadge}">${statusText}</div>
                </div>
                <div class="result-details">
                    <div class="detail-item">
                        <div class="detail-label">Hardware ID</div>
                        <div class="detail-value">${license.hardware_id || 'Not bound'}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Last Used</div>
                        <div class="detail-value">${license.last_used ? formatDate(license.last_used) : 'Never'}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Usage Count</div>
                        <div class="detail-value">${license.usage_count || 0}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">User IP</div>
                        <div class="detail-value">${license.user_ip || 'Unknown'}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Created</div>
                        <div class="detail-value">${formatDate(license.created_at)}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Expires</div>
                        <div class="detail-value">${formatDate(license.expires_at)}</div>
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

// Display security events
function displaySecurityEvents(events) {
    const container = document.getElementById('securityResults');
    
    if (events.length === 0) {
        container.innerHTML = '<p class="no-data">No security events found</p>';
        return;
    }
    
    container.innerHTML = events.map(event => {
        const severityBadge = getSeverityBadge(event.severity);
        
        return `
            <div class="result-item">
                <div class="result-header">
                    <div class="result-title">🛡️ ${event.event_type}</div>
                    <div class="result-badge ${severityBadge}">${event.severity}</div>
                </div>
                <div class="result-details">
                    <div class="detail-item">
                        <div class="detail-label">Description</div>
                        <div class="detail-value">${event.description}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">IP Address</div>
                        <div class="detail-value">${event.ip_address || 'Unknown'}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">License Key</div>
                        <div class="detail-value">${event.license_key || 'N/A'}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Hardware ID</div>
                        <div class="detail-value">${event.hardware_id || 'N/A'}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Timestamp</div>
                        <div class="detail-value">${formatDate(event.timestamp)}</div>
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

// Display overview
function displayOverview(results) {
    const container = document.getElementById('overviewResults');
    const { licenses, securityEvents, summary } = results;
    
    const activeLicenses = licenses.filter(l => new Date(l.expires_at) >= new Date()).length;
    const expiredLicenses = licenses.length - activeLicenses;
    const boundLicenses = licenses.filter(l => l.hardware_id).length;
    const criticalEvents = securityEvents.filter(e => e.severity === 'critical').length;
    
    container.innerHTML = `
        <div class="overview-card">
            <h5>📋 Total Licenses</h5>
            <div class="overview-number">${licenses.length}</div>
            <p>Found in search</p>
        </div>
        <div class="overview-card">
            <h5>✅ Active Licenses</h5>
            <div class="overview-number">${activeLicenses}</div>
            <p>Currently valid</p>
        </div>
        <div class="overview-card">
            <h5>❌ Expired Licenses</h5>
            <div class="overview-number">${expiredLicenses}</div>
            <p>Need renewal</p>
        </div>
        <div class="overview-card">
            <h5>🔗 Bound Licenses</h5>
            <div class="overview-number">${boundLicenses}</div>
            <p>Hardware bound</p>
        </div>
        <div class="overview-card">
            <h5>🛡️ Security Events</h5>
            <div class="overview-number">${securityEvents.length}</div>
            <p>Total events</p>
        </div>
        <div class="overview-card">
            <h5>🚨 Critical Events</h5>
            <div class="overview-number">${criticalEvents}</div>
            <p>Require attention</p>
        </div>
    `;
}

// Show/hide tabs
function showTab(tabName) {
    // Hide all tabs
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // Remove active class from all buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // Show selected tab
    document.getElementById(tabName + 'Tab').classList.add('active');
    
    // Add active class to clicked button
    event.target.classList.add('active');
}

// Utility functions
function getSeverityBadge(severity) {
    switch (severity) {
        case 'critical': return 'badge-critical';
        case 'high': return 'badge-warning';
        case 'medium': return 'badge-warning';
        case 'low': return 'badge-active';
        default: return 'badge-active';
    }
}

function formatDate(dateString) {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleString();
}

function showLoading(show) {
    document.getElementById('loadingIndicator').style.display = show ? 'block' : 'none';
}

function hideResults() {
    document.getElementById('searchResults').style.display = 'none';
    document.getElementById('noResults').style.display = 'none';
}

function showNoResults() {
    document.getElementById('noResults').style.display = 'block';
}

function showError(message) {
    alert('Error: ' + message);
}
