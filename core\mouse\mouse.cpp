
#include "mouse.hpp"

void m_mouse::move(vec2_t data)
{
	INPUT input{};

	input.type = INPUT_MOUSE;
	input.mi.mouseData = 0;
	input.mi.time = 0;
	input.mi.dx = data.x;
	input.mi.dy = data.y;
	input.mi.dwFlags = MOUSEEVENTF_MOVE;

	SendInput(1, &input, sizeof(input));
}

void m_mouse::accurate_sleep(std::uint32_t milliseconds)
{
	LONGLONG TimerResolution;
	LONGLONG WantedTime;
	LONGLONG CurrentTime;

	QueryPerformanceFrequency((LARGE_INTEGER*)&TimerResolution);
	TimerResolution /= 1000;

	QueryPerformanceCounter((LARGE_INTEGER*)&CurrentTime);
	WantedTime = CurrentTime / TimerResolution + milliseconds;
	CurrentTime = 0;
	while (CurrentTime < WantedTime)
	{
		QueryPerformanceCounter((LARGE_INTEGER*)&CurrentTime);
		CurrentTime /= TimerResolution;
	}
}

void m_mouse::interpolate(vec2_t angles, float animation, int delay)
{
	int old_x = 0, old_y = 0, old_t = 0;

	for (int i = 1; i <= (int)animation; ++i)
	{
		int new_x = i * angles.x / (int)animation;
		int new_y = i * angles.y / (int)animation;

		int new_time = i * (int)animation / (int)animation;

		move(vec2_t(static_cast<float>(new_x - old_x), static_cast<float>(new_y - old_y)));

		accurate_sleep(new_time - old_t);

		old_x = new_x; old_y = new_y; old_t = new_time;
	}

	accurate_sleep((int)delay - (int)animation);
}
