/*
 * Command Types and Structures
 * Defines all command types, structures, and enums used throughout the firmware
 */

#ifndef COMMAND_TYPES_H
#define COMMAND_TYPES_H

#include <Arduino.h>

// ===== ENHANCED COMMAND TYPES =====
enum CommandType {
    CMD_MOUSE_MOVE = 0x01,
    CMD_RECOIL_SMOOTH = 0x02,
    CMD_CLICK_DOWN = 0x03,
    CMD_CLICK_UP = 0x04,
    CMD_STATUS = 0x05,
    CMD_PING = 0x06,
    CMD_IDENTIFY = 0x07,
    CMD_SECURITY_HANDSHAKE = 0x08,
    CMD_PATTERN_EXECUTE = 0x09,
    CMD_STEALTH_MODE = 0x0A,
    CMD_TEMPLATE_RECOIL = 0x0B,
    CMD_ZERO_DELAY_MOVE = 0x0C,
    CMD_SYSTEM_INFO = 0x0D,
    CMD_RESET_SECURITY = 0x0E,
    C<PERSON>_DEEP_STEALTH = 0x0F
};

// ===== LED STATE DEFINITIONS =====
enum LEDState {
    LED_BOOT = 0,
    LED_INITIALIZING = 1,
    LED_WAITING = 2,
    LED_CONNECTED = 3,
    LED_ACTIVE = 4,
    LED_STEALTH = 5,
    LED_ERROR = 6,
    LED_SECURITY_ALERT = 7,
    LED_TEMPLATE_ACTIVE = 8,
    LED_ZERO_DELAY = 9
};

// ===== SECURITY STATES =====
enum SecurityState {
    SECURITY_NORMAL = 0,
    SECURITY_STEALTH = 1,
    SECURITY_DEEP_STEALTH = 2,
    SECURITY_ALERT = 3,
    SECURITY_LOCKDOWN = 4
};

// ===== RECOIL ENGINE STATES =====
enum RecoilState {
    RECOIL_IDLE = 0,
    RECOIL_ACTIVE = 1,
    RECOIL_TEMPLATE_MODE = 2,
    RECOIL_ZERO_DELAY = 3,
    RECOIL_INTERPOLATING = 4
};

// ===== COMMAND STRUCTURE =====
struct Command {
    CommandType type;
    int16_t x;
    int16_t y;
    uint8_t button;
    uint16_t steps;
    uint16_t delay;
    uint32_t timestamp;
    uint8_t security_token;
    bool processed;
    uint8_t priority;
    uint32_t sequence_id;
};

// ===== RECOIL PATTERN STRUCTURE =====
struct RecoilPattern {
    float x;
    float y;
    uint16_t delay_ms;
    uint8_t interpolation_steps;
};

// ===== DEVICE STATUS STRUCTURE =====
struct DeviceStatus {
    bool usb_connected;
    bool serial_connected;
    bool security_active;
    bool stealth_mode;
    uint32_t uptime_ms;
    uint32_t free_heap;
    uint16_t queue_size;
    uint16_t commands_processed;
    SecurityState security_state;
    RecoilState recoil_state;
    LEDState led_state;
};

// ===== SECURITY CONFIGURATION STRUCTURE =====
struct SecurityConfig {
    bool anti_detection_enabled;
    bool stealth_mode_enabled;
    bool deep_stealth_enabled;
    uint16_t detection_sensitivity;
    uint32_t stealth_interval_ms;
    uint8_t obfuscation_level;
    bool audit_enabled;
};

// ===== RECOIL CONFIGURATION STRUCTURE =====
struct RecoilConfig {
    bool template_mode_enabled;
    bool zero_delay_enabled;
    float sensitivity_multiplier;
    float ads_multiplier;
    uint16_t field_of_view;
    uint8_t interpolation_quality;
    bool sub_pixel_precision;
};

// ===== HARDWARE CONFIGURATION STRUCTURE =====
struct HardwareConfig {
    uint8_t led_pin;
    uint32_t serial_baud;
    bool usb_hid_enabled;
    bool led_status_enabled;
    uint16_t cpu_frequency_mhz;
    bool power_save_enabled;
};

// ===== SYSTEM METRICS STRUCTURE =====
struct SystemMetrics {
    uint32_t boot_time_ms;
    uint32_t total_commands;
    uint32_t successful_commands;
    uint32_t failed_commands;
    uint32_t security_events;
    uint32_t recoil_patterns_executed;
    uint32_t mouse_movements;
    float average_response_time_us;
};

// ===== TEMPLATE TIMING STRUCTURE =====
struct TemplateTiming {
    uint32_t shot_delay_ms;        // Template: 133ms for assault rifle
    uint32_t interpolation_steps;  // Template: 100 steps
    uint32_t animation_time_ms;    // Template: animation duration
    bool high_precision_timing;    // Template: QueryPerformanceCounter equivalent
    bool busy_wait_enabled;        // Template: accurate_sleep busy wait
};

// ===== AUTO-CALIBRATION DATA STRUCTURE =====
struct CalibrationData {
    // Timing calibration - Perfect positioning compensation
    float timing_offset_us;           // Microsecond timing offset compensation
    float delay_compensation_factor;  // Delay compensation multiplier for perfect timing
    float interpolation_accuracy;     // Interpolation accuracy percentage (target: 99.9%)

    // Movement calibration - Absolute best position possible
    float mouse_sensitivity_x;        // X-axis sensitivity calibration factor
    float mouse_sensitivity_y;        // Y-axis sensitivity calibration factor
    float pixel_accuracy;             // Pixel positioning accuracy (sub-pixel precision)
    float movement_drift_x;           // X-axis movement drift compensation
    float movement_drift_y;           // Y-axis movement drift compensation

    // System calibration - Hardware-specific optimizations
    float cpu_frequency_factor;       // CPU frequency compensation for timing
    float usb_latency_us;            // USB HID latency compensation in microseconds
    float serial_latency_us;         // Serial communication latency compensation
    float interrupt_latency_us;      // Interrupt handling latency compensation

    // Template precision calibration
    float template_timing_accuracy;   // Template timing system accuracy (target: 99.99%)
    float shot_delay_precision;       // Shot delay precision calibration (133ms target)
    float interpolation_precision;    // Interpolation step precision calibration

    // Auto-calibration metadata
    unsigned long calibration_timestamp;  // When calibration was performed
    uint16_t calibration_version;         // Calibration algorithm version
    bool calibration_valid;               // Whether calibration data is valid
    uint32_t calibration_checksum;        // Data integrity checksum
    uint16_t calibration_iterations;      // Number of calibration iterations performed
    float calibration_confidence;         // Confidence level of calibration (0.0-1.0)
};

// ===== CALIBRATION TEST RESULTS =====
struct CalibrationResults {
    // Timing test results
    float average_timing_error_us;    // Average timing error in microseconds
    float max_timing_error_us;        // Maximum timing error detected
    float timing_stability;           // Timing stability percentage

    // Movement test results
    float average_position_error;     // Average positioning error in pixels
    float max_position_error;         // Maximum positioning error detected
    float movement_consistency;       // Movement consistency percentage

    // Template precision results
    float template_accuracy;          // Template system accuracy percentage
    float shot_timing_precision;      // Shot timing precision percentage
    float interpolation_quality;      // Interpolation quality percentage

    // Overall calibration score
    float overall_score;              // Overall calibration quality (0.0-100.0)
    bool calibration_passed;          // Whether calibration meets quality standards
};

#endif // COMMAND_TYPES_H
