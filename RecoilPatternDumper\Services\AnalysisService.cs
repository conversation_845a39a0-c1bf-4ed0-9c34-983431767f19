using RecoilPatternDumper.Models;
using MathNet.Numerics.Statistics;

namespace RecoilPatternDumper.Services
{
    public class AnalysisService
    {
        public Dictionary<string, WeaponStatistics> AnalyzeMultipleRuns(List<DumpRunResult> results)
        {
            var weaponStats = new Dictionary<string, WeaponStatistics>();
            
            // Group successful runs by weapon name
            var successfulRuns = results.Where(r => r.Success && r.Data != null).ToList();
            
            if (!successfulRuns.Any())
                return weaponStats;

            // Get all unique weapon names
            var allWeaponNames = successfulRuns
                .SelectMany(r => r.Data!.Weapons.Select(w => w.Name))
                .Distinct()
                .ToList();

            foreach (var weaponName in allWeaponNames)
            {
                var weaponDataAcrossRuns = successfulRuns
                    .Select(r => r.Data!.Weapons.FirstOrDefault(w => w.Name == weaponName))
                    .Where(w => w != null)
                    .ToList();

                if (weaponDataAcrossRuns.Any())
                {
                    weaponStats[weaponName] = StatisticalAnalyzer.AnalyzeWeapon(weaponName, weaponDataAcrossRuns!);
                }
            }

            return weaponStats;
        }

        public List<OptimizedRecoilPattern> GenerateOptimizedPatterns(Dictionary<string, WeaponStatistics> weaponStats)
        {
            var optimizedPatterns = new List<OptimizedRecoilPattern>();

            foreach (var kvp in weaponStats)
            {
                var weaponName = kvp.Key;
                var stats = kvp.Value;

                var pattern = new OptimizedRecoilPattern
                {
                    WeaponName = weaponName,
                    OptimalPitchPattern = new List<double>(stats.OptimalPitchCurve),
                    OptimalYawPattern = new List<double>(stats.OptimalYawCurve),
                    ConfidenceScore = stats.QualityScore,
                    BasedOnRuns = stats.TotalRuns,
                    GeneratedAt = DateTime.Now,
                    OptimalProperties = new RecoilProperties
                    {
                        YawMax = stats.RecoilStats.YawMaxMean,
                        YawMin = stats.RecoilStats.YawMinMean,
                        PitchMax = stats.RecoilStats.PitchMaxMean,
                        PitchMin = stats.RecoilStats.PitchMinMean,
                        TimeMin = stats.RecoilStats.TimeMinMean,
                        TimeMax = stats.RecoilStats.TimeMaxMean,
                        AdsScale = stats.RecoilStats.AdsScaleMean,
                        MaxRadius = stats.RecoilStats.MaxRadiusMean
                    }
                };

                optimizedPatterns.Add(pattern);
            }

            return optimizedPatterns.OrderByDescending(p => p.ConfidenceScore).ToList();
        }

        public string GenerateDetailedReport(Dictionary<string, WeaponStatistics> weaponStats, List<DumpRunResult> allResults)
        {
            var report = new System.Text.StringBuilder();
            
            report.AppendLine("=== RECOIL PATTERN ANALYSIS REPORT ===");
            report.AppendLine($"Generated: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine($"Total Runs: {allResults.Count}");
            report.AppendLine($"Successful Runs: {allResults.Count(r => r.Success)}");
            report.AppendLine($"Failed Runs: {allResults.Count(r => !r.Success)}");
            report.AppendLine();

            // Execution time statistics
            var successfulRuns = allResults.Where(r => r.Success).ToList();
            if (successfulRuns.Any())
            {
                var executionTimes = successfulRuns.Select(r => r.ExecutionTime.TotalSeconds).ToArray();
                report.AppendLine("=== EXECUTION TIME STATISTICS ===");
                report.AppendLine($"Average Execution Time: {executionTimes.Mean():F2} seconds");
                report.AppendLine($"Min Execution Time: {executionTimes.Min():F2} seconds");
                report.AppendLine($"Max Execution Time: {executionTimes.Max():F2} seconds");
                report.AppendLine($"Standard Deviation: {executionTimes.StandardDeviation():F2} seconds");
                report.AppendLine();
            }

            // Weapon statistics
            report.AppendLine("=== WEAPON ANALYSIS ===");
            foreach (var kvp in weaponStats.OrderByDescending(w => w.Value.QualityScore))
            {
                var weaponName = kvp.Key;
                var stats = kvp.Value;

                report.AppendLine($"\n--- {weaponName} ---");
                report.AppendLine($"Quality Score: {stats.QualityScore:F2}/100");
                report.AppendLine($"Based on {stats.TotalRuns} successful runs");
                
                report.AppendLine("\nRecoil Properties:");
                report.AppendLine($"  Yaw Max: {stats.RecoilStats.YawMaxMean:F4} ± {stats.RecoilStats.YawMaxStdDev:F4}");
                report.AppendLine($"  Yaw Min: {stats.RecoilStats.YawMinMean:F4} ± {stats.RecoilStats.YawMinStdDev:F4}");
                report.AppendLine($"  Pitch Max: {stats.RecoilStats.PitchMaxMean:F4} ± {stats.RecoilStats.PitchMaxStdDev:F4}");
                report.AppendLine($"  Pitch Min: {stats.RecoilStats.PitchMinMean:F4} ± {stats.RecoilStats.PitchMinStdDev:F4}");
                report.AppendLine($"  Time Min: {stats.RecoilStats.TimeMinMean:F2} ± {stats.RecoilStats.TimeMinStdDev:F2} ms");
                report.AppendLine($"  Time Max: {stats.RecoilStats.TimeMaxMean:F2} ± {stats.RecoilStats.TimeMaxStdDev:F2} ms");
                report.AppendLine($"  ADS Scale: {stats.RecoilStats.AdsScaleMean:F4} ± {stats.RecoilStats.AdsScaleStdDev:F4}");
                report.AppendLine($"  Max Radius: {stats.RecoilStats.MaxRadiusMean:F4} ± {stats.RecoilStats.MaxRadiusStdDev:F4}");

                report.AppendLine("\nOther Properties:");
                report.AppendLine($"  Repeat Delay: {stats.RepeatDelayMean:F2} ± {stats.RepeatDelayStdDev:F2} ms");
                report.AppendLine($"  Move Penalty: {stats.MovePenaltyMean:F4} ± {stats.MovePenaltyStdDev:F4}");

                // Pattern consistency analysis
                if (stats.OptimalPitchCurve.Any())
                {
                    var avgPitchStdDev = stats.PitchCurveStdDev.Average();
                    var avgYawStdDev = stats.YawCurveStdDev.Average();
                    
                    report.AppendLine("\nPattern Consistency:");
                    report.AppendLine($"  Average Pitch Variation: {avgPitchStdDev:F4}");
                    report.AppendLine($"  Average Yaw Variation: {avgYawStdDev:F4}");
                    report.AppendLine($"  Pattern Length: {stats.OptimalPitchCurve.Count} shots");
                }
            }

            // Failed runs analysis
            var failedRuns = allResults.Where(r => !r.Success).ToList();
            if (failedRuns.Any())
            {
                report.AppendLine("\n=== FAILED RUNS ANALYSIS ===");
                var errorGroups = failedRuns.GroupBy(r => r.ErrorMessage).ToList();
                
                foreach (var errorGroup in errorGroups)
                {
                    report.AppendLine($"Error: {errorGroup.Key}");
                    report.AppendLine($"Occurrences: {errorGroup.Count()}");
                    report.AppendLine($"Run Numbers: {string.Join(", ", errorGroup.Select(r => r.RunNumber))}");
                    report.AppendLine();
                }
            }

            return report.ToString();
        }

        public Dictionary<string, object> GenerateComparisonMetrics(Dictionary<string, WeaponStatistics> weaponStats)
        {
            var metrics = new Dictionary<string, object>();

            if (!weaponStats.Any())
                return metrics;

            // Overall quality metrics
            var qualityScores = weaponStats.Values.Select(w => w.QualityScore).ToArray();
            metrics["OverallQuality"] = new
            {
                Average = qualityScores.Mean(),
                Best = qualityScores.Max(),
                Worst = qualityScores.Min(),
                StandardDeviation = qualityScores.StandardDeviation()
            };

            // Most consistent weapons
            var topWeapons = weaponStats
                .OrderByDescending(w => w.Value.QualityScore)
                .Take(5)
                .Select(w => new { Name = w.Key, Score = w.Value.QualityScore })
                .ToList();
            metrics["TopWeapons"] = topWeapons;

            // Recoil intensity comparison
            var recoilIntensities = weaponStats.ToDictionary(
                w => w.Key,
                w => Math.Sqrt(Math.Pow(w.Value.RecoilStats.PitchMaxMean, 2) + Math.Pow(w.Value.RecoilStats.YawMaxMean, 2))
            );
            metrics["RecoilIntensities"] = recoilIntensities.OrderByDescending(r => r.Value).ToList();

            return metrics;
        }
    }
}
