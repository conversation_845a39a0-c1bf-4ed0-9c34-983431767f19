#!/usr/bin/env python3
"""
ESP32-S2 Auto-Calibration Test Script
Tests the auto-calibration functionality for perfect positioning
"""

import serial
import json
import time
import sys
import statistics
from typing import Optional, List, Dict

class CalibrationTester:
    def __init__(self, port: str = None, baud: int = 115200):
        self.port = port
        self.baud = baud
        self.serial_conn: Optional[serial.Serial] = None
        self.connected = False
        
    def find_esp32_port(self) -> Optional[str]:
        """Auto-detect ESP32 device"""
        import serial.tools.list_ports
        
        print("Scanning for ESP32 devices...")
        ports = serial.tools.list_ports.comports()
        
        for port in ports:
            try:
                print(f"Testing port {port.device}...")
                test_serial = serial.Serial(port.device, self.baud, timeout=2)
                time.sleep(0.2)
                
                test_serial.flushInput()
                test_serial.flushOutput()
                
                test_serial.write(b"identify\n")
                time.sleep(0.5)
                
                response = ""
                start_time = time.time()
                while time.time() - start_time < 2:
                    if test_serial.in_waiting > 0:
                        response += test_serial.read(test_serial.in_waiting).decode('utf-8', errors='ignore')
                    time.sleep(0.1)
                
                test_serial.close()
                
                if "OCTANE_ESP32_ID:" in response or "ESP32" in response:
                    print(f"✅ Found ESP32 on {port.device}")
                    return port.device
                    
            except Exception as e:
                continue
                
        return None
    
    def connect(self) -> bool:
        """Connect to ESP32 device"""
        if not self.port:
            self.port = self.find_esp32_port()
            if not self.port:
                print("ERROR: No ESP32 device found!")
                return False
        
        try:
            self.serial_conn = serial.Serial(self.port, self.baud, timeout=3)
            time.sleep(1)
            self.connected = True
            print(f"Connected to ESP32 on {self.port}")
            return True
        except Exception as e:
            print(f"ERROR: Failed to connect to {self.port}: {e}")
            return False
    
    def disconnect(self):
        """Disconnect from ESP32"""
        if self.serial_conn:
            self.serial_conn.close()
            self.connected = False
            print("Disconnected from ESP32")
    
    def send_command(self, command: str) -> str:
        """Send command and get response"""
        if not self.connected:
            return "ERROR: Not connected"
        
        try:
            self.serial_conn.write((command + "\n").encode())
            time.sleep(0.1)
            
            response = ""
            start_time = time.time()
            while time.time() - start_time < 2:
                if self.serial_conn.in_waiting > 0:
                    response += self.serial_conn.read(self.serial_conn.in_waiting).decode('utf-8', errors='ignore')
                time.sleep(0.05)
            
            return response.strip()
        except Exception as e:
            return f"ERROR: {e}"
    
    def trigger_calibration(self) -> bool:
        """Trigger auto-calibration on the ESP32"""
        print("\n🔧 Triggering auto-calibration...")
        
        # Send calibration command
        response = self.send_command("calibrate")
        print(f"Calibration response: {response}")
        
        # Wait for calibration to complete
        print("⏳ Waiting for calibration to complete...")
        time.sleep(10)  # Give time for calibration
        
        # Check calibration status
        status_response = self.send_command("calibration_status")
        print(f"Calibration status: {status_response}")
        
        return "calibration_complete" in status_response.lower() or "complete" in status_response.lower()
    
    def test_timing_accuracy(self) -> Dict[str, float]:
        """Test timing accuracy after calibration"""
        print("\n⏱️ Testing timing accuracy...")
        
        timing_results = []
        
        for i in range(20):
            start_time = time.time()
            
            # Send a movement command and measure response time
            response = self.send_command(f"MOUSE_MOVE {i%10-5},{i%8-4}")
            
            end_time = time.time()
            response_time = (end_time - start_time) * 1000  # Convert to milliseconds
            
            if "MOVE_EXECUTED" in response:
                timing_results.append(response_time)
            
            time.sleep(0.1)
        
        if timing_results:
            avg_time = statistics.mean(timing_results)
            min_time = min(timing_results)
            max_time = max(timing_results)
            std_dev = statistics.stdev(timing_results) if len(timing_results) > 1 else 0
            
            print(f"   Average response time: {avg_time:.2f} ms")
            print(f"   Min response time: {min_time:.2f} ms")
            print(f"   Max response time: {max_time:.2f} ms")
            print(f"   Standard deviation: {std_dev:.2f} ms")
            
            return {
                'average': avg_time,
                'min': min_time,
                'max': max_time,
                'std_dev': std_dev,
                'consistency': 100.0 - (std_dev / avg_time * 100.0) if avg_time > 0 else 0
            }
        
        return {}
    
    def test_recoil_precision(self) -> Dict[str, float]:
        """Test recoil precision after calibration"""
        print("\n🎯 Testing recoil precision...")
        
        recoil_results = []
        
        for i in range(10):
            x = (i % 20) - 10  # -10 to +9
            y = (i % 16) - 8   # -8 to +7
            steps = 10
            delay = 133  # Template delay
            
            start_time = time.time()
            
            response = self.send_command(f"RECOIL_SMOOTH {x},{y},{steps},{delay}")
            
            end_time = time.time()
            execution_time = (end_time - start_time) * 1000  # Convert to milliseconds
            
            if "RECOIL_EXECUTED" in response:
                recoil_results.append(execution_time)
                print(f"   Recoil {x},{y}: {execution_time:.2f} ms")
            
            time.sleep(0.2)
        
        if recoil_results:
            avg_time = statistics.mean(recoil_results)
            expected_time = 133 + 10  # Template delay + steps
            accuracy = 100.0 - (abs(avg_time - expected_time) / expected_time * 100.0)
            
            print(f"   Average execution time: {avg_time:.2f} ms")
            print(f"   Expected time: {expected_time} ms")
            print(f"   Timing accuracy: {accuracy:.2f}%")
            
            return {
                'average_time': avg_time,
                'expected_time': expected_time,
                'accuracy': accuracy
            }
        
        return {}
    
    def test_movement_consistency(self) -> Dict[str, float]:
        """Test movement consistency after calibration"""
        print("\n📍 Testing movement consistency...")
        
        # Test repeated identical movements
        movement_times = []
        
        for i in range(30):
            start_time = time.time()
            
            # Send identical movement
            response = self.send_command("MOUSE_MOVE 5,5")
            
            end_time = time.time()
            movement_time = (end_time - start_time) * 1000
            
            if "MOVE_EXECUTED" in response:
                movement_times.append(movement_time)
            
            time.sleep(0.05)
        
        if movement_times:
            avg_time = statistics.mean(movement_times)
            std_dev = statistics.stdev(movement_times) if len(movement_times) > 1 else 0
            consistency = 100.0 - (std_dev / avg_time * 100.0) if avg_time > 0 else 0
            
            print(f"   Average movement time: {avg_time:.2f} ms")
            print(f"   Standard deviation: {std_dev:.2f} ms")
            print(f"   Consistency: {consistency:.2f}%")
            
            return {
                'average_time': avg_time,
                'std_dev': std_dev,
                'consistency': consistency
            }
        
        return {}
    
    def get_calibration_data(self) -> Dict:
        """Get calibration data from ESP32"""
        print("\n📊 Retrieving calibration data...")
        
        response = self.send_command("get_calibration_data")
        print(f"Calibration data response: {response}")
        
        # Try to parse JSON response
        try:
            if "{" in response:
                json_start = response.find("{")
                json_data = response[json_start:]
                return json.loads(json_data)
        except json.JSONDecodeError:
            pass
        
        return {}
    
    def run_calibration_tests(self) -> bool:
        """Run complete calibration test suite"""
        if not self.connect():
            return False
        
        print("🔧 ESP32-S2 Auto-Calibration Test Suite")
        print("=" * 50)
        
        # Step 1: Trigger calibration
        calibration_success = self.trigger_calibration()
        if not calibration_success:
            print("❌ Calibration failed or timed out")
            self.disconnect()
            return False
        
        print("✅ Auto-calibration completed successfully")
        
        # Step 2: Test timing accuracy
        timing_results = self.test_timing_accuracy()
        
        # Step 3: Test recoil precision
        recoil_results = self.test_recoil_precision()
        
        # Step 4: Test movement consistency
        movement_results = self.test_movement_consistency()
        
        # Step 5: Get calibration data
        calibration_data = self.get_calibration_data()
        
        # Generate report
        print("\n📋 CALIBRATION TEST REPORT")
        print("=" * 50)
        
        if timing_results:
            print(f"⏱️ Timing Performance:")
            print(f"   Average Response: {timing_results.get('average', 0):.2f} ms")
            print(f"   Consistency: {timing_results.get('consistency', 0):.1f}%")
        
        if recoil_results:
            print(f"🎯 Recoil Performance:")
            print(f"   Timing Accuracy: {recoil_results.get('accuracy', 0):.1f}%")
        
        if movement_results:
            print(f"📍 Movement Performance:")
            print(f"   Consistency: {movement_results.get('consistency', 0):.1f}%")
        
        if calibration_data:
            print(f"📊 Calibration Data:")
            for key, value in calibration_data.items():
                if isinstance(value, float):
                    print(f"   {key}: {value:.4f}")
                else:
                    print(f"   {key}: {value}")
        
        # Overall assessment
        overall_score = 0
        if timing_results:
            overall_score += timing_results.get('consistency', 0) * 0.3
        if recoil_results:
            overall_score += recoil_results.get('accuracy', 0) * 0.4
        if movement_results:
            overall_score += movement_results.get('consistency', 0) * 0.3
        
        print(f"\n🏆 Overall Calibration Score: {overall_score:.1f}%")
        
        if overall_score >= 90:
            print("🎯 EXCELLENT: Absolute best positioning achieved!")
        elif overall_score >= 80:
            print("✅ GOOD: High-quality calibration completed")
        elif overall_score >= 70:
            print("⚠️ FAIR: Calibration acceptable but could be improved")
        else:
            print("❌ POOR: Calibration needs improvement")
        
        self.disconnect()
        return overall_score >= 70

def main():
    """Main test function"""
    print("ESP32-S2 Auto-Calibration Test")
    print("=" * 40)
    
    port = None
    if len(sys.argv) > 1:
        port = sys.argv[1]
        print(f"Using specified port: {port}")
    
    tester = CalibrationTester(port)
    success = tester.run_calibration_tests()
    
    if success:
        print("\n✅ Calibration tests completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Calibration tests failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
