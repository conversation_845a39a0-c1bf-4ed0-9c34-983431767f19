@echo off
echo.
echo OCTANE COMPLETE BUILD AND DEPLOYMENT
echo ===================================
echo.

set VPS_IP=*************
set VPS_USER=root
set LOCAL_PATH=%~dp0
set REMOTE_PATH=/opt/octane-auth
set SSH_KEY=%~dp0deploy\octane_key
set ESP32_PROJECT_PATH=C:\Users\<USER>\Desktop\recoil\esp32-hid-firmware\Custom farmer for ESP

echo Build and Deployment Details:
echo    VPS IP: %VPS_IP%
echo    Local Path: %LOCAL_PATH%
echo    Remote Path: %REMOTE_PATH%
echo    ESP32 Project: %ESP32_PROJECT_PATH%
echo.

set /p CONTINUE="Ready to build firmware and deploy? (y/N): "
if /i not "%CONTINUE%"=="y" (
    echo Deployment cancelled.
    pause
    exit /b
)

echo.
echo STEP 1: Building ESP32 Firmware...
echo ==================================

echo Building latest ESP32-S2 firmware...
cd /d "%ESP32_PROJECT_PATH%"
C:\Users\<USER>\.platformio\penv\Scripts\platformio.exe run --environment lolin_s2_mini

if %ERRORLEVEL% NEQ 0 (
    echo Firmware build failed!
    pause
    exit /b 1
)

echo Firmware built successfully!

echo.
echo STEP 2: Copying firmware files...
echo =================================

cd /d "%LOCAL_PATH%"
echo Copying firmware binaries...
copy "%ESP32_PROJECT_PATH%\.pio\build\lolin_s2_mini\firmware.bin" "firmware\octane_esp32s2_enhanced_latest.bin"
copy "%ESP32_PROJECT_PATH%\.pio\build\lolin_s2_mini\bootloader.bin" "firmware\bootloader_esp32s2_enhanced.bin"
copy "%ESP32_PROJECT_PATH%\.pio\build\lolin_s2_mini\partitions.bin" "firmware\partitions_esp32s2_enhanced.bin"

echo.
echo STEP 3: Cleaning up VPS...
echo =========================

echo Removing old backup directories from VPS...
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "cd /opt/octane-auth && rm -rf backup-modern-website original-website original-website-backup"

echo Cleaning up old firmware files...
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "cd /opt/octane-auth && rm -rf firmware/esp32s2_enhanced && mkdir -p firmware"

echo.
echo STEP 4: Uploading files to VPS...
echo =================================

echo Uploading application files...
scp -i "%SSH_KEY%" -r "models" %VPS_USER%@%VPS_IP%:%REMOTE_PATH%/
scp -i "%SSH_KEY%" -r "routes" %VPS_USER%@%VPS_IP%:%REMOTE_PATH%/
scp -i "%SSH_KEY%" -r "services" %VPS_USER%@%VPS_IP%:%REMOTE_PATH%/
scp -i "%SSH_KEY%" -r "middleware" %VPS_USER%@%VPS_IP%:%REMOTE_PATH%/
scp -i "%SSH_KEY%" -r "public" %VPS_USER%@%VPS_IP%:%REMOTE_PATH%/
scp -i "%SSH_KEY%" -r "scripts" %VPS_USER%@%VPS_IP%:%REMOTE_PATH%/
scp -i "%SSH_KEY%" -r "firmware" %VPS_USER%@%VPS_IP%:%REMOTE_PATH%/

echo Uploading main application files...
scp -i "%SSH_KEY%" "server.js" %VPS_USER%@%VPS_IP%:%REMOTE_PATH%/
scp -i "%SSH_KEY%" "discord-bot.js" %VPS_USER%@%VPS_IP%:%REMOTE_PATH%/
scp -i "%SSH_KEY%" "discord.js" %VPS_USER%@%VPS_IP%:%REMOTE_PATH%/
scp -i "%SSH_KEY%" "package.json" %VPS_USER%@%VPS_IP%:%REMOTE_PATH%/
scp -i "%SSH_KEY%" "ecosystem.config.js" %VPS_USER%@%VPS_IP%:%REMOTE_PATH%/
scp -i "%SSH_KEY%" ".env" %VPS_USER%@%VPS_IP%:%REMOTE_PATH%/
scp -i "%SSH_KEY%" "access-denied.html" %VPS_USER%@%VPS_IP%:%REMOTE_PATH%/

if %ERRORLEVEL% NEQ 0 (
    echo Upload failed!
    pause
    exit /b 1
)

echo Files uploaded successfully!

echo.
echo STEP 5: Setting permissions...
echo =============================

echo Setting proper file ownership and permissions...
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "chown -R octane:octane %REMOTE_PATH%"
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "chmod 600 %REMOTE_PATH%/.env"
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "chmod 755 %REMOTE_PATH%/*.js"
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "chmod 755 %REMOTE_PATH%/scripts/*.sh"
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "chmod 644 %REMOTE_PATH%/firmware/*.bin"

echo.
echo STEP 6: Installing dependencies...
echo ==================================

echo Installing Node.js dependencies...
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "cd %REMOTE_PATH% && npm install --production"

if %ERRORLEVEL% NEQ 0 (
    echo Dependency installation failed!
    pause
    exit /b 1
)

echo.
echo STEP 7: Restarting services...
echo ==============================

echo Restarting application services...
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "sudo -u octane pm2 restart octane-auth"
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "sudo -u octane pm2 restart discord-bot"
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "sudo -u octane pm2 save"

echo Reloading nginx...
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "systemctl reload nginx"

echo.
echo STEP 8: Verification...
echo ======================

echo Checking service status...
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "sudo -u octane pm2 status"

echo.
echo Testing API endpoint...
curl -s http://%VPS_IP%/api/health

echo.
echo Checking disk space...
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "df -h | grep vda1"

echo.
echo.
echo DEPLOYMENT COMPLETE!
echo ===================
echo.
echo Your system is now updated with:
echo    ✅ Latest ESP32-S2 firmware
echo    ✅ Organized file structure
echo    ✅ Updated application code
echo    ✅ Clean VPS environment
echo.
echo Access your system at:
echo    🌐 Web Interface: http://%VPS_IP%
echo    🔧 Admin Panel: http://%VPS_IP%/admin
echo    📊 API Health: http://%VPS_IP%/api/health
echo.

echo Opening admin panel...
start http://%VPS_IP%

pause
