/*
 * Recoil Engine Header
 * Handles template-based precision recoil control and mouse movement
 */

#ifndef RECOIL_ENGINE_H
#define RECOIL_ENGINE_H

#include <Arduino.h>
#include "../config/firmware_config.h"
#include "../config/command_types.h"

// Forward declaration
class HardwareManager;

class RecoilEngine {
private:
    // Template timing system
    TemplateTiming templateTiming;
    bool templateModeEnabled;
    bool zeroDelayEnabled;
    bool subPixelPrecisionEnabled;

    // Auto-calibration system
    CalibrationData calibrationData;
    bool autoCalibrationEnabled;
    bool calibrationComplete;
    unsigned long lastCalibrationTime;
    
    // Recoil configuration
    RecoilConfig recoilConfig;
    RecoilState currentState;
    
    // Template precision variables
    float accumulatorX;
    float accumulatorY;
    unsigned long lastExecutionTime;
    
    // Performance metrics
    uint32_t recoilPatternsExecuted;
    uint32_t mouseMovementsExecuted;
    uint32_t templateExecutions;
    float averageExecutionTime;
    
    // Hardware reference
    HardwareManager* hardware;
    
    // Template timing methods (exact copy from template)
    void templateTimerStart();
    void templateTimerEnd();
    uint32_t templateElapsedTimeMs();
    void templateAccurateSleep(int milliseconds);
    
    // Template interpolation methods (exact copy from template mouse.cpp)
    void templateInterpolate(int totalX, int totalY, int steps, int delayMs);
    void executeTemplateMovement(int deltaX, int deltaY);
    
    // Sub-pixel precision methods
    void addToAccumulator(float x, float y);
    void flushAccumulator();
    bool hasAccumulatedMovement();
    
    // Zero delay optimization
    void executeZeroDelayMovement(int x, int y);
    void executeDirectHIDMovement(int x, int y);
    
    // Validation methods
    bool validateRecoilParameters(int x, int y, int steps, int delay);
    bool isRecoilPatternValid(const RecoilPattern* pattern, int size);
    
public:
    RecoilEngine();
    
    // Initialization
    bool initialize();
    void setHardwareManager(HardwareManager* hw);
    
    // Template recoil execution (exact copy from template)
    void executeTemplateRecoil(int totalX, int totalY, int steps, int delayMs);
    void executeRecoilPattern(const RecoilPattern* pattern, int patternSize);
    void executeOptimizedRecoil(int x, int y, int steps, int delay);
    
    // Mouse movement methods
    void executeMouseMove(int x, int y);
    void executeMouseMoveOptimized(int x, int y);
    void executeSmoothMove(int x, int y, int steps, int delay);
    
    // Template precision methods
    void enableTemplateMode(bool enabled);
    void enableZeroDelay(bool enabled);
    void enableSubPixelPrecision(bool enabled);
    
    // Configuration methods
    void setRecoilConfig(const RecoilConfig& config);
    RecoilConfig getRecoilConfig() const;
    void setTemplateTiming(const TemplateTiming& timing);
    TemplateTiming getTemplateTiming() const;
    
    // State management
    void setRecoilState(RecoilState state);
    RecoilState getRecoilState() const;
    bool isRecoilActive() const;
    bool isTemplateMode() const;
    
    // Performance monitoring
    uint32_t getRecoilPatternsExecuted() const;
    uint32_t getMouseMovementsExecuted() const;
    uint32_t getTemplateExecutions() const;
    float getAverageExecutionTime() const;
    
    // Auto-calibration system
    void performAutoCalibration();
    void calibrateTimingSystem();
    void calibrateMouseMovement();
    void calibrateRecoilAccuracy();
    bool validateCalibration();

    // Calibration status and data access
    bool isCalibrationComplete() const { return calibrationComplete; }
    bool isAutoCalibrationEnabled() const { return autoCalibrationEnabled; }
    float getCalibrationConfidence() const { return calibrationData.calibration_confidence; }
    float getTimingAccuracy() const { return calibrationData.template_timing_accuracy; }
    float getPositionAccuracy() const { return calibrationData.pixel_accuracy; }
    String getCalibrationDataJSON() const;

    // Calibration and testing
    void calibrateRecoilSystem();
    void testTemplateAccuracy();
    void benchmarkPerformance();
    
    // Emergency methods
    void stopAllRecoil();
    void resetRecoilEngine();
    void emergencyStop();
    
    // Debug methods
    void printRecoilStatus();
    void printTemplateTimingInfo();
    void enableRecoilDebug(bool enabled);
};

#endif // RECOIL_ENGINE_H
