using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.IO.Ports;
using System.Linq;
using System.Management;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace OctaneFlasherEnhanced
{
    class Program
    {
        // ===== ENHANCED CONFIGURATION =====
        private static readonly string VPS_BASE_URL = "http://217.154.58.14";
        private static readonly string FIRMWARE_API_ENDPOINT = "/api/firmware";
        private static readonly string FIRMWARE_TYPE = "esp32s2_enhanced";
        private static readonly HttpClient httpClient = new HttpClient();
        
        // ===== SECURITY CONFIGURATION =====
        private static readonly byte[] FIRMWARE_DECRYPTION_KEY = Encoding.UTF8.GetBytes("OctaneRecoilSystem2024V2_SecureKey32");
        private static string hardwareId = "";
        private static string downloadToken = "";
        
        // ===== FIRMWARE STORAGE =====
        private static Dictionary<string, byte[]> firmwareFiles = new Dictionary<string, byte[]>();
        private static Dictionary<string, string> firmwareChecksums = new Dictionary<string, string>();

        static async Task Main(string[] args)
        {
            try
            {
                Console.Title = "Octane Enhanced Flasher v2.1.0 - Secure Firmware Distribution";
                
                ShowEnhancedHeader();
                
                // Step 1: Initialize security
                await InitializeSecurity();
                
                // Step 2: Authenticate with VPS
                await AuthenticateWithVPS();
                
                // Step 3: Download firmware securely
                await DownloadFirmwareSecurely();
                
                // Step 4: Detect ESP32 and flash
                await FlashESP32Enhanced();
                
                Console.WriteLine();
                Console.ForegroundColor = ConsoleColor.Green;
                Console.WriteLine("🎉 ESP32 Enhanced Firmware Flashing Completed Successfully!");
                Console.ResetColor();
                
            }
            catch (Exception ex)
            {
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine($"❌ Fatal Error: {ex.Message}");
                Console.ResetColor();
                
                // Send error to Discord webhook
                _ = SendErrorToWebhook("Enhanced Flasher Error", ex.Message, ex.StackTrace);
            }
            finally
            {
                // Clean up sensitive data from memory
                CleanupSensitiveData();
                
                Console.WriteLine("\nPress any key to exit...");
                Console.ReadKey();
            }
        }

        static void ShowEnhancedHeader()
        {
            Console.Clear();
            Console.ForegroundColor = ConsoleColor.Cyan;
            Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
            Console.WriteLine("║                    OCTANE ENHANCED FLASHER                  ║");
            Console.WriteLine("║                      Version 2.1.0                          ║");
            Console.WriteLine("║                  Secure Firmware Distribution               ║");
            Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
            Console.ResetColor();
            Console.WriteLine();
            Console.WriteLine("🔐 Enhanced Security Features:");
            Console.WriteLine("   • Secure firmware download from VPS");
            Console.WriteLine("   • In-memory firmware storage (no disk writes)");
            Console.WriteLine("   • Hardware-based authentication");
            Console.WriteLine("   • Encrypted firmware transmission");
            Console.WriteLine("   • Anti-reverse engineering protection");
            Console.WriteLine();
        }

        static async Task InitializeSecurity()
        {
            Console.WriteLine("🔐 Initializing Enhanced Security System...");
            
            // Generate hardware ID
            hardwareId = GenerateEnhancedHardwareId();
            Console.WriteLine($"   Hardware ID: {hardwareId.Substring(0, 8)}...");
            
            // Initialize HTTP client with security headers
            httpClient.DefaultRequestHeaders.Clear();
            httpClient.DefaultRequestHeaders.Add("User-Agent", "OctaneFlasherEnhanced/2.1.0");
            httpClient.DefaultRequestHeaders.Add("X-Client-Type", "Enhanced-Flasher");
            httpClient.DefaultRequestHeaders.Add("X-Security-Level", "ADVANCED");
            httpClient.Timeout = TimeSpan.FromMinutes(5);
            
            Console.WriteLine("✅ Security system initialized");
        }

        static string GenerateEnhancedHardwareId()
        {
            try
            {
                var entropy = new StringBuilder();
                
                // Collect multiple entropy sources
                entropy.Append(Environment.ProcessorCount);
                entropy.Append(Environment.MachineName);
                entropy.Append(Environment.UserName);
                entropy.Append(Environment.OSVersion.ToString());
                
                // Add hardware-specific data
                try
                {
                    using (var searcher = new ManagementObjectSearcher("SELECT ProcessorId FROM Win32_Processor"))
                    {
                        foreach (ManagementObject obj in searcher.Get())
                        {
                            entropy.Append(obj["ProcessorId"]?.ToString() ?? "");
                            break;
                        }
                    }
                    
                    using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BaseBoard"))
                    {
                        foreach (ManagementObject obj in searcher.Get())
                        {
                            entropy.Append(obj["SerialNumber"]?.ToString() ?? "");
                            break;
                        }
                    }
                }
                catch
                {
                    // Fallback if WMI fails
                    entropy.Append(Environment.TickCount);
                }
                
                // Generate SHA256 hash
                using (var sha256 = SHA256.Create())
                {
                    var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(entropy.ToString()));
                    return Convert.ToBase64String(hash).Substring(0, 16);
                }
            }
            catch
            {
                // Ultimate fallback
                return Convert.ToBase64String(Encoding.UTF8.GetBytes($"{Environment.MachineName}_{Environment.UserName}")).Substring(0, 16);
            }
        }

        static async Task AuthenticateWithVPS()
        {
            Console.WriteLine("🌐 Authenticating with Octane VPS...");
            
            try
            {
                // Request download token
                var tokenRequest = new
                {
                    hardware_id = hardwareId,
                    firmware_type = FIRMWARE_TYPE
                };
                
                var tokenJson = JsonSerializer.Serialize(tokenRequest);
                var tokenContent = new StringContent(tokenJson, Encoding.UTF8, "application/json");
                
                var tokenResponse = await httpClient.PostAsync($"{VPS_BASE_URL}{FIRMWARE_API_ENDPOINT}/token", tokenContent);
                
                if (!tokenResponse.IsSuccessStatusCode)
                {
                    throw new Exception($"Authentication failed: {tokenResponse.StatusCode}");
                }
                
                var tokenResponseText = await tokenResponse.Content.ReadAsStringAsync();
                var tokenData = JsonSerializer.Deserialize<JsonElement>(tokenResponseText);
                
                if (!tokenData.GetProperty("success").GetBoolean())
                {
                    throw new Exception("Failed to obtain download token");
                }
                
                downloadToken = tokenData.GetProperty("token").GetString();
                var expiresIn = tokenData.GetProperty("expires_in").GetInt32();
                
                Console.WriteLine($"✅ Authentication successful (expires in {expiresIn} seconds)");
                
            }
            catch (Exception ex)
            {
                throw new Exception($"VPS authentication failed: {ex.Message}");
            }
        }

        static async Task DownloadFirmwareSecurely()
        {
            Console.WriteLine("📥 Downloading Enhanced Firmware Securely...");
            
            try
            {
                // Get firmware metadata first
                var metadataResponse = await httpClient.GetAsync($"{VPS_BASE_URL}{FIRMWARE_API_ENDPOINT}/metadata/{FIRMWARE_TYPE}");
                
                if (!metadataResponse.IsSuccessStatusCode)
                {
                    throw new Exception($"Failed to get firmware metadata: {metadataResponse.StatusCode}");
                }
                
                var metadataText = await metadataResponse.Content.ReadAsStringAsync();
                var metadata = JsonSerializer.Deserialize<JsonElement>(metadataText);
                var firmwareMetadata = metadata.GetProperty("metadata");
                
                Console.WriteLine($"   Firmware Version: {firmwareMetadata.GetProperty("version").GetString()}");
                Console.WriteLine($"   Security Level: {firmwareMetadata.GetProperty("security_level").GetString()}");
                Console.WriteLine($"   Anti-Detection: {firmwareMetadata.GetProperty("anti_detection").GetBoolean()}");
                
                // Download each firmware file
                var fileTypes = new[] { "bootloader", "firmware", "partitions" };
                
                foreach (var fileType in fileTypes)
                {
                    Console.WriteLine($"   Downloading {fileType}...");
                    await DownloadFirmwareFile(fileType);
                }
                
                Console.WriteLine("✅ All firmware files downloaded and verified");
                
            }
            catch (Exception ex)
            {
                throw new Exception($"Secure firmware download failed: {ex.Message}");
            }
        }

        static async Task DownloadFirmwareFile(string fileType)
        {
            try
            {
                // Set authentication headers
                var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString();
                
                httpClient.DefaultRequestHeaders.Remove("hardware_id");
                httpClient.DefaultRequestHeaders.Remove("timestamp");
                httpClient.DefaultRequestHeaders.Remove("token");
                
                httpClient.DefaultRequestHeaders.Add("hardware_id", hardwareId);
                httpClient.DefaultRequestHeaders.Add("timestamp", timestamp);
                httpClient.DefaultRequestHeaders.Add("token", downloadToken);
                
                // Download encrypted firmware
                var downloadUrl = $"{VPS_BASE_URL}{FIRMWARE_API_ENDPOINT}/download/{FIRMWARE_TYPE}/{fileType}?encrypt=true";
                var response = await httpClient.GetAsync(downloadUrl);
                
                if (!response.IsSuccessStatusCode)
                {
                    throw new Exception($"Download failed for {fileType}: {response.StatusCode}");
                }
                
                var responseText = await response.Content.ReadAsStringAsync();
                var responseData = JsonSerializer.Deserialize<JsonElement>(responseText);
                
                if (!responseData.GetProperty("success").GetBoolean())
                {
                    throw new Exception($"Download response indicates failure for {fileType}");
                }
                
                // Decrypt firmware
                var encryptedData = responseData.GetProperty("data").GetString();
                var iv = responseData.GetProperty("iv").GetString();
                var checksum = responseData.GetProperty("checksum").GetString();
                
                var firmwareData = DecryptFirmware(encryptedData, iv);
                
                // Verify checksum
                var calculatedChecksum = CalculateChecksum(firmwareData);
                if (calculatedChecksum != checksum)
                {
                    throw new Exception($"Checksum verification failed for {fileType}");
                }
                
                // Store in memory
                firmwareFiles[fileType] = firmwareData;
                firmwareChecksums[fileType] = checksum;
                
                Console.WriteLine($"     ✅ {fileType} downloaded and verified ({firmwareData.Length} bytes)");
                
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to download {fileType}: {ex.Message}");
            }
        }

        static byte[] DecryptFirmware(string encryptedData, string ivHex)
        {
            try
            {
                var encrypted = Convert.FromBase64String(encryptedData);
                var iv = Convert.FromHexString(ivHex);
                
                using (var aes = Aes.Create())
                {
                    aes.Key = FIRMWARE_DECRYPTION_KEY;
                    aes.IV = iv;
                    aes.Mode = CipherMode.CBC;
                    aes.Padding = PaddingMode.PKCS7;
                    
                    using (var decryptor = aes.CreateDecryptor())
                    {
                        return decryptor.TransformFinalBlock(encrypted, 0, encrypted.Length);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Firmware decryption failed: {ex.Message}");
            }
        }

        static string CalculateChecksum(byte[] data)
        {
            using (var sha256 = SHA256.Create())
            {
                var hash = sha256.ComputeHash(data);
                return Convert.ToHexString(hash).ToLower();
            }
        }

        static async Task FlashESP32Enhanced()
        {
            Console.WriteLine("🔧 Starting Enhanced ESP32 Flashing Process...");
            
            // Detect ESP32
            string comPort = DetectESP32Enhanced();
            if (string.IsNullOrEmpty(comPort))
            {
                throw new Exception("No ESP32 device detected");
            }
            
            Console.WriteLine($"✅ ESP32 detected on {comPort}");
            
            // Flash firmware from memory
            await FlashFirmwareFromMemory(comPort);
            
            // Verify flashing
            await VerifyEnhancedFirmware(comPort);
        }

        static string DetectESP32Enhanced()
        {
            Console.WriteLine("🔍 Scanning for ESP32 devices...");
            
            var ports = SerialPort.GetPortNames();
            
            foreach (var port in ports)
            {
                try
                {
                    using (var searcher = new ManagementObjectSearcher($"SELECT * FROM Win32_PnPEntity WHERE Name LIKE '%{port}%'"))
                    {
                        foreach (ManagementObject obj in searcher.Get())
                        {
                            var description = obj["Description"]?.ToString() ?? "";
                            var name = obj["Name"]?.ToString() ?? "";
                            
                            // Enhanced ESP32 detection
                            if (IsESP32Device(description, name))
                            {
                                Console.WriteLine($"   Found ESP32: {port} - {description}");
                                return port;
                            }
                        }
                    }
                }
                catch
                {
                    // Continue searching
                }
            }
            
            return null;
        }

        static bool IsESP32Device(string description, string name)
        {
            var esp32Indicators = new[]
            {
                "ESP32", "ESP-32", "Silicon Labs", "CP210", "CH340", "USB Serial", "UART Bridge"
            };
            
            return esp32Indicators.Any(indicator =>
                description.Contains(indicator, StringComparison.OrdinalIgnoreCase) ||
                name.Contains(indicator, StringComparison.OrdinalIgnoreCase));
        }

        static async Task FlashFirmwareFromMemory(string comPort)
        {
            Console.WriteLine("⚡ Flashing Enhanced Firmware from Memory...");
            
            try
            {
                // Create temporary files for esptool (will be deleted immediately)
                var tempDir = Path.Combine(Path.GetTempPath(), "OctaneFlasherTemp_" + Guid.NewGuid().ToString("N")[..8]);
                Directory.CreateDirectory(tempDir);
                
                var tempFiles = new Dictionary<string, string>();
                
                try
                {
                    // Write firmware files to temp directory
                    foreach (var kvp in firmwareFiles)
                    {
                        var tempFile = Path.Combine(tempDir, $"{kvp.Key}.bin");
                        await File.WriteAllBytesAsync(tempFile, kvp.Value);
                        tempFiles[kvp.Key] = tempFile;
                        Console.WriteLine($"   Prepared {kvp.Key} ({kvp.Value.Length} bytes)");
                    }
                    
                    // Flash using esptool
                    var esptoolPath = GetEsptoolPath();
                    var flashCommand = BuildFlashCommand(comPort, tempFiles);
                    
                    Console.WriteLine("   Starting flash process...");
                    
                    var startInfo = new ProcessStartInfo
                    {
                        FileName = esptoolPath,
                        Arguments = flashCommand,
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        CreateNoWindow = true
                    };
                    
                    using (var process = Process.Start(startInfo))
                    {
                        var output = await process.StandardOutput.ReadToEndAsync();
                        var error = await process.StandardError.ReadToEndAsync();
                        
                        await process.WaitForExitAsync();
                        
                        if (process.ExitCode == 0 || output.Contains("Hash of data verified"))
                        {
                            Console.WriteLine("✅ Firmware flashed successfully!");
                        }
                        else
                        {
                            throw new Exception($"Flash failed: {error}");
                        }
                    }
                }
                finally
                {
                    // Immediately clean up temporary files
                    try
                    {
                        Directory.Delete(tempDir, true);
                        Console.WriteLine("🗑️ Temporary files securely deleted");
                    }
                    catch
                    {
                        // Best effort cleanup
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Memory-based flashing failed: {ex.Message}");
            }
        }

        static string GetEsptoolPath()
        {
            // Look for esptool in common locations
            var possiblePaths = new[]
            {
                Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "esptool.exe"),
                Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "tools", "esptool.exe"),
                "esptool.exe" // System PATH
            };
            
            foreach (var path in possiblePaths)
            {
                if (File.Exists(path))
                {
                    return path;
                }
            }
            
            throw new Exception("esptool.exe not found");
        }

        static string BuildFlashCommand(string comPort, Dictionary<string, string> tempFiles)
        {
            return $"--chip esp32s2 --port {comPort} --baud 460800 " +
                   $"--before default_reset --after hard_reset " +
                   $"write_flash --flash_mode dio --flash_freq 40m --flash_size 4MB " +
                   $"0x1000 \"{tempFiles["bootloader"]}\" " +
                   $"0x8000 \"{tempFiles["partitions"]}\" " +
                   $"0x10000 \"{tempFiles["firmware"]}\"";
        }

        static async Task VerifyEnhancedFirmware(string comPort)
        {
            Console.WriteLine("🔍 Verifying Enhanced Firmware...");
            
            try
            {
                using (var serialPort = new SerialPort(comPort, 115200))
                {
                    serialPort.ReadTimeout = 5000;
                    serialPort.WriteTimeout = 5000;
                    serialPort.Open();
                    
                    await Task.Delay(3000); // Wait for boot
                    
                    serialPort.DiscardInBuffer();
                    serialPort.WriteLine("version");
                    
                    await Task.Delay(1000);
                    
                    var response = serialPort.ReadExisting();
                    
                    if (response.Contains("OCTANE_ESP32_VER:2.1.0"))
                    {
                        Console.WriteLine("✅ Enhanced firmware verified successfully!");
                        
                        // Test unique identifier
                        serialPort.WriteLine("identify");
                        await Task.Delay(1000);
                        
                        var identifyResponse = serialPort.ReadExisting();
                        if (identifyResponse.Contains("OCTANE_ESP32_ID:"))
                        {
                            Console.WriteLine("✅ Unique identifier system verified!");
                        }
                    }
                    else
                    {
                        throw new Exception("Firmware verification failed");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Verification warning: {ex.Message}");
            }
        }

        static void CleanupSensitiveData()
        {
            // Clear sensitive data from memory
            firmwareFiles.Clear();
            firmwareChecksums.Clear();
            downloadToken = "";
            
            // Force garbage collection
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
            
            Console.WriteLine("🧹 Sensitive data cleared from memory");
        }

        static async Task SendErrorToWebhook(string title, string message, string stackTrace)
        {
            try
            {
                // Implementation would send to Discord webhook
                await Task.Delay(100); // Placeholder
            }
            catch
            {
                // Silently fail
            }
        }
    }
}
