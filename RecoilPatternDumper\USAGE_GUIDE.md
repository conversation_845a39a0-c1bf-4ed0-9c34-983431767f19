# Quick Start Guide - Recoil Pattern Dumper

## Step-by-Step Setup

### 1. Prerequisites Check
Before running the application, ensure you have:

- ✅ **Windows 10/11**
- ✅ **.NET 8.0 Runtime** installed
- ✅ **Python 3.7+** with UnityPy library
- ✅ **Rust game** installed and accessible
- ✅ **Python dumper scripts** (main.py and related files)

### 2. First Time Setup

#### Install Python Dependencies
```bash
pip install UnityPy
```

#### Verify Python Installation
```bash
python --version
python -c "import UnityPy; print('UnityPy installed successfully')"
```

#### Build the Application
1. Open Command Prompt in the project directory
2. Run: `build.bat`
3. Follow the prompts to build the application

### 3. Application Configuration

#### Launch the Application
- Run `RecoilPatternDumper.exe` from the build output folder
- The application will open with the Configuration tab active

#### Configure Python Environment
1. **Python Executable Path**: 
   - Default: `python` (if Python is in PATH)
   - Custom: Browse to `python.exe` location
   - Validation: Green checkmark indicates success

#### Configure Dumper Script
1. **Script Path**: Browse to your `main.py` file
   - Usually located in the dumper script directory
   - Must be the main entry point script
   - Validation: Confirms file exists and is accessible

#### Configure Rust Game Directory
1. **Game Path**: Browse to Rust installation folder
   - Typical locations:
     - `C:\Program Files (x86)\Steam\steamapps\common\Rust`
     - `C:\Program Files\Steam\steamapps\common\Rust`
   - Validation: Checks for content.bundle files

#### Analysis Settings
1. **Number of Runs**: Use slider to select 10-30 runs
   - **10 runs**: Quick analysis (5-10 minutes)
   - **20 runs**: Balanced analysis (10-20 minutes)
   - **30+ runs**: Comprehensive analysis (20+ minutes)

2. **Options**:
   - ✅ **Enable Detailed Logging**: Recommended for troubleshooting
   - ✅ **Auto-Export Results**: Automatically saves results after analysis
   - ✅ **Generate ESP32 Code**: Creates C++ header files

### 4. Running Analysis

#### Start Analysis
1. Switch to the **Analysis** tab
2. Click **Start Analysis** button
3. Monitor progress in real-time:
   - Progress bar shows completion percentage
   - Log window displays detailed execution information
   - Status bar shows current operation and elapsed time

#### Monitor Progress
- **Current Run**: Shows which dump run is executing
- **Success/Failure Count**: Tracks successful vs failed runs
- **Execution Time**: Real-time timer
- **Detailed Logs**: Scroll through for troubleshooting

#### Handle Issues
- **Stop Analysis**: Click if you need to cancel
- **Clear Log**: Clean up log window for better visibility
- **Error Messages**: Check log for specific error details

### 5. Viewing Results

#### Results Tab
1. **Weapon List**: Shows all analyzed weapons sorted by quality score
2. **Quality Scores**: Higher scores indicate more reliable patterns
3. **Weapon Details**: Click on any weapon to view statistics

#### Understanding Quality Scores
- **90-100**: Excellent reliability, highly consistent
- **80-89**: Very good, minor variations
- **70-79**: Good, acceptable for most uses
- **60-69**: Fair, some inconsistency
- **Below 60**: Poor reliability, consider re-analysis

#### Statistical Information
For each weapon, you'll see:
- **Recoil Properties**: Pitch/Yaw min/max values with standard deviations
- **Timing Data**: Time intervals and ADS scaling
- **Pattern Consistency**: Variation measurements
- **Sample Size**: Number of successful runs used

### 6. Exporting Results

#### Export Tab Options
Choose your desired export formats:

1. **JSON Format**: For integration with other tools
2. **CSV Format**: For spreadsheet analysis
3. **Detailed Report**: Human-readable analysis summary
4. **ESP32 C++ Header**: Ready-to-use firmware code
5. **Chart Data**: For creating visualizations

#### Export Methods

**Individual Exports**:
1. Select desired formats using checkboxes
2. Choose export directory
3. Click **Export Results**

**Complete Package**:
1. Click **Create Complete Package**
2. Generates timestamped folder with all formats
3. Includes README with analysis summary
4. Automatically opens export directory

### 7. Using Results with ESP32

#### Integration Steps
1. Copy the generated `.h` file to your ESP32 project
2. Include the header: `#include "recoil_patterns.h"`
3. Access weapon data using the info structs
4. Implement timing based on the optimal properties

#### Example Usage
```cpp
// Access AK47 pattern
extern const struct weapon_info ak47_info;

// Get pattern data
const float* pitch_pattern = ak47_info.pitch_pattern;
const float* yaw_pattern = ak47_info.yaw_pattern;
int pattern_length = ak47_info.pattern_length;
float confidence = ak47_info.confidence_score;

// Use in recoil compensation
for (int i = 0; i < pattern_length; i++) {
    apply_recoil_compensation(pitch_pattern[i], yaw_pattern[i]);
    delay(calculate_shot_interval());
}
```

## Troubleshooting Common Issues

### Python Environment Issues
**Problem**: "Python environment not valid"
**Solutions**:
- Verify Python installation: `python --version`
- Install UnityPy: `pip install UnityPy`
- Use full path to python.exe if not in PATH

### Script Validation Issues
**Problem**: "Script file not found"
**Solutions**:
- Verify main.py exists and is accessible
- Check file permissions
- Ensure all required Python files are present

### Game Directory Issues
**Problem**: "Invalid game directory"
**Solutions**:
- Verify Rust is properly installed
- Check for content.bundle files in game directory
- Try running Steam file verification

### Analysis Failures
**Problem**: Dumps fail or return no data
**Solutions**:
- Check Python script output in log window
- Verify game files are not corrupted
- Ensure sufficient disk space
- Try running Python script manually first

### Performance Issues
**Problem**: Analysis runs very slowly
**Solutions**:
- Use SSD storage if available
- Close unnecessary applications
- Reduce number of runs for testing
- Check available RAM and disk space

## Best Practices

### For Optimal Results
1. **Run 15-25 iterations** for good statistical balance
2. **Use consistent game state** (same version, no mods)
3. **Monitor system resources** during analysis
4. **Backup results** before making changes
5. **Validate patterns** before using in production

### For ESP32 Integration
1. **Test patterns gradually** starting with low confidence weapons
2. **Implement safety limits** to prevent excessive compensation
3. **Add user controls** for fine-tuning
4. **Monitor performance** impact on ESP32
5. **Keep backups** of working configurations

### For Troubleshooting
1. **Enable detailed logging** for problem diagnosis
2. **Run single dumps** manually to test Python scripts
3. **Check file permissions** if access errors occur
4. **Monitor disk space** during long analysis runs
5. **Save logs** for support requests

## Support and Updates

### Getting Help
- Check the detailed logs for specific error messages
- Verify all prerequisites are properly installed
- Test Python scripts independently
- Review the README.md for technical details

### Reporting Issues
When reporting problems, include:
- Application version and build date
- Operating system version
- Python version and UnityPy version
- Complete error messages from logs
- Steps to reproduce the issue

This tool is designed to work seamlessly with your existing Python dumper scripts while providing advanced statistical analysis and multiple export formats for maximum flexibility.
