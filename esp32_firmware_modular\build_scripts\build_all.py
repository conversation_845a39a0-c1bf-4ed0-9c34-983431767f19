#!/usr/bin/env python3
"""
ESP32-S2 Enhanced Firmware Build Script
Builds all configurations and generates release packages
"""

import os
import sys
import subprocess
import shutil
import datetime
import json
from pathlib import Path

# Build configurations
CONFIGURATIONS = {
    'debug': {
        'env': 'lolin_s2_mini_debug',
        'description': 'Debug build with full diagnostics',
        'filename': 'octane_esp32_debug'
    },
    'production': {
        'env': 'lolin_s2_mini_production',
        'description': 'Production build optimized for deployment',
        'filename': 'octane_esp32_production'
    },
    'secure': {
        'env': 'lolin_s2_mini_secure',
        'description': 'Security hardened build with anti-debugging',
        'filename': 'octane_esp32_secure'
    },
    'performance': {
        'env': 'lolin_s2_mini_performance',
        'description': 'Performance optimized build for zero delay',
        'filename': 'octane_esp32_performance'
    }
}

def run_command(cmd, cwd=None):
    """Run a command and return the result"""
    try:
        result = subprocess.run(cmd, shell=True, cwd=cwd, 
                              capture_output=True, text=True, check=True)
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        return False, e.stderr

def build_firmware(config_name, config):
    """Build firmware for a specific configuration"""
    print(f"\n🔨 Building {config_name} configuration...")
    print(f"   Environment: {config['env']}")
    print(f"   Description: {config['description']}")
    
    # Clean previous build
    success, output = run_command(f"pio run -e {config['env']} -t clean")
    if not success:
        print(f"❌ Clean failed for {config_name}: {output}")
        return False
    
    # Build firmware
    success, output = run_command(f"pio run -e {config['env']}")
    if not success:
        print(f"❌ Build failed for {config_name}: {output}")
        return False
    
    print(f"✅ {config_name} build completed successfully")
    return True

def copy_build_artifacts(config_name, config):
    """Copy build artifacts to release directory"""
    build_dir = Path(f".pio/build/{config['env']}")
    release_dir = Path("release") / config_name
    
    # Create release directory
    release_dir.mkdir(parents=True, exist_ok=True)
    
    # Copy firmware binary
    firmware_bin = build_dir / "firmware.bin"
    if firmware_bin.exists():
        shutil.copy2(firmware_bin, release_dir / f"{config['filename']}.bin")
        print(f"📦 Copied firmware binary to release/{config_name}/")
    
    # Copy bootloader and partitions if they exist
    bootloader_bin = build_dir / "bootloader.bin"
    if bootloader_bin.exists():
        shutil.copy2(bootloader_bin, release_dir / "bootloader.bin")
    
    partitions_bin = build_dir / "partitions.bin"
    if partitions_bin.exists():
        shutil.copy2(partitions_bin, release_dir / "partitions.bin")
    
    # Copy ELF file for debugging
    firmware_elf = build_dir / "firmware.elf"
    if firmware_elf.exists():
        shutil.copy2(firmware_elf, release_dir / f"{config['filename']}.elf")
    
    return True

def generate_build_info():
    """Generate build information file"""
    build_info = {
        'build_date': datetime.datetime.now().isoformat(),
        'firmware_version': '2.1.0',
        'hardware_version': 'ESP32-S2-Enhanced',
        'security_level': 'ADVANCED',
        'configurations': {}
    }
    
    for config_name, config in CONFIGURATIONS.items():
        build_info['configurations'][config_name] = {
            'environment': config['env'],
            'description': config['description'],
            'filename': config['filename']
        }
    
    # Write build info
    with open('release/build_info.json', 'w') as f:
        json.dump(build_info, f, indent=2)
    
    print("📋 Generated build_info.json")

def generate_flash_scripts():
    """Generate flash scripts for each configuration"""
    release_dir = Path("release")
    
    # Windows batch script
    batch_script = """@echo off
echo ESP32-S2 Octane Firmware Flasher
echo ================================

set /p config="Enter configuration (debug/production/secure/performance): "

if "%config%"=="debug" (
    echo Flashing debug firmware...
    esptool.py --chip esp32s2 --port COM3 --baud 921600 write_flash -z 0x1000 debug/octane_esp32_debug.bin
) else if "%config%"=="production" (
    echo Flashing production firmware...
    esptool.py --chip esp32s2 --port COM3 --baud 921600 write_flash -z 0x1000 production/octane_esp32_production.bin
) else if "%config%"=="secure" (
    echo Flashing secure firmware...
    esptool.py --chip esp32s2 --port COM3 --baud 921600 write_flash -z 0x1000 secure/octane_esp32_secure.bin
) else if "%config%"=="performance" (
    echo Flashing performance firmware...
    esptool.py --chip esp32s2 --port COM3 --baud 921600 write_flash -z 0x1000 performance/octane_esp32_performance.bin
) else (
    echo Invalid configuration!
    pause
    exit /b 1
)

echo.
echo Firmware flashed successfully!
pause
"""
    
    with open(release_dir / "flash_firmware.bat", 'w') as f:
        f.write(batch_script)
    
    # Linux/Mac shell script
    shell_script = """#!/bin/bash
echo "ESP32-S2 Octane Firmware Flasher"
echo "================================"

read -p "Enter configuration (debug/production/secure/performance): " config

case $config in
    debug)
        echo "Flashing debug firmware..."
        esptool.py --chip esp32s2 --port /dev/ttyUSB0 --baud 921600 write_flash -z 0x1000 debug/octane_esp32_debug.bin
        ;;
    production)
        echo "Flashing production firmware..."
        esptool.py --chip esp32s2 --port /dev/ttyUSB0 --baud 921600 write_flash -z 0x1000 production/octane_esp32_production.bin
        ;;
    secure)
        echo "Flashing secure firmware..."
        esptool.py --chip esp32s2 --port /dev/ttyUSB0 --baud 921600 write_flash -z 0x1000 secure/octane_esp32_secure.bin
        ;;
    performance)
        echo "Flashing performance firmware..."
        esptool.py --chip esp32s2 --port /dev/ttyUSB0 --baud 921600 write_flash -z 0x1000 performance/octane_esp32_performance.bin
        ;;
    *)
        echo "Invalid configuration!"
        exit 1
        ;;
esac

echo ""
echo "Firmware flashed successfully!"
"""
    
    with open(release_dir / "flash_firmware.sh", 'w') as f:
        f.write(shell_script)
    
    # Make shell script executable
    os.chmod(release_dir / "flash_firmware.sh", 0o755)
    
    print("📜 Generated flash scripts")

def main():
    """Main build function"""
    print("🚀 ESP32-S2 Enhanced Firmware Build System")
    print("==========================================")
    
    # Check if PlatformIO is available
    success, _ = run_command("pio --version")
    if not success:
        print("❌ PlatformIO not found. Please install PlatformIO first.")
        sys.exit(1)
    
    # Create release directory
    Path("release").mkdir(exist_ok=True)
    
    # Build all configurations
    build_results = {}
    for config_name, config in CONFIGURATIONS.items():
        success = build_firmware(config_name, config)
        build_results[config_name] = success
        
        if success:
            copy_build_artifacts(config_name, config)
        else:
            print(f"⚠️ Skipping artifact copy for failed build: {config_name}")
    
    # Generate additional files
    generate_build_info()
    generate_flash_scripts()
    
    # Summary
    print("\n📊 Build Summary")
    print("================")
    successful_builds = 0
    for config_name, success in build_results.items():
        status = "✅ SUCCESS" if success else "❌ FAILED"
        print(f"{config_name:12} - {status}")
        if success:
            successful_builds += 1
    
    print(f"\n🎯 {successful_builds}/{len(CONFIGURATIONS)} builds completed successfully")
    
    if successful_builds > 0:
        print(f"📦 Release packages available in: release/")
        print(f"🔧 Use flash scripts to deploy firmware")
    
    return successful_builds == len(CONFIGURATIONS)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
