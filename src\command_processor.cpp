#include "command_processor.h"
#include "usb_manager.h"
#include "led_controller.h"

// External USB Serial reference
extern USBCDC USBSerial;
#include "system_info.h"

// Global instance
CommandProcessor commandProcessor;

CommandProcessor::CommandProcessor() 
    : commandQueue(nullptr), commandBuffer(""), commandsProcessed(0), lastHeartbeat(0) {
}

void CommandProcessor::begin() {
    // Create command queue
    commandQueue = xQueueCreate(QUEUE_SIZE, sizeof(HIDCommand));
    
    if (commandQueue == NULL) {
        usbManager.sendDebugMessage("ERROR: Failed to create command queue");
        ledController.setState(LED_ERROR);
        return;
    }
    
    lastHeartbeat = millis();
    usbManager.sendDebugMessage("Command processor initialized");
}

void CommandProcessor::processSerialCommands() {
    while (USBSerial.available()) {
        char c = USBSerial.read();
        
        if (c == '\n' || c == '\r') {
            if (commandBuffer.length() > 0) {
                if (parseCommand(commandBuffer)) {
                    ledController.setState(LED_COMMAND_RECEIVED);
                }
                commandBuffer = "";
            }
        } else if (c >= 32 && c <= 126) { // Printable characters only
            commandBuffer += c;
            
            // Prevent buffer overflow
            if (commandBuffer.length() >= COMMAND_BUFFER_SIZE - 1) {
                usbManager.sendDebugMessage("ERROR: Command buffer overflow");
                commandBuffer = "";
            }
        }
    }
}

void CommandProcessor::processCommandQueue() {
    HIDCommand cmd;
    
    // Process all available commands
    while (xQueueReceive(commandQueue, &cmd, 0) == pdTRUE) {
        if (!cmd.processed) {
            executeCommand(cmd);
            commandsProcessed++;
        }
    }
}

void CommandProcessor::sendHeartbeat() {
    uint32_t currentTime = millis();
    
    if (currentTime - lastHeartbeat >= HEARTBEAT_INTERVAL) {
        lastHeartbeat = currentTime;
        
        String status = "HEARTBEAT|" + String(systemInfo.getUptime()) + "|" + 
                       String(commandsProcessed) + "|" + String(systemInfo.getFreeHeap()) + 
                       "|" + String(usbManager.isReady() ? "USB_OK" : "USB_ERR");
        
        usbManager.sendDebugMessage(status);
    }
}

uint32_t CommandProcessor::getCommandsProcessed() const {
    return commandsProcessed;
}

bool CommandProcessor::parseCommand(const String& command) {
    String trimmedCommand = command;
    trimmedCommand.trim();
    
    if (trimmedCommand.length() == 0) {
        return false;
    }
    
    // Try JSON format first
    if (trimmedCommand.startsWith("{") && trimmedCommand.endsWith("}")) {
        return parseJSONCommand(trimmedCommand);
    } else {
        return parseSimpleCommand(trimmedCommand);
    }
}

bool CommandProcessor::parseJSONCommand(const String& jsonStr) {
    DynamicJsonDocument doc(JSON_BUFFER_SIZE);
    DeserializationError error = deserializeJson(doc, jsonStr);
    
    if (error) {
        usbManager.sendDebugMessage("ERROR: JSON parse failed - " + String(error.c_str()));
        return false;
    }
    
    HIDCommand cmd = {CMD_STATUS, 0, 0, 0, millis(), false};
    
    if (doc.containsKey("type")) {
        String type = doc["type"];
        if (type == "move") {
            cmd.type = CMD_MOUSE_MOVE;
            cmd.x = doc["x"] | 0;
            cmd.y = doc["y"] | 0;
        } else if (type == "click") {
            cmd.type = doc["pressed"] ? CMD_CLICK_DOWN : CMD_CLICK_UP;
            cmd.button = doc["button"] | MOUSE_LEFT;
        } else if (type == "status") {
            cmd.type = CMD_STATUS;
        }
    }
    
    return queueCommand(cmd);
}

bool CommandProcessor::parseSimpleCommand(const String& command) {
    HIDCommand cmd = {CMD_STATUS, 0, 0, 0, millis(), false};
    
    if (command.startsWith("MOVE ")) {
        cmd.type = CMD_MOUSE_MOVE;
        int spaceIndex = command.indexOf(' ', 5);
        if (spaceIndex > 0) {
            cmd.x = command.substring(5, spaceIndex).toInt();
            cmd.y = command.substring(spaceIndex + 1).toInt();
        }
    } else if (command.startsWith("CLICK ")) {
        cmd.type = CMD_CLICK_DOWN;
        cmd.button = command.substring(6).toInt();
    } else if (command.startsWith("RELEASE ")) {
        cmd.type = CMD_CLICK_UP;
        cmd.button = command.substring(8).toInt();
    } else if (command == "STATUS") {
        cmd.type = CMD_STATUS;
    } else if (command == "ping" || command == "PING") {
        usbManager.sendDebugMessage("PONG|" + String(millis()));
        return true;
    } else if (command == "identify" || command == "IDENTIFY") {
        usbManager.sendDebugMessage("DEVICE|ESP32-S2|Octane|" + String(FIRMWARE_VERSION));
        return true;
    } else if (command.startsWith("MOUSE_MOVE ")) {
        // MOUSE_MOVE x,y
        String params = command.substring(11);
        int commaPos = params.indexOf(',');
        if (commaPos > 0) {
            float deltaX = params.substring(0, commaPos).toFloat();
            float deltaY = params.substring(commaPos + 1).toFloat();
            usbManager.sendMouseMove(static_cast<int16_t>(deltaX), static_cast<int16_t>(deltaY));
            usbManager.sendDebugMessage("Mouse moved: " + String(deltaX) + ", " + String(deltaY));
            return true;
        } else {
            usbManager.sendDebugMessage("ERROR: Invalid MOUSE_MOVE format");
            return false;
        }
    } else if (command == "get_version" || command == "GET_VERSION") {
        usbManager.sendDebugMessage("VERSION|" + String(FIRMWARE_VERSION));
        return true;
    } else if (command.startsWith("BLINK ")) {
        // BLINK count
        int count = command.substring(6).toInt();
        if (count > 0 && count <= 20) {
            ledController.setState(LED_ACTIVE); // Use active state for blinking
            usbManager.sendDebugMessage("Blinking LED " + String(count) + " times");
            return true;
        } else {
            usbManager.sendDebugMessage("ERROR: Invalid blink count (1-20)");
            return false;
        }
    } else if (command.startsWith("RECOIL_SMOOTH ")) {
        // Advanced recoil smoothing: RECOIL_SMOOTH totalX,totalY,steps,delayPerStep
        String params = command.substring(14);
        int pos1 = params.indexOf(',');
        int pos2 = params.indexOf(',', pos1 + 1);
        int pos3 = params.indexOf(',', pos2 + 1);

        if (pos1 > 0 && pos2 > 0 && pos3 > 0) {
            float totalX = params.substring(0, pos1).toFloat();
            float totalY = params.substring(pos1 + 1, pos2).toFloat();
            int steps = params.substring(pos2 + 1, pos3).toInt();
            int delayPerStep = params.substring(pos3 + 1).toInt();

            executeSmoothRecoil(totalX, totalY, steps, delayPerStep);
            return true;
        } else {
            usbManager.sendDebugMessage("ERROR: Invalid RECOIL_SMOOTH format");
            return false;
        }
    } else {
        usbManager.sendDebugMessage("ERROR: Unknown command - " + command);
        return false;
    }
    
    return queueCommand(cmd);
}

bool CommandProcessor::queueCommand(const HIDCommand& cmd) {
    if (commandQueue == nullptr) {
        usbManager.sendDebugMessage("ERROR: Command queue not initialized");
        return false;
    }
    
    if (xQueueSend(commandQueue, &cmd, 0) != pdTRUE) {
        usbManager.sendDebugMessage("ERROR: Command queue full");
        return false;
    }
    
    return true;
}

void CommandProcessor::executeCommand(const HIDCommand& cmd) {
    switch (cmd.type) {
        case CMD_MOUSE_MOVE:
            usbManager.sendMouseMove(cmd.x, cmd.y);
            usbManager.sendDebugMessage("MOVE " + String(cmd.x) + " " + String(cmd.y));
            break;
            
        case CMD_CLICK_DOWN:
            usbManager.sendMouseClick(cmd.button, true);
            usbManager.sendDebugMessage("CLICK " + String(cmd.button));
            break;
            
        case CMD_CLICK_UP:
            usbManager.sendMouseClick(cmd.button, false);
            usbManager.sendDebugMessage("RELEASE " + String(cmd.button));
            break;
            
        case CMD_STATUS:
            sendStatusResponse();
            break;
    }
}

void CommandProcessor::sendStatusResponse() {
    String response = "STATUS|OK|" + String(systemInfo.getUptime()) + "|" + 
                     String(commandsProcessed) + "|" + String(systemInfo.getFreeHeap());
    usbManager.sendDebugMessage(response);
}

void CommandProcessor::sendSystemInfo() {
    usbManager.sendDebugMessage("SYSTEM|" + systemInfo.getChipModel() + "|" +
                               String(systemInfo.getCpuFreqMHz()) + "MHz|" +
                               String(systemInfo.getFreeHeap()) + "B");
}

void CommandProcessor::executeSmoothRecoil(float totalX, float totalY, int steps, int delayPerStep) {
    if (steps <= 0) steps = 1;

    float stepX = totalX / steps;
    float stepY = totalY / steps;

    // Static accumulators to maintain precision across calls (based on your algorithm)
    static float accumX = 0.0f;
    static float accumY = 0.0f;

    usbManager.sendDebugMessage("SMOOTH_RECOIL: " + String(totalX) + "," + String(totalY) +
                               " steps=" + String(steps) + " delay=" + String(delayPerStep));

    for (int i = 0; i < steps; ++i) {
        accumX += stepX;
        accumY += stepY;

        // Only move when we have at least 1 pixel of movement (your precision approach)
        if (abs(accumX) >= 1.0f || abs(accumY) >= 1.0f) {
            int moveX = static_cast<int>(accumX);
            int moveY = static_cast<int>(accumY);

            // Send mouse movement via HID
            usbManager.sendMouseMove(static_cast<int16_t>(moveX), static_cast<int16_t>(moveY));

            // Subtract the movement we just made from accumulators
            accumX -= moveX;
            accumY -= moveY;
        }

        // Delay between steps (RPM-based timing from your system)
        if (delayPerStep > 0 && i < steps - 1) {
            delay(delayPerStep);
        }
    }
}
