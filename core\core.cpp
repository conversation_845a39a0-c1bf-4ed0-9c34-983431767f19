
#include "core.hpp"

vec2_t m_core::to_pixel(vec2_t input)
{
	input.x = (round(input.x / (-0.03 * (static_cast<float>(m_global::m_settings::sensitivity * m_global::m_settings::ads_sensitivity) * 3.0f) * (m_global::m_settings::field_of_view / 100))));
	input.y = (round(input.y / (-0.03 * (static_cast<float>(m_global::m_settings::sensitivity * m_global::m_settings::ads_sensitivity) * 3.0f) * (m_global::m_settings::field_of_view / 100))));

	return input;
}

bool m_core::static_cache()
{
	cached_weapons.clear();
	weapon_list.clear();

	weapon_list.push_back(m_data::assault_rifle);
	weapon_list.push_back(m_data::assault_rifle);
	weapon_list.push_back(m_data::assault_rifle);

	for (std::size_t idx = 0; idx < weapon_list.size(); ++idx)
	{
		std::vector<vec2_t> temp{};

		for (std::size_t jdx = 0; jdx < weapon_list[idx].max_bullet_count; ++jdx)
		{
			temp.push_back(to_pixel(weapon_list[idx].data_list[jdx]));
		}

		cached_weapons.push_back(temp);

		temp.clear();
	}

	if (cached_weapons.size() > 0)
		return true;

	return false;
}

void m_core::initialize()
{
	static double last_shot_time = 133;
	static m_timer* timer = new m_timer();

	while (true)
	{
		auto selected_weapon = 1;

		for (std::size_t idx = 0; idx < (static_cast<int>(weapon_list[selected_weapon].max_bullet_count) &&
			GetAsyncKeyState(VK_LBUTTON)) && m_global::m_combo::m_weapons::selected != 0; ++idx)
		{
			if (!m_global::m_settings::recoil_control)
				continue;

			timer->start();

			last_shot_time = std::chrono::time_point_cast<std::chrono::milliseconds>(std::chrono::high_resolution_clock::now()).time_since_epoch().count();

			auto data = cached_weapons[selected_weapon][idx];

			if (GetAsyncKeyState(m_global::m_keybinds::crouch))
				data *= 0.5f;

			m_mouse::interpolate(data * m_movement::final_multiplier(), 100, weapon_list[selected_weapon].delay - timer->elapsed_time_ms());

			timer->end();
		}

		//std::this_thread::sleep_for(c_globals::c_combo::c_performance_mode::selected == 0 ? std::chrono::microseconds(50) : std::chrono::milliseconds(1));
	}
}
