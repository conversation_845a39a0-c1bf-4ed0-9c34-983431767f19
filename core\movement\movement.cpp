#include "movement.hpp"

bool m_movement::crouching()
{
	return GetAsyncKeyState(m_global::m_keybinds::crouch) && !walking();
}

bool m_movement::crouch_walking()
{
	return GetAsyncKeyState(m_global::m_keybinds::crouch) && walking();
}

bool m_movement::walking()
{
	return (GetAsyncKeyState(0x57) || GetAsyncKeyState(0x41) || GetAsyncKeyState(0x53) || GetAsyncKeyState(0x44));
}

float m_movement::final_multiplier()
{
	if (crouch_walking())
	{
		if (m_global::m_selected::weapon == 7 || m_global::m_selected::weapon == 8)
			return 1.71f;
		else
			return 1.11f;
	}
	else if (walking())
	{
		if (m_global::m_selected::weapon == 7 || m_global::m_selected::weapon == 8)
			return 2.1f;
		else
			return 1.17f;
	}
	else
		return 1.f;
}



