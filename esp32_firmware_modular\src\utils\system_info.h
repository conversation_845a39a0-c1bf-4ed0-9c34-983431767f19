/*
 * System Information Header
 * Handles system diagnostics, monitoring, and information reporting
 */

#ifndef SYSTEM_INFO_H
#define SYSTEM_INFO_H

#include <Arduino.h>
#include <esp_system.h>
#include "../config/firmware_config.h"
#include "../config/command_types.h"

class SystemInfo {
private:
    unsigned long bootTime;
    SystemMetrics metrics;
    bool diagnosticsEnabled;
    unsigned long lastDiagnosticCheck;
    
    // System monitoring
    uint32_t minFreeHeap;
    uint32_t maxFreeHeap;
    float averageCpuUsage;
    uint32_t totalResets;
    
    // Performance monitoring
    unsigned long lastPerformanceCheck;
    float systemLoad;
    uint32_t taskSwitches;
    
public:
    SystemInfo();
    
    // Initialization
    bool initialize();
    void recordBootTime();
    
    // System information
    void printSystemInfo();
    void printEnhancedSystemInfo();
    void printHardwareInfo();
    void printMemoryInfo();
    void printPerformanceInfo();
    
    // Diagnostics
    void performSystemDiagnostics();
    void checkSystemHealth();
    bool validateSystemIntegrity();
    
    // Metrics
    SystemMetrics getSystemMetrics() const;
    void updateMetrics();
    void resetMetrics();
    
    // Memory monitoring
    uint32_t getFreeHeap() const;
    uint32_t getMinFreeHeap() const;
    uint32_t getMaxFreeHeap() const;
    float getMemoryUsagePercent() const;
    
    // Performance monitoring
    float getSystemLoad() const;
    float getAverageCpuUsage() const;
    uint32_t getUptime() const;
    
    // Configuration
    void enableDiagnostics(bool enabled);
    void setDiagnosticInterval(uint32_t interval);
    
    // Emergency methods
    void emergencyDiagnostics();
    void dumpSystemState();
};

#endif // SYSTEM_INFO_H
