using System;
using System.Collections.Generic;
using System.Linq;

namespace RecoilController.Services
{
    /// <summary>
    /// Movement prediction engine for enhanced accuracy
    /// </summary>
    public class MovementPredictor
    {
        private readonly Queue<Vector2> _velocityHistory = new Queue<Vector2>();
        private readonly Queue<Vector2> _accelerationHistory = new Queue<Vector2>();

        public Vector2 PredictOptimalMovement(Vector2 currentMovement, Queue<MovementSample> history)
        {
            if (history.Count < 3)
                return currentMovement;

            // Calculate velocity and acceleration trends
            var recentSamples = history.TakeLast(5).ToArray();
            var predictedMovement = AnalyzeTrends(recentSamples, currentMovement);
            
            // Apply prediction smoothing
            return ApplyPredictionSmoothing(currentMovement, predictedMovement);
        }

        private Vector2 AnalyzeTrends(MovementSample[] samples, Vector2 current)
        {
            if (samples.Length < 2)
                return current;

            // Calculate average velocity
            var velocities = new List<Vector2>();
            for (int i = 1; i < samples.Length; i++)
            {
                var timeDelta = samples[i].Timestamp - samples[i - 1].Timestamp;
                if (timeDelta > 0)
                {
                    var velocity = new Vector2(
                        (samples[i].Movement.X - samples[i - 1].Movement.X) / timeDelta,
                        (samples[i].Movement.Y - samples[i - 1].Movement.Y) / timeDelta
                    );
                    velocities.Add(velocity);
                }
            }

            if (velocities.Count == 0)
                return current;

            // Calculate trend-based prediction
            var avgVelocity = new Vector2(
                velocities.Average(v => v.X),
                velocities.Average(v => v.Y)
            );

            // Predict next movement based on trend
            return new Vector2(
                current.X + avgVelocity.X * 0.1, // Small prediction factor
                current.Y + avgVelocity.Y * 0.1
            );
        }

        private Vector2 ApplyPredictionSmoothing(Vector2 current, Vector2 predicted)
        {
            const double smoothingFactor = 0.3;
            return new Vector2(
                current.X * (1 - smoothingFactor) + predicted.X * smoothingFactor,
                current.Y * (1 - smoothingFactor) + predicted.Y * smoothingFactor
            );
        }
    }

    /// <summary>
    /// Latency compensation for improved timing accuracy
    /// </summary>
    public class LatencyCompensator
    {
        private double _measuredLatency = 2.0; // Default 2ms
        private readonly Queue<double> _latencyHistory = new Queue<double>();

        public Vector2 CompensateLatency(Vector2 movement, WeaponContext context)
        {
            // Apply latency compensation based on measured system latency
            var compensationFactor = CalculateCompensationFactor(context);
            
            return new Vector2(
                movement.X * compensationFactor,
                movement.Y * compensationFactor
            );
        }

        private double CalculateCompensationFactor(WeaponContext context)
        {
            // Base compensation on latency and weapon characteristics
            var baseFactor = 1.0 + (_measuredLatency / 100.0);
            
            // Adjust for weapon-specific factors
            if (context.IsAiming)
                baseFactor *= 0.95; // Slightly reduce for ADS
            
            if (context.IsCrouching)
                baseFactor *= 0.98; // Reduce for crouching stability
            
            return Math.Max(0.8, Math.Min(1.2, baseFactor));
        }

        public void UpdateLatency(double newLatency)
        {
            _latencyHistory.Enqueue(newLatency);
            if (_latencyHistory.Count > 10)
                _latencyHistory.Dequeue();
            
            _measuredLatency = _latencyHistory.Average();
        }
    }

    /// <summary>
    /// Sub-pixel accumulator for precise movement
    /// </summary>
    public class SubPixelAccumulator
    {
        private double _accumulatedX = 0.0;
        private double _accumulatedY = 0.0;

        public Vector2 AccumulateMovement(Vector2 movement)
        {
            // Add to accumulated sub-pixel values
            _accumulatedX += movement.X;
            _accumulatedY += movement.Y;

            // Extract integer pixel movements
            var pixelX = (int)Math.Round(_accumulatedX);
            var pixelY = (int)Math.Round(_accumulatedY);

            // Keep fractional parts for next accumulation
            _accumulatedX -= pixelX;
            _accumulatedY -= pixelY;

            return new Vector2(pixelX, pixelY);
        }

        public void Reset()
        {
            _accumulatedX = 0.0;
            _accumulatedY = 0.0;
        }
    }

    /// <summary>
    /// Adaptive smoothing engine for context-aware movement
    /// </summary>
    public class AdaptiveSmoothingEngine
    {
        private readonly Queue<Vector2> _smoothingBuffer = new Queue<Vector2>();
        private const int BufferSize = 5;

        public Vector2 ApplyAdaptiveSmoothing(Vector2 movement, WeaponContext context)
        {
            _smoothingBuffer.Enqueue(movement);
            if (_smoothingBuffer.Count > BufferSize)
                _smoothingBuffer.Dequeue();

            var smoothingStrength = CalculateSmoothingStrength(context);
            return ApplySmoothing(movement, smoothingStrength);
        }

        private double CalculateSmoothingStrength(WeaponContext context)
        {
            double strength = 0.5; // Base smoothing

            // Adjust based on context
            if (context.IsAiming)
                strength += 0.2; // More smoothing when aiming

            if (context.IsCrouching)
                strength += 0.1; // Slight increase for crouching

            if (context.IsMoving)
                strength -= 0.1; // Less smoothing when moving

            // Weapon-specific adjustments
            switch (context.WeaponName.ToLower())
            {
                case "ak47":
                    strength += 0.1; // AK needs more smoothing
                    break;
                case "m4a4":
                    strength += 0.05; // M4 needs moderate smoothing
                    break;
                case "awp":
                    strength -= 0.2; // Sniper rifles need minimal smoothing
                    break;
            }

            return Math.Max(0.1, Math.Min(0.9, strength));
        }

        private Vector2 ApplySmoothing(Vector2 current, double strength)
        {
            if (_smoothingBuffer.Count < 2)
                return current;

            var buffer = _smoothingBuffer.ToArray();
            var smoothedX = 0.0;
            var smoothedY = 0.0;

            // Apply weighted average with recent movements having more weight
            for (int i = 0; i < buffer.Length; i++)
            {
                var weight = (i + 1.0) / buffer.Length; // More recent = higher weight
                smoothedX += buffer[i].X * weight;
                smoothedY += buffer[i].Y * weight;
            }

            var weightSum = buffer.Length * (buffer.Length + 1) / 2.0;
            smoothedX /= weightSum;
            smoothedY /= weightSum;

            // Blend smoothed with current based on strength
            return new Vector2(
                current.X * (1 - strength) + smoothedX * strength,
                current.Y * (1 - strength) + smoothedY * strength
            );
        }
    }

    /// <summary>
    /// Advanced recoil pattern analyzer
    /// </summary>
    public class RecoilPatternAnalyzer
    {
        private readonly Dictionary<string, RecoilPattern> _patterns = new Dictionary<string, RecoilPattern>();

        public RecoilPattern AnalyzeWeaponPattern(string weaponName, List<Vector2> shots)
        {
            if (shots.Count < 5)
                return GetDefaultPattern(weaponName);

            var pattern = new RecoilPattern
            {
                WeaponName = weaponName,
                ShotCount = shots.Count
            };

            // Analyze horizontal drift
            pattern.HorizontalDrift = CalculateHorizontalDrift(shots);
            
            // Analyze vertical climb
            pattern.VerticalClimb = CalculateVerticalClimb(shots);
            
            // Analyze pattern consistency
            pattern.Consistency = CalculateConsistency(shots);
            
            // Calculate optimal compensation
            pattern.OptimalCompensation = CalculateOptimalCompensation(shots);

            _patterns[weaponName] = pattern;
            return pattern;
        }

        private double CalculateHorizontalDrift(List<Vector2> shots)
        {
            if (shots.Count < 2) return 0.0;
            
            var drifts = new List<double>();
            for (int i = 1; i < shots.Count; i++)
            {
                drifts.Add(shots[i].X - shots[i - 1].X);
            }
            
            return drifts.Average();
        }

        private double CalculateVerticalClimb(List<Vector2> shots)
        {
            if (shots.Count < 2) return 0.0;
            
            var climbs = new List<double>();
            for (int i = 1; i < shots.Count; i++)
            {
                climbs.Add(shots[i].Y - shots[i - 1].Y);
            }
            
            return climbs.Average();
        }

        private double CalculateConsistency(List<Vector2> shots)
        {
            if (shots.Count < 3) return 1.0;
            
            var deviations = new List<double>();
            var avgX = shots.Average(s => s.X);
            var avgY = shots.Average(s => s.Y);
            
            foreach (var shot in shots)
            {
                var deviation = Math.Sqrt(Math.Pow(shot.X - avgX, 2) + Math.Pow(shot.Y - avgY, 2));
                deviations.Add(deviation);
            }
            
            var avgDeviation = deviations.Average();
            return Math.Max(0.1, 1.0 - (avgDeviation / 100.0)); // Normalize to 0.1-1.0
        }

        private Vector2 CalculateOptimalCompensation(List<Vector2> shots)
        {
            if (shots.Count < 2) return new Vector2(0, 0);
            
            var totalX = 0.0;
            var totalY = 0.0;
            
            for (int i = 1; i < shots.Count; i++)
            {
                totalX += shots[i].X - shots[0].X;
                totalY += shots[i].Y - shots[0].Y;
            }
            
            return new Vector2(
                -totalX / (shots.Count - 1), // Negative for compensation
                -totalY / (shots.Count - 1)
            );
        }

        private RecoilPattern GetDefaultPattern(string weaponName)
        {
            return new RecoilPattern
            {
                WeaponName = weaponName,
                HorizontalDrift = 0.0,
                VerticalClimb = 2.0,
                Consistency = 0.8,
                OptimalCompensation = new Vector2(0, -2.0)
            };
        }
    }

    public class RecoilPattern
    {
        public string WeaponName { get; set; } = "";
        public int ShotCount { get; set; }
        public double HorizontalDrift { get; set; }
        public double VerticalClimb { get; set; }
        public double Consistency { get; set; }
        public Vector2 OptimalCompensation { get; set; }
    }
}
