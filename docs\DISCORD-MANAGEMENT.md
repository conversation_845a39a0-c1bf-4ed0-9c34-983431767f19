# 🤖 Discord Bot Management System

Comprehensive Discord bot management interface for the Octane Authentication System.

## 🎯 Overview

The Discord Management page provides complete control over your Discord bot, including configuration, command testing, monitoring, and real-time management.

**Access:** http://217.154.58.14/discord

## 📊 Features

### **1. Overview Tab**
- **Real-time bot status** monitoring
- **Quick action buttons** for common tasks
- **Server information** display
- **Usage statistics** tracking

### **2. Configuration Tab**
- **Bot credentials** management
- **Server and channel** configuration
- **Gateway intents** status
- **Application information** display

### **3. Commands Tab**
- **Available slash commands** documentation
- **Command registration** management
- **Command testing** interface

### **4. Logs Tab**
- **Real-time bot logs** viewing
- **Log filtering** by type
- **Error tracking** and debugging

### **5. Testing Tab**
- **Manual command execution**
- **Test result** monitoring
- **Command validation**

## 🔧 Configuration

### **Discord Credentials**
Your current Discord bot configuration:

```
Application ID: 1398460876402593924
Client ID: 1156721390720925828
Server ID: 1350622776943444040
Admin User ID: 1156721390720925828
Token: MTM5ODQ2MDg3NjQwMjU5MzkyNA.GvcFLz.3qJjO1ZPJSaffSweRBde8P4EwS7FIjcCUlbsnM
Public Key: 38424df4880709b105e0da73ed5ffa8c529f67de6f691c2618e1bb3f055a8919
```

### **Required Gateway Intents**
Enable these in Discord Developer Portal:

1. **Server Members Intent** ✅ Required
2. **Message Content Intent** ✅ Required  
3. **Presence Intent** ⚠️ Optional

**Enable at:** https://discord.com/developers/applications/1398460876402593924/bot

## 🎮 Available Commands

### **Slash Commands**

| Command | Description | Parameters |
|---------|-------------|------------|
| `/create-license` | Create new license key | `duration`, `notes` (optional) |
| `/reset-hwid` | Reset hardware ID | `license-key` |
| `/list-licenses` | List all licenses | `limit` (optional, 1-50) |
| `/check-license` | Check license details | `license-key` |

### **Duration Options**
- `1hour` - 1 Hour
- `1day` - 1 Day  
- `1week` - 1 Week
- `1month` - 1 Month
- `3months` - 3 Months
- `6months` - 6 Months
- `1year` - 1 Year
- `lifetime` - Lifetime

## 🚀 Quick Actions

### **Test Bot Connection**
Verifies bot connectivity and sends test message to configured channel.

### **Send Test Message**
Sends a formatted test message to verify messaging functionality.

### **Send Daily Report**
Generates and sends comprehensive license statistics report.

### **Restart Bot**
Safely restarts the Discord bot service with confirmation.

## 🔧 Management Features

### **Configuration Management**
- **Update bot token** directly from interface
- **Change server/channel IDs** without server restart
- **Modify admin permissions** in real-time

### **Command Management**
- **Re-register commands** if they become unavailable
- **Clear all commands** for troubleshooting
- **Test commands manually** before Discord deployment

### **Monitoring & Logs**
- **Real-time status** monitoring
- **Error tracking** and debugging
- **Usage statistics** and analytics

## 🧪 Testing Interface

### **Manual Command Testing**
Execute Discord commands directly from the admin interface:

1. **Select command type** from dropdown
2. **Configure parameters** (duration, license key, notes)
3. **Execute command** and view results
4. **Monitor test results** in real-time

### **Test Results**
All test executions are logged with timestamps and results for debugging.

## 📱 Mobile Responsive

The Discord management interface is fully responsive and works on:
- **Desktop browsers**
- **Tablet devices**
- **Mobile phones**

## 🔒 Security Features

### **Authentication Required**
- Same Firebase authentication as admin panel
- Admin-only access control
- Secure token management

### **Permission Validation**
- Discord admin user verification
- Server-specific command restrictions
- Rate limiting protection

## 🛠️ API Endpoints

The Discord management interface uses these API endpoints:

### **Status & Configuration**
- `GET /api/admin/discord-status` - Get bot status
- `POST /api/admin/update-discord-config` - Update configuration
- `POST /api/admin/update-discord-token` - Update bot token

### **Actions**
- `POST /api/admin/test-discord` - Test bot connection
- `POST /api/admin/send-test-message` - Send test message
- `POST /api/admin/send-daily-report` - Send daily report
- `POST /api/admin/restart-discord-bot` - Restart bot

### **Command Management**
- `POST /api/admin/execute-discord-command` - Execute manual command
- `GET /api/admin/discord-logs` - Get bot logs

## 🚨 Troubleshooting

### **Bot Offline Issues**
1. **Check token validity** in configuration tab
2. **Verify gateway intents** are enabled
3. **Restart bot** using quick action
4. **Check logs** for error messages

### **Commands Not Working**
1. **Re-register commands** in commands tab
2. **Verify bot permissions** in Discord server
3. **Check admin user ID** configuration
4. **Test manually** in testing tab

### **Permission Errors**
1. **Enable required intents** in Discord Developer Portal
2. **Verify bot has admin permissions** in Discord server
3. **Check server ID** configuration
4. **Confirm admin user ID** is correct

## 📞 Support

For Discord bot issues:
1. **Check the logs tab** for error messages
2. **Use the testing tab** to isolate issues
3. **Verify configuration** in the configuration tab
4. **Restart the bot** if needed

## 🎯 Integration

The Discord bot integrates with:
- **License Management System** - Real-time license operations
- **Admin Panel** - Unified authentication and management
- **VPS Backend** - Centralized API and database
- **ESP32 Firmware** - Hardware authentication notifications

---

**Octane by Lag** - Professional Discord Bot Management
