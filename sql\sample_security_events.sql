INSERT INTO security_events (type, severity, description, ip_address) VALUES 
('invalid_request', 'warning', 'Invalid license key format submitted', '*************'),
('license_not_found', 'critical', 'Attempt to use non-existent license key ABC-123-XYZ', '*********'),
('suspicious_activity', 'warning', 'Multiple failed authentication attempts', '************'),
('invalid_hwid', 'info', 'Hardware ID mismatch for license DEF-456-GHI', '*************'),
('brute_force', 'critical', 'Brute force attack detected from IP', '***********'),
('invalid_request', 'warning', 'Malformed JSON in request body', '*********'),
('license_expired', 'info', 'User attempted to use expired license', '************'),
('rate_limit', 'warning', 'Rate limit exceeded for API endpoint', '*************');
