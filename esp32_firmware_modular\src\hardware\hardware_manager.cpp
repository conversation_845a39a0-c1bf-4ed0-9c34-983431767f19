/*
 * Hardware Manager Implementation
 * Handles all hardware initialization, LED control, and USB functionality
 */

#include "hardware_manager.h"
#include <esp_system.h>

HardwareManager::HardwareManager() {
    isConnectedToHost = false;
    usbInitialized = false;
    serialInitialized = false;
    lastConnectionCheck = 0;
    currentLEDState = LED_BOOT;
    ledLastUpdate = 0;
    ledState = false;
    ledBlinkInterval = 1000;
    ledBrightness = 255;
    lastUSBCheck = 0;
    previousUSBState = false;
    
    // Initialize default hardware config
    config.led_pin = LED_PIN;
    config.serial_baud = SERIAL_BAUD;
    config.usb_hid_enabled = true;
    config.led_status_enabled = true;
    config.cpu_frequency_mhz = CPU_FREQUENCY_MHZ;
    config.power_save_enabled = false;
}

bool HardwareManager::initialize() {
    Serial.println("🔧 Initializing Hardware Manager...");
    
    // Initialize in proper order
    if (!initializeLED()) {
        Serial.println("❌ LED initialization failed");
        return false;
    }
    
    if (!initializeSerial()) {
        Serial.println("❌ Serial initialization failed");
        return false;
    }
    
    if (!initializeUSB()) {
        Serial.println("❌ USB initialization failed");
        return false;
    }
    
    // Boot sequence
    ledBootSequence();
    
    Serial.println("✅ Hardware Manager initialized successfully");
    return true;
}

bool HardwareManager::initializeSerial() {
    Serial.begin(config.serial_baud);
    
    // Wait for serial connection with timeout
    unsigned long startTime = millis();
    while (!Serial && (millis() - startTime) < 3000) {
        delay(10);
    }
    
    serialInitialized = Serial;
    
    if (serialInitialized) {
        Serial.println("✅ Serial communication initialized at " + String(config.serial_baud) + " baud");
    }
    
    return serialInitialized;
}

bool HardwareManager::initializeUSB() {
    if (!config.usb_hid_enabled) {
        Serial.println("⚠️ USB HID disabled in configuration");
        return true;
    }
    
    try {
        USB.begin();
        delay(100);
        
        mouse.begin();
        delay(1000);
        
        usbInitialized = true;
        Serial.println("✅ USB HID Mouse initialized");
        
        // Test mouse functionality
        if (isMouseReady()) {
            Serial.println("✅ Mouse functionality verified");
        } else {
            Serial.println("⚠️ Mouse functionality test failed");
        }
        
    } catch (...) {
        Serial.println("❌ USB initialization exception");
        usbInitialized = false;
    }
    
    return usbInitialized;
}

bool HardwareManager::initializeLED() {
    if (!config.led_status_enabled) {
        Serial.println("⚠️ LED status disabled in configuration");
        return true;
    }
    
    pinMode(config.led_pin, OUTPUT);
    digitalWrite(config.led_pin, LOW);
    
    // Test LED functionality
    for (int i = 0; i < 3; i++) {
        digitalWrite(config.led_pin, HIGH);
        delay(100);
        digitalWrite(config.led_pin, LOW);
        delay(100);
    }
    
    Serial.println("✅ LED initialized on pin " + String(config.led_pin));
    return true;
}

void HardwareManager::setLEDState(LEDState state) {
    if (!config.led_status_enabled) return;
    
    currentLEDState = state;
    
    switch (state) {
        case LED_BOOT:
            ledBlinkInterval = 200;
            break;
        case LED_INITIALIZING:
            ledBlinkInterval = 500;
            break;
        case LED_WAITING:
            ledBlinkInterval = 1000;
            break;
        case LED_CONNECTED:
            digitalWrite(config.led_pin, HIGH);
            return;
        case LED_ACTIVE:
            ledBlinkInterval = 100;
            break;
        case LED_STEALTH:
            digitalWrite(config.led_pin, LOW);
            return;
        case LED_ERROR:
            ledBlinkInterval = 50;
            break;
        case LED_SECURITY_ALERT:
            ledBlinkInterval = 25;
            break;
        case LED_TEMPLATE_ACTIVE:
            ledBlinkInterval = 150;
            break;
        case LED_ZERO_DELAY:
            ledBlinkInterval = 75;
            break;
    }
}

void HardwareManager::updateLED() {
    if (!config.led_status_enabled) return;
    
    if (currentLEDState == LED_CONNECTED || currentLEDState == LED_STEALTH) {
        return; // Static states handled in setLEDState
    }
    
    unsigned long currentTime = millis();
    if (currentTime - ledLastUpdate >= ledBlinkInterval) {
        ledState = !ledState;
        digitalWrite(config.led_pin, ledState ? HIGH : LOW);
        ledLastUpdate = currentTime;
    }
}

void HardwareManager::sendMouseMove(int16_t x, int16_t y) {
    if (!usbInitialized || !isMouseReady()) {
        return;
    }
    
    try {
        mouse.move(x, y);
        
        // Update LED for activity (only in non-stealth mode)
        if (currentLEDState != LED_STEALTH) {
            setLEDState(LED_ACTIVE);
        }
        
    } catch (...) {
        Serial.println("❌ Mouse move failed");
    }
}

void HardwareManager::sendMouseClick(uint8_t button, bool pressed) {
    if (!usbInitialized || !isMouseReady()) {
        return;
    }
    
    try {
        if (pressed) {
            mouse.press(button);
        } else {
            mouse.release(button);
        }
    } catch (...) {
        Serial.println("❌ Mouse click failed");
    }
}

bool HardwareManager::isUSBConnected() {
    return USB.connected();
}

bool HardwareManager::isMouseReady() {
    return usbInitialized && USB.connected();
}

void HardwareManager::updateConnectionStatus() {
    unsigned long currentTime = millis();
    
    if (currentTime - lastConnectionCheck >= 1000) { // Check every second
        bool currentUSBState = isUSBConnected();
        
        if (currentUSBState != previousUSBState) {
            if (currentUSBState) {
                Serial.println("🔌 USB connected");
                isConnectedToHost = true;
                if (currentLEDState != LED_STEALTH) {
                    setLEDState(LED_CONNECTED);
                }
            } else {
                Serial.println("🔌 USB disconnected");
                isConnectedToHost = false;
                setLEDState(LED_WAITING);
            }
            previousUSBState = currentUSBState;
        }
        
        lastConnectionCheck = currentTime;
    }
    
    // Update LED
    updateLED();
}

void HardwareManager::ledBootSequence() {
    if (!config.led_status_enabled) return;
    
    Serial.println("🔄 Executing LED boot sequence...");
    
    // Enhanced boot pattern
    for (int cycle = 0; cycle < 3; cycle++) {
        for (int brightness = 0; brightness <= 255; brightness += 15) {
            analogWrite(config.led_pin, brightness);
            delay(10);
        }
        for (int brightness = 255; brightness >= 0; brightness -= 15) {
            analogWrite(config.led_pin, brightness);
            delay(10);
        }
    }
    
    // Final state
    digitalWrite(config.led_pin, LOW);
    delay(500);
    digitalWrite(config.led_pin, HIGH);
    delay(200);
    digitalWrite(config.led_pin, LOW);
}

void HardwareManager::printHardwareInfo() {
    Serial.println("========================================");
    Serial.println("Hardware Manager Status");
    Serial.println("========================================");
    Serial.println("Serial Initialized: " + String(serialInitialized ? "YES" : "NO"));
    Serial.println("USB Initialized: " + String(usbInitialized ? "YES" : "NO"));
    Serial.println("USB Connected: " + String(isUSBConnected() ? "YES" : "NO"));
    Serial.println("Mouse Ready: " + String(isMouseReady() ? "YES" : "NO"));
    Serial.println("LED Pin: " + String(config.led_pin));
    Serial.println("LED State: " + String(currentLEDState));
    Serial.println("Free Heap: " + String(ESP.getFreeHeap()) + " bytes");
    Serial.println("CPU Frequency: " + String(ESP.getCpuFreqMHz()) + " MHz");
    Serial.println("Uptime: " + String(millis()) + " ms");
    Serial.println("========================================");
}

DeviceStatus HardwareManager::getHardwareStatus() {
    DeviceStatus status;
    status.usb_connected = isUSBConnected();
    status.serial_connected = serialInitialized;
    status.uptime_ms = millis();
    status.free_heap = ESP.getFreeHeap();
    status.led_state = currentLEDState;
    return status;
}

uint32_t HardwareManager::getUptime() {
    return millis();
}

uint32_t HardwareManager::getFreeHeap() {
    return ESP.getFreeHeap();
}
