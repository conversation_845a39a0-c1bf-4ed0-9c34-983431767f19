using Microsoft.Web.WebView2.Core;
using Microsoft.Web.WebView2.Wpf;
using System;
using System.Threading.Tasks;

namespace RecoilController.Security
{
    /// <summary>
    /// Manages WebView2 security features for production builds
    /// </summary>
    public static class WebViewSecurityManager
    {
        /// <summary>
        /// Configure WebView2 security settings based on build configuration
        /// </summary>
        public static async Task ConfigureSecurityAsync(WebView2 webView)
        {
            if (webView?.CoreWebView2 == null)
                return;

#if PRODUCTION_BUILD && BLOCK_DEV_TOOLS
            await ConfigureProductionSecurityAsync(webView.CoreWebView2);
#elif DEBUG && ALLOW_DEV_TOOLS
            await ConfigureDebugSecurityAsync(webView.CoreWebView2);
#endif
        }

        /// <summary>
        /// Configure production security - block dev tools, disable context menu, prevent text selection
        /// </summary>
        private static async Task ConfigureProductionSecurityAsync(CoreWebView2 coreWebView2)
        {
            // Block developer tools completely
            coreWebView2.Settings.AreDevToolsEnabled = false;
            coreWebView2.Settings.AreDefaultContextMenusEnabled = false;
            coreWebView2.Settings.AreBrowserAcceleratorKeysEnabled = false;
            coreWebView2.Settings.AreHostObjectsAllowed = false;
            coreWebView2.Settings.IsGeneralAutofillEnabled = false;
            coreWebView2.Settings.IsPasswordAutosaveEnabled = false;

            // Inject CSS to prevent text selection and hide web-based indicators
            var productionCSS = @"
                * {
                    -webkit-user-select: none !important;
                    -moz-user-select: none !important;
                    -ms-user-select: none !important;
                    user-select: none !important;
                    -webkit-touch-callout: none !important;
                    -webkit-tap-highlight-color: transparent !important;
                }
                
                body {
                    -webkit-app-region: drag;
                    cursor: default !important;
                }
                
                /* Hide any web-based indicators */
                ::-webkit-scrollbar { display: none !important; }
                html { scrollbar-width: none !important; }
                
                /* Disable right-click context menu */
                * { pointer-events: auto !important; }
                
                /* Hide selection highlights */
                ::selection { background: transparent !important; }
                ::-moz-selection { background: transparent !important; }
            ";

            // Inject JavaScript to block common developer shortcuts and methods
            var productionJS = @"
                // Block common developer shortcuts
                document.addEventListener('keydown', function(e) {
                    // Block F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U, Ctrl+Shift+C
                    if (e.keyCode === 123 || // F12
                        (e.ctrlKey && e.shiftKey && e.keyCode === 73) || // Ctrl+Shift+I
                        (e.ctrlKey && e.shiftKey && e.keyCode === 74) || // Ctrl+Shift+J
                        (e.ctrlKey && e.keyCode === 85) || // Ctrl+U
                        (e.ctrlKey && e.shiftKey && e.keyCode === 67)) { // Ctrl+Shift+C
                        e.preventDefault();
                        e.stopPropagation();
                        return false;
                    }
                }, true);
                
                // Block right-click context menu
                document.addEventListener('contextmenu', function(e) {
                    e.preventDefault();
                    return false;
                }, true);
                
                // Block text selection
                document.addEventListener('selectstart', function(e) {
                    e.preventDefault();
                    return false;
                }, true);
                
                // Block drag operations
                document.addEventListener('dragstart', function(e) {
                    e.preventDefault();
                    return false;
                }, true);
                
                // Override console methods to prevent debugging
                if (typeof console !== 'undefined') {
                    console.log = console.warn = console.error = console.info = console.debug = function() {};
                }
                
                // Block common debugging methods
                window.eval = function() { return null; };
                window.Function = function() { return function() {}; };
                
                // Detect and block developer tools
                let devtools = {open: false, orientation: null};
                const threshold = 160;
                
                setInterval(function() {
                    if (window.outerHeight - window.innerHeight > threshold || 
                        window.outerWidth - window.innerWidth > threshold) {
                        if (!devtools.open) {
                            devtools.open = true;
                            // Send security alert to C#
                            window.chrome.webview.postMessage({
                                action: 'securityAlert',
                                type: 'devToolsDetected',
                                message: 'Developer tools detected and blocked'
                            });
                        }
                    } else {
                        devtools.open = false;
                    }
                }, 500);
            ";

            // Apply security configurations on navigation completed
            coreWebView2.NavigationCompleted += async (sender, args) =>
            {
                try
                {
                    // Inject production security CSS
                    await coreWebView2.ExecuteScriptAsync($"const style = document.createElement('style'); style.textContent = `{productionCSS}`; document.head.appendChild(style);");

                    // Inject production security JavaScript
                    await coreWebView2.ExecuteScriptAsync(productionJS);
                }
                catch (Exception ex)
                {
                    // Log security injection failure
                    Console.WriteLine($"Security injection failed: {ex.Message}");
                }
            };
        }

        /// <summary>
        /// Configure debug security - allow dev tools for development
        /// </summary>
        private static async Task ConfigureDebugSecurityAsync(CoreWebView2 coreWebView2)
        {
            // Allow developer tools in debug mode
            coreWebView2.Settings.AreDevToolsEnabled = true;
            coreWebView2.Settings.AreDefaultContextMenusEnabled = true;
            coreWebView2.Settings.AreBrowserAcceleratorKeysEnabled = true;
            
            // Inject debug indicator
            var debugCSS = @"
                body::before {
                    content: 'DEBUG BUILD - DEV TOOLS ENABLED';
                    position: fixed;
                    top: 0;
                    left: 0;
                    background: #ff4444;
                    color: white;
                    padding: 5px 10px;
                    font-size: 12px;
                    z-index: 999999;
                    font-family: monospace;
                }
            ";

            coreWebView2.NavigationCompleted += async (sender, args) =>
            {
                try
                {
                    await coreWebView2.ExecuteScriptAsync($"const style = document.createElement('style'); style.textContent = `{debugCSS}`; document.head.appendChild(style);");
                    await coreWebView2.ExecuteScriptAsync("console.log('🔧 DEBUG MODE: Developer tools are enabled');");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Debug injection failed: {ex.Message}");
                }
            };

            await Task.CompletedTask;
        }

        /// <summary>
        /// Handle security alerts from WebView2
        /// </summary>
        public static void HandleSecurityAlert(string alertType, string message)
        {
#if PRODUCTION_BUILD && ENABLE_SECURITY && !DEBUG && !DISABLE_SECURITY
            // Log security violation to Discord and take action
            _ = Task.Run(async () =>
            {
                try
                {
                    var securityManager = new EnhancedSecurityManager();
                    await securityManager.LogSecurityViolationAsync($"WebView Security Alert: {alertType}", message);

                    // Take immediate action for critical violations
                    if (alertType == "devToolsDetected")
                    {
                        Environment.Exit(1); // Terminate application immediately
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Security alert handling failed: {ex.Message}");
                }
            });
#else
            // Debug mode - log but don't exit
            Console.WriteLine($"🔧 DEBUG MODE: WebView security alert ignored: {alertType} - {message}");
#endif
        }
    }
}
