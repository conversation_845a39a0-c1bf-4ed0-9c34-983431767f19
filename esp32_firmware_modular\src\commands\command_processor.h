/*
 * Command Processor Header
 * Handles all command parsing, queue management, and execution
 */

#ifndef COMMAND_PROCESSOR_H
#define COMMAND_PROCESSOR_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <freertos/FreeRTOS.h>
#include <freertos/queue.h>
#include "../config/firmware_config.h"
#include "../config/command_types.h"

// Forward declarations
class HardwareManager;
class SecurityManager;
class RecoilEngine;

class CommandProcessor {
private:
    QueueHandle_t commandQueue;
    String commandBuffer;
    uint32_t commandsProcessed;
    uint32_t commandsSuccessful;
    uint32_t commandsFailed;
    unsigned long lastHeartbeat;
    
    // Command parsing
    StaticJsonDocument<JSON_BUFFER_SIZE> jsonDoc;
    char serialBuffer[COMMAND_BUFFER_SIZE];
    uint16_t bufferIndex;
    
    // Command statistics
    SystemMetrics metrics;
    
    // References to other managers
    HardwareManager* hardware;
    SecurityManager* security;
    RecoilEngine* recoilEngine;
    
    // Command processing methods
    bool parseCommand(const String& command);
    bool parseJSONCommand(const String& jsonStr);
    bool parseSimpleCommand(const String& command);
    bool queueCommand(const Command& cmd);
    void executeCommand(const Command& cmd);
    
    // Specific command handlers
    void handlePingCommand();
    void handleStatusCommand();
    void handleMouseMoveCommand(const String& params);
    void handleRecoilSmoothCommand(const String& params);
    void handleClickCommand(const String& params, bool isDown);
    void handleIdentifyCommand();
    void handleSecurityHandshake(const String& params);
    void handleStealthModeCommand(const String& params);
    void handleSystemInfoCommand();

    // Calibration command handlers
    void handleCalibrationCommand();
    void handleCalibrationStatusCommand();
    void handleGetCalibrationDataCommand();

    // Zero delay optimized handlers
    void handleMouseMoveOptimized(const String& command);
    void handleRecoilSmoothOptimized(const String& command);
    void handleTemplateRecoilCommand(const String& command);
    
    // Response methods
    void sendResponse(const String& response);
    void sendStatusResponse();
    void sendSystemInfo();
    void sendErrorResponse(const String& error);
    void sendHeartbeat();
    
    // Validation methods
    bool validateCommand(const Command& cmd);
    bool validateParameters(const String& params);
    bool isCommandAuthorized(CommandType type);
    
public:
    CommandProcessor();
    
    // Initialization
    bool initialize();
    void setManagers(HardwareManager* hw, SecurityManager* sec, RecoilEngine* recoil);
    
    // Main processing methods
    void processSerialCommands();
    void processCommandQueue();
    void update();
    
    // Queue management
    bool addCommand(const Command& cmd);
    uint16_t getQueueSize();
    uint16_t getQueueSpaceAvailable();
    void clearQueue();
    
    // Statistics and monitoring
    uint32_t getCommandsProcessed() const;
    uint32_t getSuccessfulCommands() const;
    uint32_t getFailedCommands() const;
    SystemMetrics getMetrics() const;
    float getAverageResponseTime() const;
    
    // Configuration
    void enableHeartbeat(bool enabled);
    void setHeartbeatInterval(uint32_t interval);
    
    // Emergency methods
    void emergencyStop();
    void flushCommands();
    void resetStatistics();
    
    // Debug methods
    void printQueueStatus();
    void printCommandStatistics();
    void enableDebugMode(bool enabled);

    // Template timing methods (for recoil engine integration)
    void executeZeroDelayCommand(const Command& cmd);
    void processHighPriorityCommands();
    bool isHighPriorityCommand(CommandType type);
};

#endif // COMMAND_PROCESSOR_H
