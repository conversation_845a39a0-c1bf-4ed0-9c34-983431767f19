﻿#pragma checksum "..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "B79D46DE85AE16EFD84D19D7309D8E8D76973BB5"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace RecoilPatternDumper {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 72 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowsePythonButton;
        
        #line default
        #line hidden
        
        
        #line 73 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PythonPathTextBox;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PythonValidationText;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseScriptButton;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ScriptPathTextBox;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ScriptValidationText;
        
        #line default
        #line hidden
        
        
        #line 94 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseGameButton;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox GamePathTextBox;
        
        #line default
        #line hidden
        
        
        #line 97 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock GameValidationText;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider RunCountSlider;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RunCountText;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableDetailedLoggingCheckBox;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoExportCheckBox;
        
        #line default
        #line hidden
        
        
        #line 121 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox GenerateESP32CodeCheckBox;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StartAnalysisButton;
        
        #line default
        #line hidden
        
        
        #line 145 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StopAnalysisButton;
        
        #line default
        #line hidden
        
        
        #line 147 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearLogButton;
        
        #line default
        #line hidden
        
        
        #line 153 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar AnalysisProgressBar;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProgressText;
        
        #line default
        #line hidden
        
        
        #line 161 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LogTextBox;
        
        #line default
        #line hidden
        
        
        #line 174 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 176 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TimerText;
        
        #line default
        #line hidden
        
        
        #line 194 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox WeaponListBox;
        
        #line default
        #line hidden
        
        
        #line 203 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel WeaponDetailsPanel;
        
        #line default
        #line hidden
        
        
        #line 218 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ExportJsonCheckBox;
        
        #line default
        #line hidden
        
        
        #line 220 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ExportCsvCheckBox;
        
        #line default
        #line hidden
        
        
        #line 222 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ExportReportCheckBox;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ExportESP32CheckBox;
        
        #line default
        #line hidden
        
        
        #line 226 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ExportChartDataCheckBox;
        
        #line default
        #line hidden
        
        
        #line 233 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseExportButton;
        
        #line default
        #line hidden
        
        
        #line 235 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ExportPathTextBox;
        
        #line default
        #line hidden
        
        
        #line 240 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportButton;
        
        #line default
        #line hidden
        
        
        #line 242 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CreatePackageButton;
        
        #line default
        #line hidden
        
        
        #line 246 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ExportStatusText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/RecoilPatternDumper;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.BrowsePythonButton = ((System.Windows.Controls.Button)(target));
            
            #line 72 "..\..\..\MainWindow.xaml"
            this.BrowsePythonButton.Click += new System.Windows.RoutedEventHandler(this.BrowsePythonButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.PythonPathTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 73 "..\..\..\MainWindow.xaml"
            this.PythonPathTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.PythonPathTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.PythonValidationText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.BrowseScriptButton = ((System.Windows.Controls.Button)(target));
            
            #line 83 "..\..\..\MainWindow.xaml"
            this.BrowseScriptButton.Click += new System.Windows.RoutedEventHandler(this.BrowseScriptButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.ScriptPathTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 84 "..\..\..\MainWindow.xaml"
            this.ScriptPathTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ScriptPathTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.ScriptValidationText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.BrowseGameButton = ((System.Windows.Controls.Button)(target));
            
            #line 94 "..\..\..\MainWindow.xaml"
            this.BrowseGameButton.Click += new System.Windows.RoutedEventHandler(this.BrowseGameButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.GamePathTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 95 "..\..\..\MainWindow.xaml"
            this.GamePathTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.GamePathTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            this.GameValidationText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.RunCountSlider = ((System.Windows.Controls.Slider)(target));
            
            #line 112 "..\..\..\MainWindow.xaml"
            this.RunCountSlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.RunCountSlider_ValueChanged);
            
            #line default
            #line hidden
            return;
            case 11:
            this.RunCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.EnableDetailedLoggingCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 13:
            this.AutoExportCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 14:
            this.GenerateESP32CodeCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 15:
            this.StartAnalysisButton = ((System.Windows.Controls.Button)(target));
            
            #line 144 "..\..\..\MainWindow.xaml"
            this.StartAnalysisButton.Click += new System.Windows.RoutedEventHandler(this.StartAnalysisButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.StopAnalysisButton = ((System.Windows.Controls.Button)(target));
            
            #line 146 "..\..\..\MainWindow.xaml"
            this.StopAnalysisButton.Click += new System.Windows.RoutedEventHandler(this.StopAnalysisButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.ClearLogButton = ((System.Windows.Controls.Button)(target));
            
            #line 148 "..\..\..\MainWindow.xaml"
            this.ClearLogButton.Click += new System.Windows.RoutedEventHandler(this.ClearLogButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.AnalysisProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 19:
            this.ProgressText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.LogTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 21:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.TimerText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.WeaponListBox = ((System.Windows.Controls.ListBox)(target));
            
            #line 197 "..\..\..\MainWindow.xaml"
            this.WeaponListBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.WeaponListBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 24:
            this.WeaponDetailsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 25:
            this.ExportJsonCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 26:
            this.ExportCsvCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 27:
            this.ExportReportCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 28:
            this.ExportESP32CheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 29:
            this.ExportChartDataCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 30:
            this.BrowseExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 234 "..\..\..\MainWindow.xaml"
            this.BrowseExportButton.Click += new System.Windows.RoutedEventHandler(this.BrowseExportButton_Click);
            
            #line default
            #line hidden
            return;
            case 31:
            this.ExportPathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 32:
            this.ExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 241 "..\..\..\MainWindow.xaml"
            this.ExportButton.Click += new System.Windows.RoutedEventHandler(this.ExportButton_Click);
            
            #line default
            #line hidden
            return;
            case 33:
            this.CreatePackageButton = ((System.Windows.Controls.Button)(target));
            
            #line 243 "..\..\..\MainWindow.xaml"
            this.CreatePackageButton.Click += new System.Windows.RoutedEventHandler(this.CreatePackageButton_Click);
            
            #line default
            #line hidden
            return;
            case 34:
            this.ExportStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

