using System;
using System.Runtime.InteropServices;

namespace HidDeviceTest
{
    class Program
    {
        // ===== HID API IMPORTS =====
        [DllImport("hid.dll", SetLastError = true)]
        public static extern IntPtr HidD_GetHidGuid(out Guid hidGuid);

        [DllImport("setupapi.dll", SetLastError = true)]
        public static extern IntPtr SetupDiGetClassDevs(ref Guid classGuid, string enumerator, IntPtr hwndParent, uint flags);

        [DllImport("setupapi.dll", SetLastError = true)]
        public static extern bool SetupDiEnumDeviceInterfaces(IntPtr deviceInfoSet, IntPtr deviceInfoData, ref Guid interfaceClassGuid, uint memberIndex, ref SP_DEVICE_INTERFACE_DATA deviceInterfaceData);

        [DllImport("setupapi.dll", SetLastError = true)]
        public static extern bool SetupDiGetDeviceInterfaceDetail(IntPtr deviceInfoSet, ref SP_DEVICE_INTERFACE_DATA deviceInterfaceData, IntPtr deviceInterfaceDetailData, uint deviceInterfaceDetailDataSize, out uint requiredSize, IntPtr deviceInfoData);

        [DllImport("setupapi.dll", SetLastError = true)]
        public static extern bool SetupDiDestroyDeviceInfoList(IntPtr deviceInfoSet);

        [DllImport("kernel32.dll", SetLastError = true)]
        public static extern IntPtr CreateFile(string fileName, uint desiredAccess, uint shareMode, IntPtr securityAttributes, uint creationDisposition, uint flagsAndAttributes, IntPtr templateFile);

        [DllImport("kernel32.dll", SetLastError = true)]
        public static extern bool CloseHandle(IntPtr handle);

        public const uint DIGCF_PRESENT = 0x00000002;
        public const uint DIGCF_DEVICEINTERFACE = 0x00000010;
        public const uint GENERIC_READ = 0x80000000;
        public const uint GENERIC_WRITE = 0x40000000;
        public const uint FILE_SHARE_READ = 0x00000001;
        public const uint FILE_SHARE_WRITE = 0x00000002;
        public const uint OPEN_EXISTING = 3;
        public const uint FILE_FLAG_OVERLAPPED = 0x40000000;
        public static readonly IntPtr INVALID_HANDLE_VALUE = (IntPtr)(-1);

        [StructLayout(LayoutKind.Sequential)]
        public struct SP_DEVICE_INTERFACE_DATA
        {
            public uint cbSize;
            public Guid interfaceClassGuid;
            public uint flags;
            public IntPtr reserved;
        }

        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Auto)]
        public struct SP_DEVICE_INTERFACE_DETAIL_DATA
        {
            public uint cbSize;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 256)]
            public string devicePath;
        }

        static void Main(string[] args)
        {
            Console.WriteLine("HID Device Detection Test");
            Console.WriteLine("========================");
            Console.WriteLine();

            try
            {
                Guid hidGuid;
                HidD_GetHidGuid(out hidGuid);

                Console.WriteLine($"HID GUID: {hidGuid}");
                Console.WriteLine();

                IntPtr deviceInfoSet = SetupDiGetClassDevs(ref hidGuid, null, IntPtr.Zero, 
                    DIGCF_PRESENT | DIGCF_DEVICEINTERFACE);

                if (deviceInfoSet != IntPtr.Zero)
                {
                    Console.WriteLine("Enumerating HID devices...");
                    Console.WriteLine();

                    var deviceInterfaceData = new SP_DEVICE_INTERFACE_DATA();
                    deviceInterfaceData.cbSize = (uint)Marshal.SizeOf(deviceInterfaceData);

                    uint deviceCount = 0;
                    uint memberIndex = 0;
                    
                    while (SetupDiEnumDeviceInterfaces(deviceInfoSet, IntPtr.Zero, ref hidGuid, memberIndex, ref deviceInterfaceData))
                    {
                        uint requiredSize;
                        SetupDiGetDeviceInterfaceDetail(deviceInfoSet, ref deviceInterfaceData, IntPtr.Zero, 0, out requiredSize, IntPtr.Zero);

                        if (requiredSize > 0)
                        {
                            IntPtr detailDataBuffer = Marshal.AllocHGlobal((int)requiredSize);
                            try
                            {
                                var detailData = new SP_DEVICE_INTERFACE_DETAIL_DATA();
                                detailData.cbSize = (uint)(IntPtr.Size == 8 ? 8 : 6); // Different sizes for x64/x86
                                
                                Marshal.StructureToPtr(detailData, detailDataBuffer, false);

                                if (SetupDiGetDeviceInterfaceDetail(deviceInfoSet, ref deviceInterfaceData, detailDataBuffer, requiredSize, out requiredSize, IntPtr.Zero))
                                {
                                    var devicePath = Marshal.PtrToStringAuto(detailDataBuffer + 4); // Skip cbSize field
                                    
                                    deviceCount++;
                                    Console.WriteLine($"Device #{deviceCount}:");
                                    Console.WriteLine($"  Path: {devicePath}");
                                    
                                    // Try to open the device
                                    IntPtr handle = CreateFile(devicePath, 
                                        GENERIC_READ | GENERIC_WRITE,
                                        FILE_SHARE_READ | FILE_SHARE_WRITE,
                                        IntPtr.Zero, OPEN_EXISTING, 0, IntPtr.Zero);

                                    if (handle != INVALID_HANDLE_VALUE)
                                    {
                                        Console.WriteLine("  Status: Device accessible");
                                        CloseHandle(handle);
                                    }
                                    else
                                    {
                                        Console.WriteLine($"  Status: Cannot access device (Error: {Marshal.GetLastWin32Error()})");
                                    }
                                    
                                    Console.WriteLine();
                                }
                            }
                            finally
                            {
                                Marshal.FreeHGlobal(detailDataBuffer);
                            }
                        }

                        memberIndex++;
                    }

                    if (deviceCount == 0)
                    {
                        Console.WriteLine("No HID devices found.");
                    }
                    else
                    {
                        Console.WriteLine($"Found {deviceCount} HID device(s).");
                    }

                    SetupDiDestroyDeviceInfoList(deviceInfoSet);
                }
                else
                {
                    Console.WriteLine($"Failed to get device info set. Error: {Marshal.GetLastWin32Error()}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }

            Console.WriteLine();
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
