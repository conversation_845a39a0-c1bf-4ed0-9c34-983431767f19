#pragma once

class vec2_t
{
public:

	float x, y;

	vec2_t() { };

	vec2_t(float x, float y)
	{
		this->x = x;
		this->y = y;
	}

	vec2_t operator+(const vec2_t& Input)
	{
		return vec2_t{ x + Input.x, y + Input.y };
	}

	vec2_t operator-(const vec2_t& Input)
	{
		return vec2_t{ x - Input.x, y - Input.y };
	}

	vec2_t operator*(const vec2_t& Input)
	{
		return vec2_t{ x * Input.x, y * Input.y };
	}

	vec2_t operator/(const float& Input)
	{
		return vec2_t{ x / Input, y / Input };
	}

	vec2_t operator*(const float& Input)
	{
		return vec2_t{ x * Input, y * Input };
	}

	void operator+=(const vec2_t& Input)
	{
		this->x += Input.x;
		this->y += Input.y;
	}

	void operator*=(const float& Input)
	{
		this->x *= Input;
		this->y *= Input;
	}

};