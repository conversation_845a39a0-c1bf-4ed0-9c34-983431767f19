/*
 * Hardware Manager Header
 * Manages all hardware initialization, LED control, and USB functionality
 */

#ifndef HARDWARE_MANAGER_H
#define HARDWARE_MANAGER_H

#include <Arduino.h>
#include <USB.h>
#include <USBHIDMouse.h>
#include "../config/firmware_config.h"
#include "../config/command_types.h"

class HardwareManager {
private:
    USBH<PERSON>Mouse mouse;
    bool isConnectedToHost;
    bool usbInitialized;
    bool serialInitialized;
    unsigned long lastConnectionCheck;
    LEDState currentLEDState;
    
    // LED control variables
    unsigned long ledLastUpdate;
    bool ledState;
    uint16_t ledBlinkInterval;
    uint8_t ledBrightness;
    
    // USB connection monitoring
    unsigned long lastUSBCheck;
    bool previousUSBState;
    
    // Hardware status
    HardwareConfig config;
    
public:
    HardwareManager();
    
    // Initialization methods
    bool initialize();
    bool initializeSerial();
    bool initializeUSB();
    bool initializeLED();
    
    // LED control methods
    void setLEDState(LEDState state);
    void updateLED();
    void setLEDBrightness(uint8_t brightness);
    void blinkLED(uint16_t interval);
    void ledBootSequence();
    void ledErrorPattern();
    void ledStealthMode();
    
    // USB and Mouse methods
    bool isUSBConnected();
    bool isMouseReady();
    void sendMouseMove(int16_t x, int16_t y);
    void sendMouseClick(uint8_t button, bool pressed);
    void sendMouseScroll(int8_t scroll);
    
    // Connection monitoring
    void updateConnectionStatus();
    bool getConnectionStatus();
    void checkUSBConnection();
    
    // Hardware diagnostics
    void printHardwareInfo();
    void performHardwareSelfTest();
    bool validateHardwareIntegrity();
    
    // Power management
    void enterLowPowerMode();
    void exitLowPowerMode();
    void optimizePowerConsumption();
    
    // Configuration methods
    void setHardwareConfig(const HardwareConfig& newConfig);
    HardwareConfig getHardwareConfig();
    
    // Status methods
    DeviceStatus getHardwareStatus();
    uint32_t getUptime();
    uint32_t getFreeHeap();
    
    // Emergency methods
    void emergencyReset();
    void safeShutdown();
};

#endif // HARDWARE_MANAGER_H
