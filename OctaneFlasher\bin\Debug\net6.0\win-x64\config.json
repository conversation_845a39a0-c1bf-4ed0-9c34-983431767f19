{"buildMode": "development", "version": "3.1.0", "features": {"production": {"debugMode": false, "monitorMode": false, "wiresharkMode": false, "testMode": false, "allowedModes": ["flash"], "description": "Production build - only flashing functionality"}, "development": {"debugMode": true, "monitorMode": true, "wiresharkMode": true, "testMode": true, "allowedModes": ["flash", "debug", "monitor", "wireshark"], "description": "Development build - all debugging features enabled"}}, "backend": {"baseUrl": "http://*************", "licenseValidationUrl": "http://*************/api/validate", "tokenEndpoint": "/api/firmware/token", "downloadEndpoint": "/api/firmware/download/esp32s2_enhanced"}, "serial": {"baudRate": 115200, "dataBits": 8, "parity": "None", "stopBits": "One", "handshake": "None", "readTimeout": 5000, "writeTimeout": 5000}, "monitoring": {"capturePackets": true, "showTimestamps": true, "showHexData": true, "showAsciiData": true, "maxPacketSize": 1024, "bufferSize": 10000}, "wireshark": {"enabled": true, "captureFilter": "", "displayFilter": "", "saveToFile": true, "outputFormat": "pcap", "maxFileSize": "100MB"}}