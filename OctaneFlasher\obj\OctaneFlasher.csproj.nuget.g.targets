﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)system.text.json\7.0.0\buildTransitive\net6.0\System.Text.Json.targets" Condition="Exists('$(NuGetPackageRoot)system.text.json\7.0.0\buildTransitive\net6.0\System.Text.Json.targets')" />
    <Import Project="$(NuGetPackageRoot)system.codedom\9.0.7\buildTransitive\netcoreapp2.0\System.CodeDom.targets" Condition="Exists('$(NuGetPackageRoot)system.codedom\9.0.7\buildTransitive\netcoreapp2.0\System.CodeDom.targets')" />
    <Import Project="$(NuGetPackageRoot)system.management\9.0.7\buildTransitive\netcoreapp2.0\System.Management.targets" Condition="Exists('$(NuGetPackageRoot)system.management\9.0.7\buildTransitive\netcoreapp2.0\System.Management.targets')" />
  </ImportGroup>
</Project>