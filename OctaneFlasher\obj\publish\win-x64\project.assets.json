{"version": 3, "targets": {"net6.0": {"Microsoft.NET.ILLink.Analyzers/7.0.100-1.23211.1": {"type": "package", "build": {"build/Microsoft.NET.ILLink.Analyzers.props": {}}}, "Microsoft.NET.ILLink.Tasks/7.0.100-1.23211.1": {"type": "package", "build": {"build/Microsoft.NET.ILLink.Tasks.props": {}}}, "runtime.linux-arm.runtime.native.System.IO.Ports/7.0.0": {"type": "package", "runtimeTargets": {"runtimes/linux-arm/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-arm"}}}, "runtime.linux-arm64.runtime.native.System.IO.Ports/7.0.0": {"type": "package", "runtimeTargets": {"runtimes/linux-arm64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-arm64"}}}, "runtime.linux-x64.runtime.native.System.IO.Ports/7.0.0": {"type": "package", "runtimeTargets": {"runtimes/linux-x64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-x64"}}}, "runtime.native.System.IO.Ports/7.0.0": {"type": "package", "dependencies": {"runtime.linux-arm.runtime.native.System.IO.Ports": "7.0.0", "runtime.linux-arm64.runtime.native.System.IO.Ports": "7.0.0", "runtime.linux-x64.runtime.native.System.IO.Ports": "7.0.0", "runtime.osx-arm64.runtime.native.System.IO.Ports": "7.0.0", "runtime.osx-x64.runtime.native.System.IO.Ports": "7.0.0"}}, "runtime.osx-arm64.runtime.native.System.IO.Ports/7.0.0": {"type": "package", "runtimeTargets": {"runtimes/osx-arm64/native/libSystem.IO.Ports.Native.dylib": {"assetType": "native", "rid": "osx-arm64"}}}, "runtime.osx-x64.runtime.native.System.IO.Ports/7.0.0": {"type": "package", "runtimeTargets": {"runtimes/osx-x64/native/libSystem.IO.Ports.Native.dylib": {"assetType": "native", "rid": "osx-x64"}}}, "System.CodeDom/8.0.0": {"type": "package", "compile": {"lib/net6.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.CodeDom.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.IO.Ports/7.0.0": {"type": "package", "dependencies": {"runtime.native.System.IO.Ports": "7.0.0"}, "compile": {"lib/net6.0/System.IO.Ports.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.IO.Ports.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/System.IO.Ports.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net6.0/System.IO.Ports.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Management/8.0.0": {"type": "package", "dependencies": {"System.CodeDom": "8.0.0"}, "compile": {"lib/net6.0/System.Management.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Management.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Management.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Text.Encodings.Web/7.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll": {"assetType": "runtime", "rid": "browser"}}}, "System.Text.Json/7.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "7.0.0"}, "compile": {"lib/net6.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/System.Text.Json.targets": {}}}}, "net6.0/win-x64": {"Microsoft.NET.ILLink.Analyzers/7.0.100-1.23211.1": {"type": "package", "build": {"build/Microsoft.NET.ILLink.Analyzers.props": {}}}, "Microsoft.NET.ILLink.Tasks/7.0.100-1.23211.1": {"type": "package", "build": {"build/Microsoft.NET.ILLink.Tasks.props": {}}}, "runtime.linux-arm.runtime.native.System.IO.Ports/7.0.0": {"type": "package"}, "runtime.linux-arm64.runtime.native.System.IO.Ports/7.0.0": {"type": "package"}, "runtime.linux-x64.runtime.native.System.IO.Ports/7.0.0": {"type": "package"}, "runtime.native.System.IO.Ports/7.0.0": {"type": "package", "dependencies": {"runtime.linux-arm.runtime.native.System.IO.Ports": "7.0.0", "runtime.linux-arm64.runtime.native.System.IO.Ports": "7.0.0", "runtime.linux-x64.runtime.native.System.IO.Ports": "7.0.0", "runtime.osx-arm64.runtime.native.System.IO.Ports": "7.0.0", "runtime.osx-x64.runtime.native.System.IO.Ports": "7.0.0"}}, "runtime.osx-arm64.runtime.native.System.IO.Ports/7.0.0": {"type": "package"}, "runtime.osx-x64.runtime.native.System.IO.Ports/7.0.0": {"type": "package"}, "System.CodeDom/8.0.0": {"type": "package", "compile": {"lib/net6.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.CodeDom.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.IO.Ports/7.0.0": {"type": "package", "dependencies": {"runtime.native.System.IO.Ports": "7.0.0"}, "compile": {"lib/net6.0/System.IO.Ports.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net6.0/System.IO.Ports.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Management/8.0.0": {"type": "package", "dependencies": {"System.CodeDom": "8.0.0"}, "compile": {"lib/net6.0/System.Management.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net6.0/System.Management.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Text.Encodings.Web/7.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Text.Json/7.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "7.0.0"}, "compile": {"lib/net6.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/System.Text.Json.targets": {}}}}}, "libraries": {"Microsoft.NET.ILLink.Analyzers/7.0.100-1.23211.1": {"sha512": "0GvbEgDGcUQA9KuWcQU1WwYHXt1tBzNr1Nls/M57rM7NA/AndFwCaCEoJpJkmxRY7xLlPDBnmGp8h5+FNqUngg==", "type": "package", "path": "microsoft.net.illink.analyzers/7.0.100-1.23211.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "analyzers/dotnet/cs/ILLink.CodeFixProvider.dll", "analyzers/dotnet/cs/ILLink.RoslynAnalyzer.dll", "build/Microsoft.NET.ILLink.Analyzers.props", "microsoft.net.illink.analyzers.7.0.100-1.23211.1.nupkg.sha512", "microsoft.net.illink.analyzers.nuspec"]}, "Microsoft.NET.ILLink.Tasks/7.0.100-1.23211.1": {"sha512": "tvG8XZYLjT0o3WicCyKBZysVWo1jC9HdCFmNRmddx3WbAz0UCsd0qKZqpiEo99VLA8Re+FzWK51OcRldQPbt2Q==", "type": "package", "path": "microsoft.net.illink.tasks/7.0.100-1.23211.1", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "Sdk/Sdk.props", "build/6.0_suppressions.xml", "build/Microsoft.NET.ILLink.Tasks.props", "build/Microsoft.NET.ILLink.targets", "microsoft.net.illink.tasks.7.0.100-1.23211.1.nupkg.sha512", "microsoft.net.illink.tasks.nuspec", "tools/net472/ILLink.Tasks.dll", "tools/net472/Mono.Cecil.dll", "tools/net472/System.Buffers.dll", "tools/net472/System.Collections.Immutable.dll", "tools/net472/System.Memory.dll", "tools/net472/System.Numerics.Vectors.dll", "tools/net472/System.Reflection.Metadata.dll", "tools/net472/System.Runtime.CompilerServices.Unsafe.dll", "tools/net7.0/ILLink.Tasks.deps.json", "tools/net7.0/ILLink.Tasks.dll", "tools/net7.0/Mono.Cecil.Pdb.dll", "tools/net7.0/Mono.Cecil.dll", "tools/net7.0/illink.deps.json", "tools/net7.0/illink.dll", "tools/net7.0/illink.runtimeconfig.json"]}, "runtime.linux-arm.runtime.native.System.IO.Ports/7.0.0": {"sha512": "CBvgRaF+M0xGLDv2Geb/0v0LEADheH8aK72GRAUJdnqnJVsQO60ki1XO8M3keEhnjm+T5NvLm41pNXAVYAPiSg==", "type": "package", "path": "runtime.linux-arm.runtime.native.system.io.ports/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-arm.runtime.native.system.io.ports.7.0.0.nupkg.sha512", "runtime.linux-arm.runtime.native.system.io.ports.nuspec", "runtimes/linux-arm/native/libSystem.IO.Ports.Native.so", "useSharedDesignerContext.txt"]}, "runtime.linux-arm64.runtime.native.System.IO.Ports/7.0.0": {"sha512": "5VCyRCtCIYU8FR/W8oo7ouFuJ8tmAg9ddsuXhfCKZfZrbaVZSKxkmNBa6fxkfYPueD0jQfOvwFBmE5c6zalCSw==", "type": "package", "path": "runtime.linux-arm64.runtime.native.system.io.ports/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-arm64.runtime.native.system.io.ports.7.0.0.nupkg.sha512", "runtime.linux-arm64.runtime.native.system.io.ports.nuspec", "runtimes/linux-arm64/native/libSystem.IO.Ports.Native.so", "useSharedDesignerContext.txt"]}, "runtime.linux-x64.runtime.native.System.IO.Ports/7.0.0": {"sha512": "DV9dWDUs23OoZqMWl5IhLr3D+b9koDiSHQxFKdYgWnQbnthv8c/yDjrlrI8nMrDc71RAKCO8jlUojzuPMX04gg==", "type": "package", "path": "runtime.linux-x64.runtime.native.system.io.ports/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-x64.runtime.native.system.io.ports.7.0.0.nupkg.sha512", "runtime.linux-x64.runtime.native.system.io.ports.nuspec", "runtimes/linux-x64/native/libSystem.IO.Ports.Native.so", "useSharedDesignerContext.txt"]}, "runtime.native.System.IO.Ports/7.0.0": {"sha512": "L4Ivegqc3B0Fee7VifFy2JST9nndm+uvJ0viLIZUaImDfnr+JmRin9Tbqd56KuMtm0eVxHpNOWZBPtKrA/1h5Q==", "type": "package", "path": "runtime.native.system.io.ports/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "runtime.native.system.io.ports.7.0.0.nupkg.sha512", "runtime.native.system.io.ports.nuspec", "useSharedDesignerContext.txt"]}, "runtime.osx-arm64.runtime.native.System.IO.Ports/7.0.0": {"sha512": "jFwh4sKSXZ7al5XrItEO4GdGWa6XNxvNx+LhEHjrSzOwawO1znwJ+Dy+VjnrkySX9Qi4bnHNLoiqOXbqMuka4g==", "type": "package", "path": "runtime.osx-arm64.runtime.native.system.io.ports/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "runtime.osx-arm64.runtime.native.system.io.ports.7.0.0.nupkg.sha512", "runtime.osx-arm64.runtime.native.system.io.ports.nuspec", "runtimes/osx-arm64/native/libSystem.IO.Ports.Native.dylib", "useSharedDesignerContext.txt"]}, "runtime.osx-x64.runtime.native.System.IO.Ports/7.0.0": {"sha512": "X4LrHEfke/z9+z+iuVr35NlkhdZldY8JGNMYUN+sfPK/U/6TcE+vP44I0Yv0ir1v0bqIzq3v6Qdv1c1vmp8s4g==", "type": "package", "path": "runtime.osx-x64.runtime.native.system.io.ports/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "runtime.osx-x64.runtime.native.system.io.ports.7.0.0.nupkg.sha512", "runtime.osx-x64.runtime.native.system.io.ports.nuspec", "runtimes/osx-x64/native/libSystem.IO.Ports.Native.dylib", "useSharedDesignerContext.txt"]}, "System.CodeDom/8.0.0": {"sha512": "WTlRjL6KWIMr/pAaq3rYqh0TJlzpouaQ/W1eelssHgtlwHAH25jXTkUphTYx9HaIIf7XA6qs/0+YhtLEQRkJ+Q==", "type": "package", "path": "system.codedom/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.CodeDom.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.CodeDom.targets", "lib/net462/System.CodeDom.dll", "lib/net462/System.CodeDom.xml", "lib/net6.0/System.CodeDom.dll", "lib/net6.0/System.CodeDom.xml", "lib/net7.0/System.CodeDom.dll", "lib/net7.0/System.CodeDom.xml", "lib/net8.0/System.CodeDom.dll", "lib/net8.0/System.CodeDom.xml", "lib/netstandard2.0/System.CodeDom.dll", "lib/netstandard2.0/System.CodeDom.xml", "system.codedom.8.0.0.nupkg.sha512", "system.codedom.nuspec", "useSharedDesignerContext.txt"]}, "System.IO.Ports/7.0.0": {"sha512": "0nWQjM5IofaIGpvkifN+LLuYwBG6BHlpmphLhhOJepcW12G8qToGuNDRgBzeTVBZzp33wVsESSZ8hUOCfq+8QA==", "type": "package", "path": "system.io.ports/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.IO.Ports.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.IO.Ports.targets", "lib/net462/System.IO.Ports.dll", "lib/net462/System.IO.Ports.xml", "lib/net6.0/System.IO.Ports.dll", "lib/net6.0/System.IO.Ports.xml", "lib/net7.0/System.IO.Ports.dll", "lib/net7.0/System.IO.Ports.xml", "lib/netstandard2.0/System.IO.Ports.dll", "lib/netstandard2.0/System.IO.Ports.xml", "runtimes/unix/lib/net6.0/System.IO.Ports.dll", "runtimes/unix/lib/net6.0/System.IO.Ports.xml", "runtimes/unix/lib/net7.0/System.IO.Ports.dll", "runtimes/unix/lib/net7.0/System.IO.Ports.xml", "runtimes/win/lib/net6.0/System.IO.Ports.dll", "runtimes/win/lib/net6.0/System.IO.Ports.xml", "runtimes/win/lib/net7.0/System.IO.Ports.dll", "runtimes/win/lib/net7.0/System.IO.Ports.xml", "system.io.ports.7.0.0.nupkg.sha512", "system.io.ports.nuspec", "useSharedDesignerContext.txt"]}, "System.Management/8.0.0": {"sha512": "jrK22i5LRzxZCfGb+tGmke2VH7oE0DvcDlJ1HAKYU8cPmD8XnpUT0bYn2Gy98GEhGjtfbR/sxKTVb+dE770pfA==", "type": "package", "path": "system.management/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Management.targets", "lib/net462/_._", "lib/net6.0/System.Management.dll", "lib/net6.0/System.Management.xml", "lib/net7.0/System.Management.dll", "lib/net7.0/System.Management.xml", "lib/net8.0/System.Management.dll", "lib/net8.0/System.Management.xml", "lib/netstandard2.0/System.Management.dll", "lib/netstandard2.0/System.Management.xml", "runtimes/win/lib/net6.0/System.Management.dll", "runtimes/win/lib/net6.0/System.Management.xml", "runtimes/win/lib/net7.0/System.Management.dll", "runtimes/win/lib/net7.0/System.Management.xml", "runtimes/win/lib/net8.0/System.Management.dll", "runtimes/win/lib/net8.0/System.Management.xml", "system.management.8.0.0.nupkg.sha512", "system.management.nuspec", "useSharedDesignerContext.txt"]}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"sha512": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Runtime.CompilerServices.Unsafe.dll", "lib/net461/System.Runtime.CompilerServices.Unsafe.xml", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Encodings.Web/7.0.0": {"sha512": "OP6umVGxc0Z0MvZQBVigj4/U31Pw72ITihDWP9WiWDm+q5aoe0GaJivsfYGq53o6dxH7DcXWiCTl7+0o2CGdmg==", "type": "package", "path": "system.text.encodings.web/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Text.Encodings.Web.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Text.Encodings.Web.targets", "lib/net462/System.Text.Encodings.Web.dll", "lib/net462/System.Text.Encodings.Web.xml", "lib/net6.0/System.Text.Encodings.Web.dll", "lib/net6.0/System.Text.Encodings.Web.xml", "lib/net7.0/System.Text.Encodings.Web.dll", "lib/net7.0/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net7.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net7.0/System.Text.Encodings.Web.xml", "system.text.encodings.web.7.0.0.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Json/7.0.0": {"sha512": "DaGSsVqKsn/ia6RG8frjwmJonfos0srquhw09TlT8KRw5I43E+4gs+/bZj4K0vShJ5H9imCuXupb4RmS+dBy3w==", "type": "package", "path": "system.text.json/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net6.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net6.0/System.Text.Json.dll", "lib/net6.0/System.Text.Json.xml", "lib/net7.0/System.Text.Json.dll", "lib/net7.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.7.0.0.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}}, "projectFileDependencyGroups": {"net6.0": ["Microsoft.NET.ILLink.Analyzers >= 7.0.100-1.23211.1", "Microsoft.NET.ILLink.Tasks >= 7.0.100-1.23211.1", "System.IO.Ports >= 7.0.0", "System.Management >= 8.0.0", "System.Text.Json >= 7.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\recoil\\esp32-flasher\\OctaneFlasher\\OctaneFlasher.csproj", "projectName": "OctaneFlasher", "projectPath": "C:\\Users\\<USER>\\Desktop\\recoil\\esp32-flasher\\OctaneFlasher\\OctaneFlasher.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\recoil\\esp32-flasher\\OctaneFlasher\\obj\\publish\\win-x64\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.NET.ILLink.Analyzers": {"suppressParent": "All", "target": "Package", "version": "[7.0.100-1.23211.1, )", "autoReferenced": true}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[7.0.100-1.23211.1, )", "autoReferenced": true}, "System.IO.Ports": {"target": "Package", "version": "[7.0.0, )"}, "System.Management": {"target": "Package", "version": "[8.0.0, )"}, "System.Text.Json": {"target": "Package", "version": "[7.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.205\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}, "logs": [{"code": "NU1903", "level": "Warning", "warningLevel": 1, "message": "Package 'System.Text.Json' 7.0.0 has a known high severity vulnerability, https://github.com/advisories/GHSA-hh2w-p6rv-4g7w", "libraryId": "System.Text.Json", "targetGraphs": ["net6.0", "net6.0/win-x64"]}]}