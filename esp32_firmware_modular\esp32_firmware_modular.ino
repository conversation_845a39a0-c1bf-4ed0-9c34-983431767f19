/*
 * ESP32-S2 Enhanced HID Mouse Firmware - Octane Recoil Control System
 * Version: 2.1.0 - Modular Architecture Edition
 * Target: ESP32-S2 Mini (Lolin S2 Mini)
 *
 * Main Arduino sketch file - Entry point for the firmware
 * 
 * Features:
 * - Modular architecture with separated concerns
 * - USB HID Mouse emulation with advanced obfuscation
 * - Template-based precision recoil control
 * - Enhanced security and anti-detection
 * - Zero-delay command processing
 */

#include "src/config/firmware_config.h"
#include "src/hardware/hardware_manager.h"
#include "src/commands/command_processor.h"
#include "src/security/security_manager.h"
#include "src/recoil/recoil_engine.h"
#include "src/utils/system_info.h"

// Global managers
HardwareManager hardware;
CommandProcessor commandProcessor;
SecurityManager security;
RecoilEngine recoilEngine;
SystemInfo systemInfo;

void setup() {
    // Record boot time
    systemInfo.recordBootTime();
    
    // Initialize all subsystems in proper order
    Serial.println("🚀 Starting Octane ESP32-S2 Enhanced Firmware v" + String(FIRMWARE_VERSION));
    
    // 1. Hardware initialization (LED, USB, etc.)
    hardware.initialize();
    
    // 2. Security system initialization
    security.initialize();
    
    // 3. Command processing system
    commandProcessor.initialize();
    
    // 4. Recoil engine initialization
    recoilEngine.initialize();
    
    // 5. System information and diagnostics
    systemInfo.initialize();
    
    // Boot sequence complete
    hardware.setLEDState(LED_CONNECTED);
    Serial.println("🎯 Octane ESP32-S2 Enhanced v" + String(FIRMWARE_VERSION) + " Ready");
    Serial.println("🔐 Security Level: " + String(SECURITY_LEVEL));
    Serial.println("🆔 Device ID: " + security.getDeviceId());
}

void loop() {
    // ZERO DELAY OPTIMIZATION - Process commands immediately
    static unsigned long lastMicros = 0;
    unsigned long currentMicros = micros();
    
    // High-frequency processing for zero delay
    commandProcessor.processSerialCommands();
    commandProcessor.processCommandQueue();
    
    // Only update status every 1000 microseconds (1ms) to maintain performance
    if (currentMicros - lastMicros >= 1000) {
        hardware.updateConnectionStatus();
        security.performSecurityChecks();
        lastMicros = currentMicros;
    }
    
    // Anti-detection measures (only in stealth mode)
    if (security.isStealthModeEnabled()) {
        security.performAntiDetectionMeasures();
        delayMicroseconds(random(100, 500)); // Microsecond precision
    }
}
