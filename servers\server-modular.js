const express = require('express');
const sqlite3 = require('sqlite3').verbose();
const rateLimit = require('express-rate-limit');
const session = require('express-session');
const path = require('path');
const cors = require('cors');

// Import route modules
const authRoutes = require('./routes/auth');
const licensesRoutes = require('./routes/licenses');
const discordRoutes = require('./routes/discord');
const systemRoutes = require('./routes/system');
const { router: securityRoutes, logSecurityEvent } = require('./routes/security');

const app = express();
const PORT = process.env.PORT || 3000;

// Database setup
let db;
function initializeDatabase() {
    db = new sqlite3.Database('./octane.db', (err) => {
        if (err) {
            console.error('❌ Database connection error:', err.message);
            process.exit(1);
        }
        console.log('✅ Connected to SQLite database');
    });

    // Create tables if they don't exist
    db.serialize(() => {
        db.run(`CREATE TABLE IF NOT EXISTS licenses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            key TEXT UNIQUE NOT NULL,
            duration TEXT NOT NULL,
            is_active INTEGER DEFAULT 1,
            expires_at TEXT,
            hardware_id TEXT,
            notes TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )`);

        db.run(`CREATE TABLE IF NOT EXISTS security_events (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            type TEXT NOT NULL,
            severity TEXT NOT NULL,
            description TEXT NOT NULL,
            ip_address TEXT,
            timestamp TEXT DEFAULT CURRENT_TIMESTAMP
        )`);
    });

    // Make database available to routes
    app.locals.db = db;
}

// Middleware setup
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));
app.use(cors());

// Session configuration
app.use(session({
    secret: 'octane-auth-secret-key-change-in-production',
    resave: false,
    saveUninitialized: false,
    cookie: { 
        secure: false, // Set to true in production with HTTPS
        maxAge: 24 * 60 * 60 * 1000 // 24 hours
    }
}));

// Request logging middleware
app.use((req, res, next) => {
    const clientIP = req.headers['x-forwarded-for'] || 
                     req.headers['x-real-ip'] || 
                     req.connection.remoteAddress || 
                     req.socket.remoteAddress ||
                     (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
                     req.ip || 'unknown';
    
    const cleanIP = clientIP.split(',')[0].trim().replace(/^::ffff:/, '');
    
    console.log(`[${new Date().toISOString()}] ${req.method} ${req.path} - IP: ${cleanIP}`);
    next();
});

// Rate limiting configuration
const apiLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // Limit each IP to 100 requests per windowMs
    message: {
        success: false,
        error: 'Too many requests from this IP, please try again later.'
    },
    standardHeaders: true,
    legacyHeaders: false,
});

// Rate limiting bypass for authorized IP
const rateLimitBypass = (req, res, next) => {
    const clientIP = req.headers['x-forwarded-for'] || 
                     req.headers['x-real-ip'] || 
                     req.connection.remoteAddress || 
                     req.socket.remoteAddress ||
                     (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
                     req.ip || 'unknown';
    
    const cleanIP = clientIP.split(',')[0].trim().replace(/^::ffff:/, '');
    
    // Skip rate limiting for authorized IP
    if (cleanIP === '**************' || cleanIP === '127.0.0.1' || cleanIP === 'localhost') {
        return next();
    }
    
    // Apply rate limiting for other IPs
    return apiLimiter(req, res, next);
};

// IP restriction middleware for admin routes
const adminIPRestriction = (req, res, next) => {
    const allowedIP = '**************'; // Your IP address
    
    // Get real IP address (handle proxies and load balancers)
    const clientIP = req.headers['x-forwarded-for'] || 
                     req.headers['x-real-ip'] || 
                     req.connection.remoteAddress || 
                     req.socket.remoteAddress ||
                     (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
                     req.ip || 'unknown';
    
    // Clean up IP (remove IPv6 prefix if present and handle comma-separated IPs)
    const cleanIP = clientIP.split(',')[0].trim().replace(/^::ffff:/, '');
    
    console.log(`Admin access attempt from IP: ${cleanIP} (original: ${clientIP})`);
    
    if (cleanIP !== allowedIP && cleanIP !== '127.0.0.1' && cleanIP !== 'localhost') {
        console.log(`🚫 Admin access denied for IP: ${cleanIP}`);
        
        // Log security event
        if (db) {
            logSecurityEvent(db, cleanIP, 'UNAUTHORIZED_ADMIN_ACCESS', 
                           `Unauthorized admin access attempt from IP: ${cleanIP}`, 'warning');
        }
        
        return res.status(403).json({
            success: false,
            message: 'Access denied - Admin panel restricted to authorized IP addresses'
        });
    }
    
    console.log(`✅ Admin access granted for IP: ${cleanIP}`);
    next();
};

// Apply conditional rate limiting to API routes
app.use('/api', rateLimitBypass);

// Apply admin IP restriction to protected routes
app.use('/api/admin', adminIPRestriction);
app.use('/api/security', adminIPRestriction);
app.use('/api/discord', adminIPRestriction);
app.use('/api/system', adminIPRestriction);
app.use('/api/settings', adminIPRestriction);
app.use('/api/export', adminIPRestriction);

// Route handlers
app.use('/api/auth', authRoutes);
app.use('/api', licensesRoutes);
app.use('/api/discord', discordRoutes);
app.use('/api/system', systemRoutes);
app.use('/api/security', securityRoutes);

// Settings API endpoints
app.get('/api/settings', (req, res) => {
    // Mock settings (in production, load from database or config file)
    const settings = {
        general: {
            systemName: 'Octane Recoil Scripts',
            maxLicenses: 1000,
            defaultDuration: '1month'
        },
        security: {
            maxFailedAttempts: 5,
            sessionTimeout: 60,
            enableRateLimit: true,
            enableSecurityLogs: true,
            enableHWIDBinding: true
        },
        discord: {
            guildId: '**********',
            webhooks: {
                security: '1398506465882411079/49oCxzr04R0v7JwYhpxveww1x3_H6pW7VoDocPa6FYPaykMJJegkNddJjkqh2CsbYbKy',
                esp32: '1398551599818866719/JqAUxaYYv4V2o1ncPfZeB2TtloxU3roht5r4sT3S_7MQ4hN3m3AU3Ah8EAKCQIFxRDLW',
                backend: '1398551716869443654/FIcTJWf78O1reAqOMAM-1vLylabv_9QAe9LZ7EtHPB-QGsLcL55iJp04VcxcZx7Xv63i'
            }
        }
    };
    
    res.json({
        success: true,
        settings: settings
    });
});

app.post('/api/settings/general', (req, res) => {
    const { systemName, maxLicenses, defaultDuration } = req.body;
    
    // In production, save to database or config file
    console.log('💾 General settings updated:', { systemName, maxLicenses, defaultDuration });
    
    res.json({
        success: true,
        message: 'General settings saved successfully'
    });
});

app.post('/api/settings/security', (req, res) => {
    const settings = req.body;
    
    // In production, save to database or config file
    console.log('🔐 Security settings updated:', settings);
    
    res.json({
        success: true,
        message: 'Security settings saved successfully'
    });
});

app.post('/api/settings/discord', (req, res) => {
    const settings = req.body;
    
    // In production, save to database or config file and restart Discord bot
    console.log('🤖 Discord settings updated:', settings);
    
    res.json({
        success: true,
        message: 'Discord settings saved successfully'
    });
});

// Export endpoints
app.get('/api/export/licenses', (req, res) => {
    db.all('SELECT * FROM licenses', (err, rows) => {
        if (err) {
            return res.status(500).json({ success: false, error: 'Export failed' });
        }
        
        res.setHeader('Content-Type', 'application/json');
        res.setHeader('Content-Disposition', 'attachment; filename=licenses-export.json');
        res.json(rows);
    });
});

app.get('/api/export/security-logs', (req, res) => {
    db.all('SELECT * FROM security_events', (err, rows) => {
        if (err) {
            return res.status(500).json({ success: false, error: 'Export failed' });
        }
        
        res.setHeader('Content-Type', 'application/json');
        res.setHeader('Content-Disposition', 'attachment; filename=security-logs-export.json');
        res.json(rows);
    });
});

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

// Routes
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.get('/admin', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'admin.html'));
});

app.get('/key-maintenance', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'key-maintenance.html'));
});

app.get('/user-management', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'user-management.html'));
});

app.get('/discord', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'discord.html'));
});

app.get('/security-alerts', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'security-alerts.html'));
});

app.get('/system-status', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'system-status.html'));
});

app.get('/settings', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'settings.html'));
});

app.get('/reminders', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'reminders.html'));
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Server error:', err);
    res.status(500).json({
        success: false,
        error: 'Internal server error'
    });
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({
        success: false,
        error: 'Endpoint not found'
    });
});

// Start server
function startServer() {
    initializeDatabase();
    
    app.listen(PORT, () => {
        console.log(`🚀 Octane Auth Server running on port ${PORT}`);
        console.log(`🌐 Access at: http://localhost:${PORT}`);
        console.log(`🔒 Admin access restricted to: **************`);
        console.log(`📊 Health check: http://localhost:${PORT}/health`);
    });
}

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down server...');
    if (db) {
        db.close((err) => {
            if (err) {
                console.error('Error closing database:', err.message);
            } else {
                console.log('✅ Database connection closed');
            }
            process.exit(0);
        });
    } else {
        process.exit(0);
    }
});

startServer();
