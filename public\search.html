<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search - Octane Authentication</title>
    <link rel="stylesheet" href="css/main-theme.css">
    <link rel="stylesheet" href="css/search.css">
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <h1>🔍 Search</h1>
                    <p>Comprehensive data lookup</p>
                </div>
                        <div class="nav-buttons">
                        <a href="/key-maintenance" class="btn btn-primary">🔧 Key Maintenance</a>
                        <a href="/user-management" class="btn btn-primary">👥 User Management</a>
                        <a href="/discord" class="btn btn-primary">🤖 Discord Management</a>
                        <a href="/security-alerts" class="btn btn-primary">🛡️ Security Alerts</a>
                        <a href="/system-status" class="btn btn-primary">📊 System Status</a>
                        <a href="/settings" class="btn btn-primary">⚙️ Settings</a>
                        <a href="/reminders" class="btn btn-primary">📋 Reminders</a>
                        <a href="/search" class="btn btn-primary">🔍 search</a>
                        <button id="logoutBtn" class="btn btn-danger">🚪 Logout</button>
                </nav>
            </div>
        </header>

        <main class="main-content">
            <div class="search-container">
                <div class="search-header">
                    <h2>🔍 Comprehensive Search</h2>
                    <p>Search for hardware IDs, IP addresses, license keys, and more</p>
                </div>

                <div class="search-form">
                    <div class="search-input-group">
                        <input type="text" id="searchQuery" placeholder="Enter hardware ID, IP address, license key, or any identifier..." class="search-input">
                        <select id="searchType" class="search-type">
                            <option value="all">All Data</option>
                            <option value="hardware_id">Hardware ID</option>
                            <option value="ip_address">IP Address</option>
                            <option value="license_key">License Key</option>
                            <option value="security">Security Events</option>
                        </select>
                        <button onclick="performSearch()" class="search-btn">🔍 Search</button>
                    </div>
                </div>

                <div id="searchResults" class="search-results" style="display: none;">
                    <div class="results-summary">
                        <h3 id="resultsSummary">Search Results</h3>
                    </div>

                    <div class="results-tabs">
                        <button class="tab-btn active" onclick="showTab('licenses')">📋 Licenses</button>
                        <button class="tab-btn" onclick="showTab('security')">🛡️ Security Events</button>
                        <button class="tab-btn" onclick="showTab('overview')">📊 Overview</button>
                    </div>

                    <div id="licensesTab" class="tab-content active">
                        <div class="results-section">
                            <h4>📋 License Information</h4>
                            <div id="licensesResults" class="results-list">
                                <!-- License results will be populated here -->
                            </div>
                        </div>
                    </div>

                    <div id="securityTab" class="tab-content">
                        <div class="results-section">
                            <h4>🛡️ Security Events</h4>
                            <div id="securityResults" class="results-list">
                                <!-- Security events will be populated here -->
                            </div>
                        </div>
                    </div>

                    <div id="overviewTab" class="tab-content">
                        <div class="results-section">
                            <h4>📊 Search Overview</h4>
                            <div id="overviewResults" class="overview-grid">
                                <!-- Overview will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>

                <div id="loadingIndicator" class="loading" style="display: none;">
                    <div class="loading-spinner"></div>
                    <p>Searching...</p>
                </div>

                <div id="noResults" class="no-results" style="display: none;">
                    <div class="no-results-icon">🔍</div>
                    <h3>No Results Found</h3>
                    <p>Try searching with different keywords or check your spelling.</p>
                </div>
            </div>
        </main>
    </div>

    <script src="js/shared-utils.js"></script>
    <script src="js/search.js"></script>
</body>
</html>
