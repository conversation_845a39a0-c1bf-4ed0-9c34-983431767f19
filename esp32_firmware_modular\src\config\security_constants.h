/*
 * Security Constants and Cryptographic Keys
 * Contains all security-related constants, keys, and obfuscation data
 */

#ifndef SECURITY_CONSTANTS_H
#define SECURITY_CONSTANTS_H

#include <Arduino.h>

// ===== SECURITY CONSTANTS =====
const uint8_t DEVICE_SALT[] = {
    0x4F, 0x63, 0x74, 0x61, 0x6E, 0x65, 0x52, 0x65,  // "OctaneRe"
    0x63, 0x6F, 0x69, 0x6C, 0x53, 0x79, 0x73, 0x74,  // "coilSyst"
    0x65, 0x6D, 0x32, 0x30, 0x32, 0x34, 0x56, 0x32   // "em2024V2"
};

const uint8_t IDENTIFICATION_KEY[] = {
    0xA7, 0x3B, 0x9F, 0x2E, 0x8D, 0x4C, 0x1A, 0x6B,
    0x5E, 0x7F, 0x3C, 0x8A, 0x9D, 0x2B, 0x4E, 0x7C,
    0x1F, 0x6A, 0x8E, 0x3D, 0x5B, 0x7A, 0x9C, 0x2F,
    0x4D, 0x6E, 0x8B, 0x1C, 0x5A, 0x7D, 0x9E, 0x3F
};

const uint8_t OBFUSCATION_MATRIX[] = {
    0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0,
    0x21, 0x43, 0x65, 0x87, 0xA9, 0xCB, 0xED, 0x0F,
    0x13, 0x24, 0x57, 0x68, 0x9B, 0xAC, 0xDF, 0xE0,
    0x31, 0x42, 0x75, 0x86, 0xB9, 0xCA, 0xFD, 0x0E
};

const uint8_t ENTROPY_SEEDS[] = {
    0xAA, 0x55, 0xCC, 0x33, 0xFF, 0x00, 0x88, 0x77,
    0x11, 0xEE, 0x22, 0xDD, 0x44, 0xBB, 0x66, 0x99
};

// ===== ANTI-DETECTION PATTERNS =====
const uint16_t STEALTH_PATTERNS[] = {
    1000, 1100, 950, 1200, 800, 1300, 900, 1150,
    1050, 1250, 850, 1350, 750, 1400, 1000, 1100
};

const uint8_t BEHAVIOR_MORPHING_SEEDS[] = {
    0x7A, 0x8B, 0x9C, 0xAD, 0xBE, 0xCF, 0xD0, 0xE1,
    0xF2, 0x03, 0x14, 0x25, 0x36, 0x47, 0x58, 0x69
};

// ===== DEVICE FINGERPRINTING =====
const char* DEVICE_SIGNATURES[] = {
    "ESP32S2_OCTANE_ENHANCED",
    "HID_MOUSE_CONTROLLER_V2",
    "RECOIL_COMPENSATION_SYS",
    "GAMING_PERIPHERAL_DEV",
    "USB_INPUT_DEVICE_ADV"
};

const uint32_t SIGNATURE_ROTATION_INTERVALS[] = {
    300000,  // 5 minutes
    600000,  // 10 minutes
    900000,  // 15 minutes
    1200000, // 20 minutes
    1800000  // 30 minutes
};

// ===== SECURITY VALIDATION CHECKSUMS =====
const uint32_t FIRMWARE_CHECKSUM = 0x12345678;
const uint32_t CONFIG_CHECKSUM = 0x87654321;
const uint32_t SECURITY_CHECKSUM = 0xABCDEF00;

// ===== ANTI-TAMPERING CONSTANTS =====
const uint8_t INTEGRITY_MARKERS[] = {
    0xDE, 0xAD, 0xBE, 0xEF, 0xCA, 0xFE, 0xBA, 0xBE,
    0xFE, 0xED, 0xFA, 0xCE, 0xD0, 0x0D, 0xF0, 0x0D
};

const uint16_t SECURITY_INTERVALS[] = {
    5000,   // Normal security check interval
    2000,   // Elevated security check interval
    1000,   // High security check interval
    500,    // Maximum security check interval
    100     // Emergency security check interval
};

// ===== STEALTH MODE CONFIGURATIONS =====
const struct {
    uint16_t response_delay_min;
    uint16_t response_delay_max;
    uint8_t led_brightness;
    bool serial_responses_enabled;
    bool status_reports_enabled;
} STEALTH_CONFIGS[] = {
    {100, 300, 255, true, true},    // Normal mode
    {200, 500, 128, true, false},   // Light stealth
    {500, 1000, 64, false, false},  // Medium stealth
    {1000, 2000, 32, false, false}, // Heavy stealth
    {2000, 5000, 0, false, false}   // Deep stealth
};

// ===== ENCRYPTION KEYS (Production would use secure key storage) =====
const uint8_t AES_KEY[] = {
    0x2B, 0x7E, 0x15, 0x16, 0x28, 0xAE, 0xD2, 0xA6,
    0xAB, 0xF7, 0x15, 0x88, 0x09, 0xCF, 0x4F, 0x3C,
    0x2B, 0x7E, 0x15, 0x16, 0x28, 0xAE, 0xD2, 0xA6,
    0xAB, 0xF7, 0x15, 0x88, 0x09, 0xCF, 0x4F, 0x3C
};

const uint8_t IV_SEED[] = {
    0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
    0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F
};

// ===== SECURITY RESPONSE CODES =====
#define SECURITY_OK                 0x00
#define SECURITY_WARNING            0x01
#define SECURITY_ALERT              0x02
#define SECURITY_BREACH             0x03
#define SECURITY_LOCKDOWN           0x04
#define SECURITY_TAMPER_DETECTED    0x05
#define SECURITY_INVALID_COMMAND    0x06
#define SECURITY_RATE_LIMITED       0x07
#define SECURITY_UNAUTHORIZED       0x08
#define SECURITY_SYSTEM_ERROR       0x09

// ===== ANTI-DETECTION THRESHOLDS =====
#define DETECTION_THRESHOLD_LOW     10
#define DETECTION_THRESHOLD_MEDIUM  25
#define DETECTION_THRESHOLD_HIGH    50
#define DETECTION_THRESHOLD_CRITICAL 100

#endif // SECURITY_CONSTANTS_H
