using System;
using System.Linq;
using System.Text;

namespace OctaneFlasher.Communication
{
    /// <summary>
    /// Represents captured packet data for monitoring and analysis
    /// </summary>
    public class PacketData
    {
        public DateTime Timestamp { get; set; }
        public string Direction { get; set; } // "IN" or "OUT"
        public byte[] Data { get; set; }
        public int Length { get; set; }
        public string HexString { get; set; }
        public string AsciiString { get; set; }
        
        public PacketData(byte[] data, string direction)
        {
            Timestamp = DateTime.Now;
            Direction = direction;
            Data = data;
            Length = data.Length;
            HexString = BitConverter.ToString(data).Replace("-", " ");
            AsciiString = Encoding.ASCII.GetString(data.Select(b => b >= 32 && b <= 126 ? b : (byte)'.').ToArray());
        }

        /// <summary>
        /// Format packet for Wireshark-like display
        /// </summary>
        public string ToWiresharkFormat()
        {
            var timeStr = Timestamp.ToString("HH:mm:ss.fff");
            var hexStr = HexString.Length > 30 ? HexString.Substring(0, 30) + "..." : HexString.PadRight(30);
            var asciiStr = AsciiString.Length > 10 ? AsciiString.Substring(0, 10) + "..." : AsciiString.PadRight(10);
            
            return $"{timeStr} | {Direction,3} | {Length,3} | {hexStr} | {asciiStr}";
        }

        /// <summary>
        /// Format packet for debug display
        /// </summary>
        public string ToDebugFormat()
        {
            return $"[{Timestamp:HH:mm:ss.fff}] {Direction} ({Length} bytes): {HexString}";
        }
    }
}
