<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <AssemblyTitle>Octane ESP32 Flasher</AssemblyTitle>
    <AssemblyDescription>Professional ESP32 firmware flashing tool</AssemblyDescription>
    <AssemblyCompany>Octane Team</AssemblyCompany>
    <AssemblyProduct>Octane ESP32 Flasher</AssemblyProduct>
    <AssemblyCopyright>Copyright © 2024 Octane Team. All rights reserved.</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>

    <!-- Production Security Settings -->
    <PublishSingleFile>true</PublishSingleFile>
    <SelfContained>true</SelfContained>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <PublishReadyToRun>false</PublishReadyToRun>
    <PublishTrimmed>false</PublishTrimmed>
    <EnableCompressionInSingleFile>true</EnableCompressionInSingleFile>
    <IncludeNativeLibrariesForSelfContained>true</IncludeNativeLibrariesForSelfContained>
  </PropertyGroup>

  <!-- Release Configuration for Security -->
  <PropertyGroup Condition="'$(Configuration)'=='Release'">
    <Optimize>true</Optimize>
    <DebugType>none</DebugType>
    <DebugSymbols>false</DebugSymbols>
    <DefineConstants>RELEASE;PRODUCTION</DefineConstants>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.IO.Ports" Version="7.0.0" />
    <PackageReference Include="System.Text.Json" Version="7.0.0" />
    <PackageReference Include="System.Management" Version="9.0.7" />
  </ItemGroup>

  <!-- Include files in the single file bundle -->
  <ItemGroup>
    <Content Include="tools\esptool.exe">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <IncludeInSingleFile>true</IncludeInSingleFile>
    </Content>
    <!-- Firmware files are now external - not embedded in the executable -->
  </ItemGroup>

</Project>
