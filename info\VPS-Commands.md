# VPS Custom Commands Documentation

## SSH Connection
```bash
ssh -i "C:\Users\<USER>\Desktop\recoil\auth-backend\deploy\octane_key" root@*************
```

## Application Management

### PM2 Process Management
```bash
# Check application status
sudo -u octane pm2 status

# Restart the application
sudo -u octane pm2 restart octane-auth

# View application logs
sudo -u octane pm2 logs octane-auth

# Monitor application in real-time
sudo -u octane pm2 monit

# Save PM2 configuration
sudo -u octane pm2 save

# Start application
sudo -u octane pm2 start server.js --name octane-auth

# Stop application
sudo -u octane pm2 stop octane-auth

# Delete application from PM2
sudo -u octane pm2 delete octane-auth
```

### Service Management
```bash
# Check nginx status
systemctl status nginx

# Restart nginx
systemctl restart nginx

# Reload nginx configuration
systemctl reload nginx

# Test nginx configuration
nginx -t

# Check PM2 service
systemctl status pm2-octane
```

## Database Management

### Database Operations
```bash
# Navigate to application directory
cd /opt/octane-auth

# Backup database manually
cp octane.db backups/octane_backup_$(date +%Y%m%d_%H%M%S).db

# View database size
ls -lh octane.db

# Check database integrity (if sqlite3 is installed)
sqlite3 octane.db "PRAGMA integrity_check;"
```

### Automated Backup Script
```bash
# Run backup script
./scripts/backup-database.sh

# View backup files
ls -la backups/
```

## System Monitoring

### Resource Monitoring
```bash
# Check memory usage
free -h

# Check disk usage
df -h

# Check disk usage by directory
du -sh /opt/octane-auth/*

# Monitor system resources
htop

# Check running processes
ps aux | grep -E "(node|nginx|pm2)"
```

### Custom Monitoring Script
```bash
# Run memory monitoring (restarts app if memory > 85%)
./monitor.sh

# Check system status
./test-complete-system.sh
```

## File Management

### Upload Files to VPS
```bash
# Upload single file
scp -i "C:\Users\<USER>\Desktop\recoil\auth-backend\deploy\octane_key" "local_file.html" root@*************:/opt/octane-auth/public/

# Upload directory
scp -i "C:\Users\<USER>\Desktop\recoil\auth-backend\deploy\octane_key" -r "local_directory" root@*************:/opt/octane-auth/

# Upload with specific permissions
scp -i "C:\Users\<USER>\Desktop\recoil\auth-backend\deploy\octane_key" "file.js" root@*************:/opt/octane-auth/public/js/
ssh -i "C:\Users\<USER>\Desktop\recoil\auth-backend\deploy\octane_key" root@************* "chown octane:octane /opt/octane-auth/public/js/file.js"
```

### File Permissions
```bash
# Fix ownership for application files
chown -R octane:octane /opt/octane-auth

# Set proper permissions for web files
chmod 755 /opt/octane-auth/public/*.html
chmod 755 /opt/octane-auth/public/js/*.js
chmod 755 /opt/octane-auth/public/css/*.css

# Make scripts executable
chmod +x /opt/octane-auth/*.sh
chmod +x /opt/octane-auth/scripts/*.sh
```

## Environment Management

### Environment Variables
```bash
# View environment (excluding sensitive data)
cd /opt/octane-auth
cat .env | grep -v 'PASSWORD\|SECRET\|TOKEN'

# Edit environment file
nano .env

# Restart application after env changes
sudo -u octane pm2 restart octane-auth
```

### Node.js Management
```bash
# Check Node.js version
node --version

# Check npm version
npm --version

# Install dependencies
cd /opt/octane-auth
npm install --production

# Update dependencies
npm update
```

## Deployment Commands

### Quick Deployment
```bash
# From local machine - run deployment script
C:\Users\<USER>\Desktop\recoil\auth-backend\deploy\deploy-fixed.bat
```

### Manual Deployment Steps
```bash
# 1. Upload files
scp -i "deploy\octane_key" -r "public" root@*************:/opt/octane-auth/
scp -i "deploy\octane_key" -r "routes" root@*************:/opt/octane-auth/
scp -i "deploy\octane_key" "server.js" root@*************:/opt/octane-auth/

# 2. Fix permissions
ssh -i "deploy\octane_key" root@************* "chown -R octane:octane /opt/octane-auth"

# 3. Install dependencies
ssh -i "deploy\octane_key" root@************* "cd /opt/octane-auth && npm install --production"

# 4. Restart application
ssh -i "deploy\octane_key" root@************* "sudo -u octane pm2 restart octane-auth"
```

## Firmware Management

### ESP32 Firmware Operations
```bash
# Check firmware directory
ls -la /opt/octane-auth/firmware/esp32s2_enhanced/

# Upload firmware from local
scp -i "deploy\octane_key" "firmware.bin" root@*************:/opt/octane-auth/public/firmware/

# Set firmware permissions
chmod 644 /opt/octane-auth/public/firmware/*.bin
chown octane:octane /opt/octane-auth/public/firmware/*.bin
```

## Troubleshooting Commands

### Application Issues
```bash
# Check application logs
sudo -u octane pm2 logs octane-auth --lines 50

# Check if application is responding
curl -s http://localhost:3000/api/health

# Check external access
curl -s http://*************/api/health

# Restart everything
sudo -u octane pm2 restart octane-auth
systemctl restart nginx
```

### System Issues
```bash
# Check system logs
journalctl -u nginx -f
journalctl -u pm2-octane -f

# Check network connectivity
ping google.com
netstat -tlnp | grep :3000
netstat -tlnp | grep :80

# Check firewall status
ufw status
```

### Disk Space Issues (CRITICAL - 95% full)
```bash
# Find large files
find /opt/octane-auth -type f -size +10M -ls

# Clean old logs
sudo -u octane pm2 flush

# Clean old backups (keep last 3)
cd /opt/octane-auth/backups
ls -t octane_backup_*.json | tail -n +4 | xargs rm -f

# Clean npm cache
npm cache clean --force

# Remove old node_modules and reinstall
cd /opt/octane-auth
rm -rf node_modules
npm install --production
```

## Security Commands

### Check Security Status
```bash
# Check failed login attempts
grep "Failed password" /var/log/auth.log | tail -10

# Check current connections
ss -tuln

# Check firewall rules
ufw status verbose

# Check running services
systemctl list-units --type=service --state=active
```

### Update System
```bash
# Update package lists
apt update

# Upgrade packages (be careful with limited disk space)
apt upgrade

# Clean package cache
apt autoremove
apt autoclean
```

## Emergency Commands

### If Application Won't Start
```bash
# Kill all node processes
pkill -f node

# Start fresh
cd /opt/octane-auth
sudo -u octane pm2 start server.js --name octane-auth

# If PM2 is corrupted
sudo -u octane pm2 kill
sudo -u octane pm2 start server.js --name octane-auth
sudo -u octane pm2 save
```

### If System is Unresponsive
```bash
# Check system load
uptime

# Check memory usage
cat /proc/meminfo

# Emergency restart (last resort)
reboot
```

## Useful Aliases (Add to ~/.bashrc)
```bash
alias octane-status='sudo -u octane pm2 status'
alias octane-logs='sudo -u octane pm2 logs octane-auth'
alias octane-restart='sudo -u octane pm2 restart octane-auth'
alias octane-dir='cd /opt/octane-auth'
alias check-space='df -h'
alias check-mem='free -h'
```
