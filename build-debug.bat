@echo off
echo ========================================
echo    OCTANE RECOIL SCRIPTS - DEBUG BUILD
echo ========================================
echo.

echo [1/4] Cleaning previous builds...
dotnet clean RecoilController --configuration Debug --verbosity quiet
if errorlevel 1 (
    echo ❌ Clean failed!
    pause
    exit /b 1
)

echo [2/4] Restoring packages...
dotnet restore RecoilController --verbosity quiet
if errorlevel 1 (
    echo ❌ Restore failed!
    pause
    exit /b 1
)

echo [3/4] Building DEBUG configuration...
dotnet build RecoilController --configuration Debug --verbosity minimal
if errorlevel 1 (
    echo ❌ Build failed!
    pause
    exit /b 1
)

echo [4/4] Publishing DEBUG build...
dotnet publish RecoilController --configuration Debug --output "builds\debug" --verbosity minimal
if errorlevel 1 (
    echo ❌ Publish failed!
    pause
    exit /b 1
)

echo.
echo ✅ DEBUG BUILD COMPLETED SUCCESSFULLY!
echo.
echo 📁 Output: builds\debug\
echo 🔧 Features enabled:
echo    - Developer tools (F12)
echo    - Console logging
echo    - Text selection allowed
echo    - Right-click context menu
echo    - Detailed error messages
echo.
echo ⚠️  WARNING: This is a DEBUG build - NOT for distribution!
echo.
pause
