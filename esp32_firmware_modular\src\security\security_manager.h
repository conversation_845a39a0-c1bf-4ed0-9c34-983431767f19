/*
 * Security Manager Header
 * Handles all security, anti-detection, and device identification features
 */

#ifndef SECURITY_MANAGER_H
#define SECURITY_MANAGER_H

#include <Arduino.h>
#include <esp_system.h>
#include <esp_random.h>
#include "../config/firmware_config.h"
#include "../config/command_types.h"
#include "../config/security_constants.h"

class SecurityManager {
private:
    // Device identification
    String deviceUniqueId;
    String deviceSignature;
    String obfuscatedIdentifier;
    uint8_t deviceFingerprint[32];
    
    // Security state
    SecurityState currentSecurityState;
    SecurityConfig securityConfig;
    bool stealthModeEnabled;
    bool deepStealthEnabled;
    bool antiDetectionEnabled;
    
    // Anti-detection variables
    unsigned long lastAntiDetectionCheck;
    unsigned long lastSecurityAudit;
    uint8_t detectionCounter;
    uint16_t behaviorPattern;
    uint32_t stealthInterval;
    
    // Security metrics
    uint32_t securityEvents;
    uint32_t detectionAttempts;
    uint32_t stealthActivations;
    uint32_t auditsPassed;
    uint32_t auditsFailed;
    
    // Entropy and randomization
    uint32_t entropyPool[16];
    uint8_t entropyIndex;
    
    // Device identification methods
    String generateDeviceId();
    String createDeviceFingerprint();
    String generateObfuscatedSignature(const String& baseId);
    void collectSystemEntropy();
    uint32_t generateSecureRandom();
    
    // Anti-detection methods
    void performAntiDetectionChecks();
    void randomizeBehaviorPatterns();
    void morphBehaviorPattern();
    void activateCountermeasures();
    
    // Security validation
    bool validateSecurityIntegrity();
    bool verifyDeviceSignature();
    bool checkTamperEvidence();
    
public:
    SecurityManager();
    
    // Initialization
    bool initialize();
    void initializeSecureIdentification();
    void resetSecurityPatterns();
    
    // Device identification
    String getDeviceId() const;
    String getDeviceSignature() const;
    String getObfuscatedIdentifier() const;
    void regenerateIdentifiers();
    
    // Security state management
    void setSecurityState(SecurityState state);
    SecurityState getSecurityState() const;
    bool isStealthModeEnabled() const;
    bool isDeepStealthEnabled() const;
    
    // Stealth mode control
    void enableStealthMode(bool enabled);
    void activateDeepStealth();
    void activateEnhancedStealth();
    void deactivateAllStealth();
    
    // Anti-detection
    void performAntiDetectionMeasures();
    void performSecurityChecks();
    void performSecurityAudit();
    bool detectTamperingAttempt();
    
    // Security configuration
    void setSecurityConfig(const SecurityConfig& config);
    SecurityConfig getSecurityConfig() const;
    void enableAntiDetection(bool enabled);
    
    // Security responses
    void handleSecurityAlert(uint8_t alertLevel);
    void handleSecurityBreach();
    void handleTamperDetection();
    void emergencyLockdown();
    
    // Metrics and monitoring
    uint32_t getSecurityEvents() const;
    uint32_t getDetectionAttempts() const;
    uint32_t getStealthActivations() const;
    float getSecurityScore() const;
    
    // Validation and authorization
    bool validateCommand(uint8_t commandType);
    bool authorizeAccess(const String& token);
    bool verifyIntegrity();
    
    // Obfuscation and encryption
    String obfuscateData(const String& data);
    String deobfuscateData(const String& obfuscatedData);
    void rotateEncryptionKeys();
    
    // Debug and diagnostics
    void printSecurityStatus();
    void printDeviceFingerprint();
    void enableSecurityDebug(bool enabled);
    
    // Emergency methods
    void wipeSecurityData();
    void factoryReset();
    void selfDestruct();
};

#endif // SECURITY_MANAGER_H
