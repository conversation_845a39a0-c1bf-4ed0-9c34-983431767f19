# PlatformIO
.pio/
.vscode/
.pioenvs/
.piolibdeps/
.clang_complete
.gcc-flags.json

# Build artifacts
*.bin
*.elf
*.map
*.hex

# IDE files
*.code-workspace
.vscode/
.idea/
*.sublime-*

# OS files
.DS_Store
Thumbs.db
*.tmp
*.temp

# Logs
*.log
serial_output.txt

# Backup files
*.bak
*.backup
*~

# Security sensitive files (if any)
secrets.h
private_keys.h

# Test files
test_output/
test_results/

# Documentation build
docs/_build/
docs/html/
docs/latex/

# Python
__pycache__/
*.pyc
*.pyo

# Node.js (if using for build tools)
node_modules/
npm-debug.log

# Temporary files
*.swp
*.swo
*~
