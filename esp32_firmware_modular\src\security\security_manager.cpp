/*
 * Security Manager Implementation
 * Handles all security, anti-detection, and device identification features
 */

#include "security_manager.h"
#include <esp_efuse.h>
#include <esp_mac.h>

SecurityManager::SecurityManager() {
    currentSecurityState = SECURITY_NORMAL;
    stealthModeEnabled = false;
    deepStealthEnabled = false;
    antiDetectionEnabled = true;
    lastAntiDetectionCheck = 0;
    lastSecurityAudit = 0;
    detectionCounter = 0;
    behaviorPattern = 0;
    stealthInterval = 1000;
    securityEvents = 0;
    detectionAttempts = 0;
    stealthActivations = 0;
    auditsPassed = 0;
    auditsFailed = 0;
    entropyIndex = 0;
    
    // Initialize default security config
    securityConfig.anti_detection_enabled = true;
    securityConfig.stealth_mode_enabled = false;
    securityConfig.deep_stealth_enabled = false;
    securityConfig.detection_sensitivity = DETECTION_THRESHOLD_MEDIUM;
    securityConfig.stealth_interval_ms = 1000;
    securityConfig.obfuscation_level = 5;
    securityConfig.audit_enabled = true;
    
    // Initialize entropy pool
    memset(entropyPool, 0, sizeof(entropyPool));
    memset(deviceFingerprint, 0, sizeof(deviceFingerprint));
}

bool SecurityManager::initialize() {
    Serial.println("🔐 Initializing Security Manager...");
    
    // Initialize secure identification
    initializeSecureIdentification();
    
    // Collect initial entropy
    collectSystemEntropy();
    
    // Generate device identifiers
    deviceUniqueId = generateDeviceId();
    deviceSignature = generateObfuscatedSignature(deviceUniqueId);
    obfuscatedIdentifier = createDeviceFingerprint();
    
    // Perform initial security audit
    if (performSecurityAudit()) {
        auditsPassed++;
    } else {
        auditsFailed++;
    }
    
    Serial.println("✅ Security Manager initialized successfully");
    Serial.println("🆔 Device ID: " + deviceUniqueId);
    Serial.println("🔒 Security Level: " + String(SECURITY_LEVEL));
    
    return true;
}

void SecurityManager::initializeSecureIdentification() {
    Serial.println("🔐 Initializing secure identification system...");
    
    // Generate unique device ID based on hardware characteristics
    uint8_t mac[6];
    esp_read_mac(mac, ESP_MAC_WIFI_STA);
    
    // Combine multiple hardware identifiers for uniqueness
    String baseId = "";
    for (int i = 0; i < 6; i++) {
        baseId += String(mac[i], HEX);
    }
    
    // Add chip ID and flash size for additional uniqueness
    uint32_t chipId = ESP.getEfuseMac();
    baseId += String(chipId, HEX);
    baseId += String(ESP.getFlashChipSize(), HEX);
    
    deviceUniqueId = baseId;
    
    Serial.println("✅ Secure identification initialized");
}

String SecurityManager::generateDeviceId() {
    String id = "ESP32S2_OCTANE_";
    
    // Add MAC address
    uint8_t mac[6];
    esp_read_mac(mac, ESP_MAC_WIFI_STA);
    for (int i = 0; i < 6; i++) {
        if (mac[i] < 16) id += "0";
        id += String(mac[i], HEX);
    }
    
    // Add chip revision and model
    id += "_REV" + String(ESP.getChipRevision());
    id += "_" + String(ESP.getChipModel());
    
    return id.toUpperCase();
}

String SecurityManager::createDeviceFingerprint() {
    String fingerprint = "";
    
    // Collect various system characteristics
    fingerprint += String(ESP.getCpuFreqMHz());
    fingerprint += String(ESP.getFlashChipSize());
    fingerprint += String(ESP.getFlashChipSpeed());
    fingerprint += String(ESP.getFreeHeap());
    fingerprint += String(ESP.getChipRevision());
    
    // Add some obfuscation
    for (int i = 0; i < fingerprint.length(); i++) {
        fingerprint[i] ^= OBFUSCATION_MATRIX[i % 32];
    }
    
    return fingerprint;
}

String SecurityManager::generateObfuscatedSignature(const String& baseId) {
    String signature = baseId;
    
    // Apply multiple rounds of obfuscation
    for (int round = 0; round < OBFUSCATION_ROUNDS; round++) {
        for (int i = 0; i < signature.length(); i++) {
            signature[i] ^= DEVICE_SALT[i % sizeof(DEVICE_SALT)];
            signature[i] = (signature[i] + ENTROPY_SEEDS[round % sizeof(ENTROPY_SEEDS)]) % 256;
        }
    }
    
    return signature;
}

void SecurityManager::collectSystemEntropy() {
    // Collect entropy from various system sources
    entropyPool[entropyIndex % 16] = esp_random();
    entropyPool[(entropyIndex + 1) % 16] = millis();
    entropyPool[(entropyIndex + 2) % 16] = micros();
    entropyPool[(entropyIndex + 3) % 16] = ESP.getFreeHeap();
    
    entropyIndex = (entropyIndex + 4) % 16;
}

uint32_t SecurityManager::generateSecureRandom() {
    collectSystemEntropy();
    
    uint32_t result = 0;
    for (int i = 0; i < 16; i++) {
        result ^= entropyPool[i];
    }
    
    return result ^ esp_random();
}

void SecurityManager::enableStealthMode(bool enabled) {
    if (enabled && !stealthModeEnabled) {
        stealthModeEnabled = true;
        stealthActivations++;
        setSecurityState(SECURITY_STEALTH);
        Serial.println("🥷 Stealth mode activated");
    } else if (!enabled && stealthModeEnabled) {
        stealthModeEnabled = false;
        setSecurityState(SECURITY_NORMAL);
        Serial.println("👁️ Stealth mode deactivated");
    }
}

void SecurityManager::activateDeepStealth() {
    deepStealthEnabled = true;
    stealthModeEnabled = true;
    setSecurityState(SECURITY_DEEP_STEALTH);
    stealthActivations++;
    
    Serial.println("🕳️ Deep stealth mode activated - minimal responses");
    
    // Reduce all response frequencies
    stealthInterval = 5000; // 5 second intervals
}

void SecurityManager::performAntiDetectionMeasures() {
    if (!antiDetectionEnabled) return;
    
    unsigned long currentTime = millis();
    
    if (currentTime - lastAntiDetectionCheck >= stealthInterval) {
        lastAntiDetectionCheck = currentTime;
        
        // Randomize behavior patterns
        randomizeBehaviorPatterns();
        
        // Check for detection attempts
        if (detectTamperingAttempt()) {
            detectionAttempts++;
            handleSecurityAlert(SECURITY_WARNING);
        }
        
        // Morph behavior pattern periodically
        if (detectionCounter % 10 == 0) {
            morphBehaviorPattern();
        }
        
        detectionCounter++;
    }
}

void SecurityManager::randomizeBehaviorPatterns() {
    // Randomize mouse movement patterns
    behaviorPattern = (behaviorPattern + generateSecureRandom()) % 16;
    
    // Randomize timing patterns
    stealthInterval = STEALTH_PATTERNS[behaviorPattern % (sizeof(STEALTH_PATTERNS) / sizeof(STEALTH_PATTERNS[0]))];
    
    // Add random jitter
    stealthInterval += generateSecureRandom() % 200;
}

void SecurityManager::morphBehaviorPattern() {
    // Change behavior patterns to avoid detection
    behaviorPattern = (behaviorPattern + 1) % 16;
    
    // Rotate encryption keys
    rotateEncryptionKeys();
    
    // Regenerate some identifiers
    obfuscatedIdentifier = createDeviceFingerprint();
}

bool SecurityManager::detectTamperingAttempt() {
    // Check for various tampering indicators
    
    // Check if device signature has been modified
    String currentSignature = generateObfuscatedSignature(deviceUniqueId);
    if (currentSignature != deviceSignature) {
        Serial.println("⚠️ Device signature mismatch detected");
        return true;
    }
    
    // Check for unusual system behavior
    if (ESP.getFreeHeap() < 10000) { // Very low memory might indicate tampering
        Serial.println("⚠️ Unusual memory usage detected");
        return true;
    }
    
    // Check for timing anomalies
    static unsigned long lastCheck = 0;
    unsigned long currentTime = millis();
    if (lastCheck > 0 && (currentTime - lastCheck) > 10000) { // Large time gap
        Serial.println("⚠️ Timing anomaly detected");
        lastCheck = currentTime;
        return true;
    }
    lastCheck = currentTime;
    
    return false;
}

void SecurityManager::performSecurityChecks() {
    unsigned long currentTime = millis();
    
    if (currentTime - lastSecurityAudit >= SECURITY_CHECK_INTERVAL) {
        lastSecurityAudit = currentTime;
        
        if (performSecurityAudit()) {
            auditsPassed++;
        } else {
            auditsFailed++;
            handleSecurityAlert(SECURITY_ALERT);
        }
    }
}

bool SecurityManager::performSecurityAudit() {
    // Verify device signature hasn't been tampered with
    if (!verifyDeviceSignature()) {
        Serial.println("❌ Security audit failed: Invalid device signature");
        return false;
    }
    
    // Check system integrity
    if (!validateSecurityIntegrity()) {
        Serial.println("❌ Security audit failed: System integrity compromised");
        return false;
    }
    
    // Verify no tampering evidence
    if (checkTamperEvidence()) {
        Serial.println("❌ Security audit failed: Tampering evidence found");
        return false;
    }
    
    return true;
}

bool SecurityManager::verifyDeviceSignature() {
    String currentSignature = generateObfuscatedSignature(deviceUniqueId);
    return currentSignature == deviceSignature;
}

bool SecurityManager::validateSecurityIntegrity() {
    // Check critical security components
    return (deviceUniqueId.length() > 0 && 
            deviceSignature.length() > 0 && 
            obfuscatedIdentifier.length() > 0);
}

bool SecurityManager::checkTamperEvidence() {
    // Look for evidence of tampering
    // This is a simplified check - production would be more sophisticated
    return false;
}

void SecurityManager::handleSecurityAlert(uint8_t alertLevel) {
    securityEvents++;
    
    switch (alertLevel) {
        case SECURITY_WARNING:
            Serial.println("⚠️ Security warning detected");
            break;
        case SECURITY_ALERT:
            Serial.println("🚨 Security alert triggered");
            setSecurityState(SECURITY_ALERT);
            break;
        case SECURITY_BREACH:
            Serial.println("💥 Security breach detected");
            handleSecurityBreach();
            break;
    }
}

void SecurityManager::handleSecurityBreach() {
    setSecurityState(SECURITY_LOCKDOWN);
    activateDeepStealth();
    
    Serial.println("🔒 Security lockdown activated");
    
    // Additional security measures would go here
}

void SecurityManager::rotateEncryptionKeys() {
    // Rotate obfuscation patterns
    for (int i = 0; i < 32; i++) {
        // Simple rotation - production would use proper key rotation
        uint8_t temp = OBFUSCATION_MATRIX[i];
        // This is a const array, so we can't actually modify it
        // In production, this would work with a mutable key store
    }
}

String SecurityManager::getDeviceId() const {
    return deviceUniqueId;
}

String SecurityManager::getDeviceSignature() const {
    return deviceSignature;
}

bool SecurityManager::isStealthModeEnabled() const {
    return stealthModeEnabled;
}

SecurityState SecurityManager::getSecurityState() const {
    return currentSecurityState;
}

void SecurityManager::setSecurityState(SecurityState state) {
    currentSecurityState = state;
}

uint32_t SecurityManager::getSecurityEvents() const {
    return securityEvents;
}

void SecurityManager::printSecurityStatus() {
    Serial.println("========================================");
    Serial.println("Security Manager Status");
    Serial.println("========================================");
    Serial.println("Device ID: " + deviceUniqueId);
    Serial.println("Security State: " + String(currentSecurityState));
    Serial.println("Stealth Mode: " + String(stealthModeEnabled ? "ENABLED" : "DISABLED"));
    Serial.println("Deep Stealth: " + String(deepStealthEnabled ? "ENABLED" : "DISABLED"));
    Serial.println("Security Events: " + String(securityEvents));
    Serial.println("Detection Attempts: " + String(detectionAttempts));
    Serial.println("Stealth Activations: " + String(stealthActivations));
    Serial.println("Audits Passed: " + String(auditsPassed));
    Serial.println("Audits Failed: " + String(auditsFailed));
    Serial.println("========================================");
}
