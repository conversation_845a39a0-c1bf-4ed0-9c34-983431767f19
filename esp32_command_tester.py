import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import serial
import serial.tools.list_ports
import threading
import time

try:
    import keyboard
    KEYBOARD_AVAILABLE = True
except ImportError:
    print("⚠️ 'keyboard' module not found. Install with: pip install keyboard")
    print("   F3 key detection will not work without this module.")
    KEYBOARD_AVAILABLE = False

class ESP32CommandTester:
    def __init__(self, root):
        self.root = root
        self.root.title("ESP32 Command Tester - Press F3 to Move Mouse")
        self.root.geometry("800x600")

        self.serial_connection = None
        self.is_connected = False
        self.reading_thread = None
        self.stop_reading = False
        self.f3_monitoring = False
        self.f3_thread = None

        self.setup_ui()
        self.refresh_ports()
        self.setup_keyboard_monitoring()
        
    def setup_ui(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Connection frame
        conn_frame = ttk.LabelFrame(main_frame, text="Connection", padding="5")
        conn_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(conn_frame, text="COM Port:").grid(row=0, column=0, padx=(0, 5))
        self.port_var = tk.StringVar()
        self.port_combo = ttk.Combobox(conn_frame, textvariable=self.port_var, width=15)
        self.port_combo.grid(row=0, column=1, padx=(0, 10))
        
        ttk.Button(conn_frame, text="Refresh", command=self.refresh_ports).grid(row=0, column=2, padx=(0, 10))
        
        self.connect_btn = ttk.Button(conn_frame, text="Connect", command=self.toggle_connection)
        self.connect_btn.grid(row=0, column=3)
        
        self.status_label = ttk.Label(conn_frame, text="Disconnected", foreground="red")
        self.status_label.grid(row=0, column=4, padx=(10, 0))
        
        # Quick commands frame
        cmd_frame = ttk.LabelFrame(main_frame, text="Quick Commands", padding="5")
        cmd_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Row 1
        ttk.Button(cmd_frame, text="PING", command=lambda: self.send_command("ping")).grid(row=0, column=0, padx=2, pady=2)
        ttk.Button(cmd_frame, text="IDENTIFY", command=lambda: self.send_command("identify")).grid(row=0, column=1, padx=2, pady=2)
        ttk.Button(cmd_frame, text="GET_VERSION", command=lambda: self.send_command("get_version")).grid(row=0, column=2, padx=2, pady=2)
        ttk.Button(cmd_frame, text="STATUS", command=lambda: self.send_command("STATUS")).grid(row=0, column=3, padx=2, pady=2)
        
        # Row 2
        ttk.Button(cmd_frame, text="LED_ON", command=lambda: self.send_command("LED_ON")).grid(row=1, column=0, padx=2, pady=2)
        ttk.Button(cmd_frame, text="LED_OFF", command=lambda: self.send_command("LED_OFF")).grid(row=1, column=1, padx=2, pady=2)
        ttk.Button(cmd_frame, text="BLINK 5", command=lambda: self.send_command("BLINK 5")).grid(row=1, column=2, padx=2, pady=2)
        ttk.Button(cmd_frame, text="RESET", command=lambda: self.send_command("RESET")).grid(row=1, column=3, padx=2, pady=2)
        
        # Mouse movement frame
        mouse_frame = ttk.LabelFrame(main_frame, text="Mouse Movement", padding="5")
        mouse_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Mouse controls
        ttk.Label(mouse_frame, text="X:").grid(row=0, column=0)
        self.mouse_x = tk.StringVar(value="10")
        ttk.Entry(mouse_frame, textvariable=self.mouse_x, width=8).grid(row=0, column=1, padx=(0, 10))
        
        ttk.Label(mouse_frame, text="Y:").grid(row=0, column=2)
        self.mouse_y = tk.StringVar(value="-5")
        ttk.Entry(mouse_frame, textvariable=self.mouse_y, width=8).grid(row=0, column=3, padx=(0, 10))
        
        ttk.Button(mouse_frame, text="Move Mouse", command=self.send_mouse_move).grid(row=0, column=4, padx=(0, 10))
        
        # Recoil testing
        ttk.Label(mouse_frame, text="Steps:").grid(row=1, column=0)
        self.recoil_steps = tk.StringVar(value="33")
        ttk.Entry(mouse_frame, textvariable=self.recoil_steps, width=8).grid(row=1, column=1, padx=(0, 10))
        
        ttk.Label(mouse_frame, text="Delay:").grid(row=1, column=2)
        self.recoil_delay = tk.StringVar(value="4")
        ttk.Entry(mouse_frame, textvariable=self.recoil_delay, width=8).grid(row=1, column=3, padx=(0, 10))
        
        ttk.Button(mouse_frame, text="Smooth Recoil", command=self.send_smooth_recoil).grid(row=1, column=4)
        
        # Custom command frame
        custom_frame = ttk.LabelFrame(main_frame, text="Custom Command", padding="5")
        custom_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.custom_cmd = tk.StringVar()
        ttk.Entry(custom_frame, textvariable=self.custom_cmd, width=50).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(custom_frame, text="Send", command=self.send_custom_command).grid(row=0, column=1)
        
        # Output frame
        output_frame = ttk.LabelFrame(main_frame, text="ESP32 Output", padding="5")
        output_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        self.output_text = scrolledtext.ScrolledText(output_frame, height=15, width=80)
        self.output_text.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        ttk.Button(output_frame, text="Clear", command=self.clear_output).grid(row=1, column=0, pady=(5, 0))
        ttk.Button(output_frame, text="Save Log", command=self.save_log).grid(row=1, column=1, pady=(5, 0))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(4, weight=1)
        output_frame.columnconfigure(0, weight=1)
        output_frame.rowconfigure(0, weight=1)
        
    def refresh_ports(self):
        ports = [port.device for port in serial.tools.list_ports.comports()]
        self.port_combo['values'] = ports
        if ports and not self.port_var.get():
            self.port_var.set(ports[0])
            
    def toggle_connection(self):
        if not self.is_connected:
            self.connect()
        else:
            self.disconnect()
            
    def connect(self):
        port = self.port_var.get()
        if not port:
            messagebox.showerror("Error", "Please select a COM port")
            return
            
        try:
            self.serial_connection = serial.Serial(port, 115200, timeout=1)
            self.is_connected = True
            self.connect_btn.config(text="Disconnect")
            self.status_label.config(text="Connected", foreground="green")
            
            # Start reading thread
            self.stop_reading = False
            self.reading_thread = threading.Thread(target=self.read_serial, daemon=True)
            self.reading_thread.start()
            
            self.log_message(f"Connected to {port}")
            
        except Exception as e:
            messagebox.showerror("Connection Error", f"Failed to connect: {str(e)}")
            
    def disconnect(self):
        if self.serial_connection:
            self.stop_reading = True
            if self.reading_thread:
                self.reading_thread.join(timeout=1)

            self.serial_connection.close()
            self.serial_connection = None
            self.is_connected = False
            self.connect_btn.config(text="Connect")
            self.status_label.config(text="Disconnected", foreground="red")
            self.log_message("Disconnected")

            # Update F3 status when disconnected
            if hasattr(self, 'f3_status_label'):
                self.f3_status_label.config(text="F3 Status: Disconnected", foreground="red")
            
    def read_serial(self):
        while not self.stop_reading and self.serial_connection:
            try:
                if self.serial_connection.in_waiting:
                    data = self.serial_connection.readline().decode('utf-8', errors='ignore').strip()
                    if data:
                        self.log_message(f"ESP32: {data}")
            except Exception as e:
                self.log_message(f"Read error: {str(e)}")
                break
            time.sleep(0.01)
            
    def send_command(self, command):
        if not self.is_connected:
            messagebox.showwarning("Warning", "Not connected to ESP32")
            return
            
        try:
            self.serial_connection.write(f"{command}\n".encode())
            self.log_message(f"Sent: {command}")
        except Exception as e:
            messagebox.showerror("Send Error", f"Failed to send command: {str(e)}")
            
    def send_mouse_move(self):
        try:
            x = self.mouse_x.get()
            y = self.mouse_y.get()
            command = f"MOUSE_MOVE {x},{y}"
            self.send_command(command)
        except ValueError:
            messagebox.showerror("Error", "Invalid mouse coordinates")
            
    def send_smooth_recoil(self):
        try:
            x = self.mouse_x.get()
            y = self.mouse_y.get()
            steps = self.recoil_steps.get()
            delay = self.recoil_delay.get()
            command = f"RECOIL_SMOOTH {x},{y},{steps},{delay}"
            self.send_command(command)
        except ValueError:
            messagebox.showerror("Error", "Invalid recoil parameters")
            
    def send_custom_command(self):
        command = self.custom_cmd.get().strip()
        if command:
            self.send_command(command)
            self.custom_cmd.set("")
            
    def log_message(self, message):
        timestamp = time.strftime("%H:%M:%S")
        self.output_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.output_text.see(tk.END)
        
    def clear_output(self):
        self.output_text.delete(1.0, tk.END)
        
    def save_log(self):
        try:
            content = self.output_text.get(1.0, tk.END)
            filename = f"esp32_log_{time.strftime('%Y%m%d_%H%M%S')}.txt"
            with open(filename, 'w') as f:
                f.write(content)
            messagebox.showinfo("Success", f"Log saved as {filename}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save log: {str(e)}")

    def setup_keyboard_monitoring(self):
        """Setup F3 key monitoring for mouse movement"""
        if not KEYBOARD_AVAILABLE:
            self.log_message("⚠️ Keyboard monitoring not available - install 'keyboard' module")
            return

        # Add F3 status to UI
        f3_frame = ttk.LabelFrame(self.root, text="F3 Key Control", padding="5")
        f3_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        self.f3_status_label = ttk.Label(f3_frame, text="F3 Status: Ready", foreground="blue")
        self.f3_status_label.grid(row=0, column=0, padx=5)

        ttk.Label(f3_frame, text="Press F3 to move mouse by current X,Y values").grid(row=0, column=1, padx=10)

        # Start F3 monitoring thread
        self.start_f3_monitoring()

    def start_f3_monitoring(self):
        """Start monitoring F3 key presses"""
        if not KEYBOARD_AVAILABLE:
            return

        self.f3_monitoring = True
        self.f3_thread = threading.Thread(target=self.monitor_f3_key, daemon=True)
        self.f3_thread.start()
        self.log_message("🎹 F3 key monitoring started - Press F3 to move mouse")

    def monitor_f3_key(self):
        """Monitor F3 key presses in background thread"""
        try:
            while self.f3_monitoring:
                if keyboard.is_pressed('f3'):
                    if self.is_connected:
                        # Get current mouse coordinates from UI
                        try:
                            x = self.mouse_x.get()
                            y = self.mouse_y.get()

                            # Send mouse move command
                            command = f"MOUSE_MOVE {x},{y}"
                            self.send_command(command)

                            # Update F3 status
                            self.root.after(0, lambda: self.f3_status_label.config(
                                text=f"F3 Status: Moved ({x},{y})", foreground="green"))

                            # Wait to prevent spam
                            time.sleep(0.1)

                        except ValueError:
                            self.root.after(0, lambda: self.log_message("❌ Invalid mouse coordinates for F3"))
                    else:
                        self.root.after(0, lambda: self.log_message("❌ F3 pressed but not connected to ESP32"))
                        self.root.after(0, lambda: self.f3_status_label.config(
                            text="F3 Status: Not Connected", foreground="red"))

                time.sleep(0.05)  # Check every 50ms

        except Exception as e:
            self.root.after(0, lambda: self.log_message(f"❌ F3 monitoring error: {e}"))

    def stop_f3_monitoring(self):
        """Stop F3 key monitoring"""
        self.f3_monitoring = False
        if self.f3_thread:
            self.f3_thread.join(timeout=1)

if __name__ == "__main__":
    root = tk.Tk()
    app = ESP32CommandTester(root)

    # Handle window closing to stop F3 monitoring
    def on_closing():
        app.stop_f3_monitoring()
        app.disconnect()
        root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()
