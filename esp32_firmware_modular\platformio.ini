;
; ESP32-S2 Enhanced HID Mouse Firmware - PlatformIO Configuration
; Octane Recoil Control System - Modular Architecture Edition
;

[env:lolin_s2_mini]
platform = espressif32
board = lolin_s2_mini
framework = arduino

; Build configuration
build_flags = 
    -DCORE_DEBUG_LEVEL=0
    -DBOARD_HAS_PSRAM=0
    -DAR<PERSON><PERSON>NO_USB_MODE=1
    -DARDUINO_USB_CDC_ON_BOOT=1
    -DFIRMWARE_VERSION=\"2.1.0\"
    -DHARDWARE_VERSION=\"ESP32-S2-Enhanced\"
    -DSECURITY_LEVEL=\"ADVANCED\"
    -DFEATURE_ANTI_DETECTION_ENABLED=true
    -DFEATURE_STEALTH_MODE_ENABLED=true
    -DFEATURE_TEMPLATE_RECOIL_ENABLED=true
    -DFEATURE_ZERO_DELAY_ENABLED=true
    -DFEATURE_SECURITY_AUDIT_ENABLED=true

; Optimization flags
build_flags = 
    ${env:lolin_s2_mini.build_flags}
    -O3
    -ffast-math
    -funroll-loops
    -finline-functions
    -fomit-frame-pointer

; Memory optimization
build_flags = 
    ${env:lolin_s2_mini.build_flags}
    -DCONFIG_SPIRAM_SUPPORT=0
    -DCONFIG_SPIRAM_USE_MALLOC=0

; USB HID configuration
build_flags = 
    ${env:lolin_s2_mini.build_flags}
    -DUSB_VID=0x303A
    -DUSB_PID=0x8001
    -DUSB_MANUFACTURER=\"Octane\"
    -DUSB_PRODUCT=\"Enhanced_HID_Mouse\"
    -DUSB_SERIAL=\"ESP32S2_OCTANE_ENHANCED\"

; Libraries
lib_deps = 
    ArduinoJson@^6.21.3
    ESP32-USB-Soft-Host@^1.0.4

; Monitor configuration
monitor_speed = 115200
monitor_filters = 
    esp32_exception_decoder
    time

; Upload configuration
upload_speed = 921600
upload_protocol = esptool

; Debug configuration
[env:lolin_s2_mini_debug]
extends = env:lolin_s2_mini
build_type = debug
build_flags = 
    ${env:lolin_s2_mini.build_flags}
    -DDEBUG_MODE=1
    -DCORE_DEBUG_LEVEL=4
    -DDEBUG_SERIAL_ENABLED=true
    -DDEBUG_LED_ENABLED=true
    -DDEBUG_TIMING_ENABLED=true
    -g
    -O0

; Production configuration
[env:lolin_s2_mini_production]
extends = env:lolin_s2_mini
build_flags = 
    ${env:lolin_s2_mini.build_flags}
    -DPRODUCTION_BUILD=1
    -DDEBUG_MODE=0
    -DCORE_DEBUG_LEVEL=0
    -DDEBUG_SERIAL_ENABLED=false
    -DDEBUG_LED_ENABLED=false
    -DDEBUG_TIMING_ENABLED=false
    -DDEBUG_SECURITY_ENABLED=false
    -Os
    -DNDEBUG

; Security hardened configuration
[env:lolin_s2_mini_secure]
extends = env:lolin_s2_mini_production
build_flags = 
    ${env:lolin_s2_mini_production.build_flags}
    -DSECURITY_HARDENED=1
    -DANTI_DEBUGGING_ENABLED=1
    -DOBFUSCATION_ENABLED=1
    -DSTEALTH_MODE_DEFAULT=1
    -DSECURITY_AUDIT_STRICT=1

; Performance optimized configuration
[env:lolin_s2_mini_performance]
extends = env:lolin_s2_mini
build_flags = 
    ${env:lolin_s2_mini.build_flags}
    -DPERFORMANCE_MODE=1
    -DZERO_DELAY_OPTIMIZED=1
    -DTEMPLATE_PRECISION_ENABLED=1
    -DSUB_PIXEL_PRECISION=1
    -DHIGH_FREQUENCY_PROCESSING=1
    -O3
    -ffast-math
    -funroll-loops
    -finline-functions
    -fomit-frame-pointer
    -march=native
    -mtune=native
