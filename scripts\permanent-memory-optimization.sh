#!/bin/bash
# Permanent VPS Memory Optimization Script
# This script implements comprehensive memory optimizations for low-memory VPS environments

set -e

echo "🔧 Starting Permanent VPS Memory Optimization..."
echo "================================================"

# Function to check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        echo "❌ This script must be run as root"
        exit 1
    fi
}

# Function to backup configuration files
backup_configs() {
    echo "📋 Creating configuration backups..."
    
    BACKUP_DIR="/opt/octane-backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # Backup critical configs
    cp /etc/mysql/mariadb.conf.d/50-server.cnf "$BACKUP_DIR/" 2>/dev/null || true
    cp /etc/nginx/nginx.conf "$BACKUP_DIR/" 2>/dev/null || true
    cp /etc/systemd/system/pm2-octane.service "$BACKUP_DIR/" 2>/dev/null || true
    
    echo "✅ Backups created in $BACKUP_DIR"
}

# Function to optimize MariaDB for low memory
optimize_mariadb() {
    echo "🗄️ Optimizing MariaDB for low memory..."
    
    cat >> /etc/mysql/mariadb.conf.d/50-server.cnf << 'EOF'

# === PERMANENT LOW MEMORY OPTIMIZATIONS ===
# Buffer Pool - Critical for memory usage
innodb_buffer_pool_size = 32M
innodb_buffer_pool_instances = 1

# Log Files - Reduce memory usage
innodb_log_file_size = 8M
innodb_log_buffer_size = 1M
innodb_log_files_in_group = 2

# MyISAM Settings
key_buffer_size = 8M
myisam_sort_buffer_size = 4M

# Query Cache
query_cache_type = 1
query_cache_size = 4M
query_cache_limit = 512K

# Connection Settings
max_connections = 20
max_user_connections = 15
thread_cache_size = 4

# Table Settings
table_open_cache = 32
table_definition_cache = 32

# Temporary Tables
tmp_table_size = 8M
max_heap_table_size = 8M

# Sort and Join Buffers
sort_buffer_size = 256K
join_buffer_size = 256K
read_buffer_size = 256K
read_rnd_buffer_size = 256K

# Disable Performance Schema (saves ~400MB)
performance_schema = OFF

# Disable unused engines
default_table_type = MyISAM
skip-innodb-doublewrite

EOF

    echo "✅ MariaDB optimized for low memory"
}

# Function to optimize Nginx
optimize_nginx() {
    echo "🌐 Optimizing Nginx for low memory..."
    
    # Backup original config
    cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.backup
    
    # Create optimized nginx config
    cat > /etc/nginx/nginx.conf << 'EOF'
user www-data;
worker_processes 1;
worker_rlimit_nofile 1024;
pid /run/nginx.pid;

events {
    worker_connections 512;
    use epoll;
    multi_accept on;
}

http {
    # Basic Settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 30;
    keepalive_requests 100;
    types_hash_max_size 2048;
    server_tokens off;
    
    # Buffer Settings (Low Memory)
    client_body_buffer_size 8K;
    client_header_buffer_size 1k;
    client_max_body_size 1m;
    large_client_header_buffers 2 1k;
    
    # Timeouts
    client_body_timeout 12;
    client_header_timeout 12;
    send_timeout 10;
    
    # Gzip Settings
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml;
    
    # MIME Types
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Logging (Minimal)
    access_log /var/log/nginx/access.log combined buffer=32k flush=5m;
    error_log /var/log/nginx/error.log warn;
    
    # Virtual Host Configs
    include /etc/nginx/conf.d/*.conf;
    include /etc/nginx/sites-enabled/*;
}
EOF

    echo "✅ Nginx optimized for low memory"
}

# Function to configure system-wide memory optimizations
optimize_system() {
    echo "⚙️ Applying system-wide memory optimizations..."
    
    # Kernel parameters for low memory
    cat >> /etc/sysctl.conf << 'EOF'

# === LOW MEMORY VPS OPTIMIZATIONS ===
# Virtual Memory Settings
vm.swappiness = 10
vm.vfs_cache_pressure = 50
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5

# Memory Overcommit
vm.overcommit_memory = 1
vm.overcommit_ratio = 50

# Network Memory
net.core.rmem_max = 16777216
net.core.wmem_max = 16777216
net.ipv4.tcp_rmem = 4096 65536 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216

# Reduce kernel memory usage
kernel.pid_max = 4096
EOF

    # Apply immediately
    sysctl -p
    
    echo "✅ System memory optimizations applied"
}

# Function to disable unnecessary services
disable_services() {
    echo "🚫 Disabling unnecessary services..."
    
    # List of services to disable on low-memory VPS
    SERVICES_TO_DISABLE=(
        "snapd"
        "fwupd"
        "fwupd-refresh.timer"
        "motd-news.timer"
        "apt-daily.timer"
        "apt-daily-upgrade.timer"
        "systemd-resolved"
        "bluetooth"
        "cups"
        "avahi-daemon"
    )
    
    for service in "${SERVICES_TO_DISABLE[@]}"; do
        if systemctl is-enabled "$service" >/dev/null 2>&1; then
            systemctl disable "$service" >/dev/null 2>&1 || true
            systemctl stop "$service" >/dev/null 2>&1 || true
            echo "   Disabled: $service"
        fi
    done
    
    echo "✅ Unnecessary services disabled"
}

# Function to optimize log rotation
optimize_logs() {
    echo "📝 Optimizing log rotation..."
    
    # Create aggressive log rotation for low memory VPS
    cat > /etc/logrotate.d/octane-memory-optimization << 'EOF'
/var/log/nginx/*.log {
    daily
    missingok
    rotate 3
    compress
    delaycompress
    notifempty
    create 0644 www-data adm
    sharedscripts
    prerotate
        if [ -d /etc/logrotate.d/httpd-prerotate ]; then \
            run-parts /etc/logrotate.d/httpd-prerotate; \
        fi
    endscript
    postrotate
        invoke-rc.d nginx rotate >/dev/null 2>&1
    endscript
}

/var/log/mysql/*.log {
    daily
    missingok
    rotate 2
    compress
    delaycompress
    notifempty
    create 0644 mysql adm
}

/opt/octane-auth/.pm2/logs/*.log {
    daily
    missingok
    rotate 2
    compress
    delaycompress
    notifempty
    create 0644 octane octane
}
EOF

    echo "✅ Log rotation optimized"
}

# Function to create memory monitoring service
create_monitoring() {
    echo "📊 Creating memory monitoring service..."
    
    # Enhanced memory monitor script
    cat > /usr/local/bin/octane-memory-monitor.sh << 'EOF'
#!/bin/bash
# Enhanced Memory Monitor for Octane VPS

MEMORY_THRESHOLD=85
SWAP_THRESHOLD=60
LOG_FILE="/var/log/octane-memory.log"

# Get memory usage percentage
MEM_USAGE=$(free | grep Mem | awk '{printf("%.0f", $3/$2 * 100.0)}')
SWAP_USAGE=$(free | grep Swap | awk '{if($2>0) printf("%.0f", $3/$2 * 100.0); else print "0"}')

# Log current status
echo "$(date): Memory: ${MEM_USAGE}%, Swap: ${SWAP_USAGE}%" >> "$LOG_FILE"

# Alert if memory usage is high
if [ "$MEM_USAGE" -gt $MEMORY_THRESHOLD ]; then
    echo "$(date): HIGH MEMORY USAGE: ${MEM_USAGE}%" >> "$LOG_FILE"
    
    # Emergency memory cleanup
    if [ "$MEM_USAGE" -gt 90 ]; then
        echo "$(date): CRITICAL MEMORY - Emergency cleanup" >> "$LOG_FILE"
        
        # Clear caches
        sync && echo 3 > /proc/sys/vm/drop_caches
        
        # Restart services if critically high
        if [ "$MEM_USAGE" -gt 95 ]; then
            echo "$(date): EMERGENCY - Restarting services" >> "$LOG_FILE"
            systemctl restart nginx
            systemctl restart mariadb
            su - octane -c 'pm2 restart octane-auth'
        fi
    fi
fi

# Rotate log if too large
if [ -f "$LOG_FILE" ] && [ $(stat -f%z "$LOG_FILE" 2>/dev/null || stat -c%s "$LOG_FILE") -gt 1048576 ]; then
    tail -n 100 "$LOG_FILE" > "${LOG_FILE}.tmp"
    mv "${LOG_FILE}.tmp" "$LOG_FILE"
fi
EOF

    chmod +x /usr/local/bin/octane-memory-monitor.sh
    
    # Create systemd service
    cat > /etc/systemd/system/octane-memory-monitor.service << 'EOF'
[Unit]
Description=Octane Memory Monitor
After=network.target

[Service]
Type=oneshot
ExecStart=/usr/local/bin/octane-memory-monitor.sh
User=root

[Install]
WantedBy=multi-user.target
EOF

    # Create timer
    cat > /etc/systemd/system/octane-memory-monitor.timer << 'EOF'
[Unit]
Description=Run Octane Memory Monitor every 2 minutes
Requires=octane-memory-monitor.service

[Timer]
OnCalendar=*:0/2
Persistent=true

[Install]
WantedBy=timers.target
EOF

    systemctl daemon-reload
    systemctl enable octane-memory-monitor.timer
    systemctl start octane-memory-monitor.timer
    
    echo "✅ Memory monitoring service created"
}

# Main execution
main() {
    check_root
    backup_configs
    optimize_mariadb
    optimize_nginx
    optimize_system
    disable_services
    optimize_logs
    create_monitoring
    
    echo ""
    echo "🎉 Permanent Memory Optimization Complete!"
    echo "================================================"
    echo "📊 Current Memory Status:"
    free -h
    echo ""
    echo "🔄 Restarting optimized services..."
    systemctl restart mariadb
    systemctl restart nginx
    su - octane -c 'pm2 restart octane-auth'
    
    echo ""
    echo "✅ All optimizations applied successfully!"
    echo "💡 The system will now use significantly less memory."
    echo "📈 Memory monitoring is active every 2 minutes."
    echo "📋 Backups are stored in /opt/octane-backups/"
}

# Run main function
main "$@"
