
#include "time.hpp"

void m_timer::start()
{
	start_time = std::chrono::system_clock::now();
	timer_running = true;
}

void m_timer::end()
{
	end_time = std::chrono::system_clock::now();
	timer_running = false;
}

double m_timer::elapsed_time_ms()
{
	std::chrono::time_point<std::chrono::system_clock > m_end_time;
	m_end_time = timer_running ? std::chrono::system_clock::now() : end_time;

	return std::chrono::duration_cast<std::chrono::milliseconds>(m_end_time - start_time).count();
}

double m_timer::elapsed_time_s()
{
	return elapsed_time_ms() / 1000.0;
}




