const http = require('http');
const crypto = require('crypto');

// Generate hardware ID
function generateHardwareId() {
    const components = "test_cpu_id" + "test_motherboard_serial";
    const hash = crypto.createHash('sha256').update(components).digest('hex');
    return hash.substring(0, 16);
}

// Test firmware authentication
async function testFirmwareAuth() {
    const hardwareId = generateHardwareId();
    console.log('Hardware ID:', hardwareId);
    
    // Step 1: Generate token
    const tokenData = JSON.stringify({
        hardware_id: hardwareId,
        firmware_type: 'esp32s2_enhanced'
    });
    
    const tokenOptions = {
        hostname: '*************',
        port: 80,
        path: '/api/firmware/token',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(tokenData)
        }
    };
    
    console.log('\n1. Generating token...');
    
    const tokenResponse = await new Promise((resolve, reject) => {
        const req = http.request(tokenOptions, (res) => {
            let data = '';
            res.on('data', (chunk) => data += chunk);
            res.on('end', () => {
                try {
                    resolve(JSON.parse(data));
                } catch (e) {
                    resolve(data);
                }
            });
        });
        
        req.on('error', reject);
        req.write(tokenData);
        req.end();
    });
    
    console.log('Token response:', tokenResponse);
    
    if (!tokenResponse.success) {
        console.error('Failed to generate token');
        return;
    }
    
    const { token, timestamp } = tokenResponse;
    
    // Step 2: Test download with authentication headers
    console.log('\n2. Testing download with auth headers...');
    
    const downloadOptions = {
        hostname: '*************',
        port: 80,
        path: '/api/firmware/download/esp32s2_enhanced/bootloader',
        method: 'GET',
        headers: {
            'hardware-id': hardwareId,
            'timestamp': timestamp,
            'token': token
        }
    };

    console.log('Sending headers:', downloadOptions.headers);
    
    const downloadResponse = await new Promise((resolve, reject) => {
        const req = http.request(downloadOptions, (res) => {
            console.log('Download status:', res.statusCode);
            console.log('Download headers:', res.headers);
            
            if (res.statusCode === 200) {
                let dataLength = 0;
                res.on('data', (chunk) => dataLength += chunk.length);
                res.on('end', () => {
                    resolve({ success: true, size: dataLength });
                });
            } else {
                let data = '';
                res.on('data', (chunk) => data += chunk);
                res.on('end', () => {
                    try {
                        resolve(JSON.parse(data));
                    } catch (e) {
                        resolve({ error: data, statusCode: res.statusCode });
                    }
                });
            }
        });
        
        req.on('error', reject);
        req.end();
    });
    
    console.log('Download response:', downloadResponse);
}

testFirmwareAuth().catch(console.error);
