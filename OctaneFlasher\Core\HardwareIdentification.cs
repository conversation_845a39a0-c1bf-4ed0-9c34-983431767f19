using System;
using System.Security.Cryptography;
using System.Text;

namespace OctaneFlasher.Core
{
    /// <summary>
    /// Handles hardware identification for license validation
    /// </summary>
    public static class HardwareIdentification
    {
        /// <summary>
        /// Generate unique hardware ID for this machine
        /// </summary>
        public static string GenerateHardwareId()
        {
            try
            {
                // Use the EXACT same method as desktop app (AuthenticationService.cs)
                var sb = new StringBuilder();
                sb.Append(Environment.ProcessorCount);
                sb.Append(Environment.MachineName);
                sb.Append(Environment.UserName);
                sb.Append(Environment.OSVersion.ToString());

                using (var sha256 = SHA256.Create())
                {
                    var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(sb.ToString()));
                    return Convert.ToBase64String(hash).Substring(0, 16); // Base64, first 16 chars
                }
            }
            catch
            {
                return Convert.ToBase64String(Encoding.UTF8.GetBytes($"{Environment.MachineName}_{Environment.UserName}")).Substring(0, 16);
            }
        }
    }
}
