#pragma once

#include "../dependencies/include.hpp"

#include "data/data.hpp"
#include "movement/movement.hpp"
#include "mouse/mouse.hpp"
#include "../dependencies/time/time.hpp"


namespace m_core
{
	namespace m_settings
	{
		inline float sensitivity = 0.2f;
		inline float ads_sensitivity = 1.0f;
		inline float field_of_view = 90;
	}

	inline std::vector<m_data::m_weapon> weapon_list{};
	inline std::vector<std::vector<vec2_t>> cached_weapons{};

	vec2_t to_pixel(vec2_t);
	bool static_cache();

	void initialize();
}