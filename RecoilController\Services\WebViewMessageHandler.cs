using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Web.WebView2.Wpf;
using Newtonsoft.Json;
using RecoilController.Models;

namespace RecoilController.Services
{
    /// <summary>
    /// Handles all WebView2 message processing and communication
    /// </summary>
    public class WebViewMessageHandler
    {
        private readonly ESP32Service _esp32Service;
        private readonly RecoilControlService _recoilService;
        private readonly AccuracyEnhancementService _accuracyService;
        private RecoilSettings _recoilSettings;

        public event EventHandler<string>? StatusChanged;
        public event EventHandler<dynamic>? SecurityAlertReceived;
        public event EventHandler<dynamic>? KeybindUpdateReceived;

        public WebViewMessageHandler(
            ESP32Service esp32Service, 
            RecoilControlService recoilService,
            AccuracyEnhancementService accuracyService)
        {
            _esp32Service = esp32Service ?? throw new ArgumentNullException(nameof(esp32Service));
            _recoilService = recoilService ?? throw new ArgumentNullException(nameof(recoilService));
            _accuracyService = accuracyService ?? throw new ArgumentNullException(nameof(accuracyService));
            
            _recoilSettings = new RecoilSettings
            {
                Sensitivity = 2.5,
                FOV = 90.0,
                ADS_Sensitivity = 1.0,
                RecoilCompensation = 100.0,
                Humanization = 0.0,
                Smoothing = 50.0,
                HorizontalPercent = 100.0,
                VerticalPercent = 100.0,
                HipfireEnabled = true,
                CursorCheckEnabled = true,
                RapidFireEnabled = false,
                AntiAfkEnabled = false,
                AfkInterval = 30000 // 30 seconds in milliseconds
            };
        }

        /// <summary>
        /// Process incoming WebView2 messages
        /// </summary>
        public async Task ProcessMessage(string messageJson, WebView2 webView)
        {
            try
            {
                var message = JsonConvert.DeserializeObject<dynamic>(messageJson);
                string action = message?.action ?? "";

                StatusChanged?.Invoke(this, $"Processing action: {action}");

                switch (action.ToLower())
                {
                    case "refreshports":
                        await HandleRefreshPorts(message, webView);
                        break;
                    case "connectesp32":
                        await HandleConnectESP32(message, webView);
                        break;
                    case "disconnectesp32":
                        await HandleDisconnectESP32(message, webView);
                        break;
                    case "enablerecoilscript":
                        await HandleEnableRecoilScript(message, webView);
                        break;
                    case "disablerecoilscript":
                        await HandleDisableRecoilScript(message, webView);
                        break;
                    case "updateweapon":
                        await HandleUpdateWeapon(message, webView);
                        break;
                    case "updaterecoilsettings":
                        await HandleUpdateRecoilSettings(message, webView);
                        break;
                    case "updatekeybind":
                        await HandleKeybindUpdate(message, webView);
                        break;
                    case "securityalert":
                        HandleSecurityAlert(message);
                        break;
                    case "calibrateaccuracy":
                        await HandleCalibrateAccuracy(message, webView);
                        break;
                    case "weaponconfigupdate":
                        await HandleWeaponConfigUpdate(message, webView);
                        break;
                    case "featuretoggle":
                        await HandleFeatureToggle(message, webView);
                        break;
                    default:
                        StatusChanged?.Invoke(this, $"Unknown action: {action}");
                        break;
                }
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"Error processing message: {ex.Message}");
                await SendErrorResponse(webView, ex.Message);
            }
        }

        public async Task HandleRefreshPorts(dynamic? message, WebView2 webView)
        {
            try
            {
                StatusChanged?.Invoke(this, "Refreshing COM ports...");
                
                var availablePorts = SerialPort.GetPortNames().ToList();
                availablePorts.Sort();

                var response = new
                {
                    action = "portsRefreshed",
                    ports = availablePorts,
                    success = true
                };

                var json = JsonConvert.SerializeObject(response);
                webView.CoreWebView2.PostWebMessageAsString(json);
                
                StatusChanged?.Invoke(this, $"Found {availablePorts.Count} COM ports");
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"Error refreshing ports: {ex.Message}");
                await SendErrorResponse(webView, ex.Message);
            }
        }

        private async Task HandleConnectESP32(dynamic message, WebView2 webView)
        {
            try
            {
                string port = message.port;
                StatusChanged?.Invoke(this, $"Connecting to ESP32 on port: {port}");

                bool connected = await _esp32Service.ConnectAsync(port);
                
                var response = new
                {
                    action = "esp32Connected",
                    success = connected,
                    port = port,
                    message = connected ? "ESP32 connected successfully" : "Failed to connect to ESP32"
                };

                var json = JsonConvert.SerializeObject(response);
                webView.CoreWebView2.PostWebMessageAsString(json);
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"ESP32 connection error: {ex.Message}");
                await SendErrorResponse(webView, ex.Message);
            }
        }

        private async Task HandleDisconnectESP32(dynamic message, WebView2 webView)
        {
            try
            {
                StatusChanged?.Invoke(this, "Disconnecting ESP32...");
                await _esp32Service.DisconnectAsync();

                var response = new
                {
                    action = "esp32Disconnected",
                    success = true,
                    message = "ESP32 disconnected successfully"
                };

                var json = JsonConvert.SerializeObject(response);
                webView.CoreWebView2.PostWebMessageAsString(json);
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"ESP32 disconnect error: {ex.Message}");
                await SendErrorResponse(webView, ex.Message);
            }
        }

        private async Task HandleEnableRecoilScript(dynamic message, WebView2 webView)
        {
            try
            {
                StatusChanged?.Invoke(this, "Enabling recoil script...");
                var weaponId = message.weaponId != null ? (int)message.weaponId : 1; // Default to AK-47
                _recoilService.EnableScript(weaponId, _recoilSettings);

                var response = new
                {
                    action = "recoilScriptEnabled",
                    success = true,
                    message = "Recoil script enabled"
                };

                var json = JsonConvert.SerializeObject(response);
                webView.CoreWebView2.PostWebMessageAsString(json);
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"Error enabling recoil script: {ex.Message}");
                await SendErrorResponse(webView, ex.Message);
            }
        }

        private async Task HandleDisableRecoilScript(dynamic message, WebView2 webView)
        {
            try
            {
                StatusChanged?.Invoke(this, "Disabling recoil script...");
                _recoilService.DisableScript();

                var response = new
                {
                    action = "recoilScriptDisabled",
                    success = true,
                    message = "Recoil script disabled"
                };

                var json = JsonConvert.SerializeObject(response);
                webView.CoreWebView2.PostWebMessageAsString(json);
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"Error disabling recoil script: {ex.Message}");
                await SendErrorResponse(webView, ex.Message);
            }
        }

        private async Task HandleUpdateWeapon(dynamic message, WebView2 webView)
        {
            try
            {
                int weapon = message.weapon ?? 0;
                _recoilService.UpdateWeapon(weapon);
                StatusChanged?.Invoke(this, $"Weapon updated to: {weapon}");
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"Error updating weapon: {ex.Message}");
                await SendErrorResponse(webView, ex.Message);
            }
        }

        private async Task HandleUpdateRecoilSettings(dynamic message, WebView2 webView)
        {
            try
            {
                // Handle all settings from the UI
                var settings = message.settings;
                if (settings != null)
                {
                    // Core settings
                    if (settings.sensitivity != null) _recoilSettings.Sensitivity = (double)settings.sensitivity;
                    if (settings.fieldOfView != null) _recoilSettings.FOV = (double)settings.fieldOfView;
                    if (settings.adsSensitivity != null) _recoilSettings.ADS_Sensitivity = (double)settings.adsSensitivity;

                    // Recoil control settings
                    if (settings.recoilCompensation != null) _recoilSettings.RecoilCompensation = (double)settings.recoilCompensation;
                    if (settings.humanization != null) _recoilSettings.Humanization = (double)settings.humanization;

                    // Hip fire and multipliers
                    if (settings.hipFireControl != null) _recoilSettings.HipfireEnabled = (bool)settings.hipFireControl;
                    if (settings.horizontalMultiplier != null) _recoilSettings.HorizontalPercent = (double)settings.horizontalMultiplier;
                    if (settings.verticalMultiplier != null) _recoilSettings.VerticalPercent = (double)settings.verticalMultiplier;
                    if (settings.smoothing != null) _recoilSettings.Smoothing = (double)settings.smoothing;

                    // Game features
                    if (settings.cursorCheck != null) _recoilSettings.CursorCheckEnabled = (bool)settings.cursorCheck;
                    if (settings.rapidFire != null) _recoilSettings.RapidFireEnabled = (bool)settings.rapidFire;
                    if (settings.antiAfk != null) _recoilSettings.AntiAfkEnabled = (bool)settings.antiAfk;
                    if (settings.afkInterval != null) _recoilSettings.AfkInterval = (int)((double)settings.afkInterval * 1000); // Convert seconds to milliseconds
                }

                _recoilService.UpdateSettings(_recoilSettings);
                StatusChanged?.Invoke(this, "Recoil settings updated");
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"Error updating recoil settings: {ex.Message}");
                await SendErrorResponse(webView, ex.Message);
            }
        }

        private async Task HandleKeybindUpdate(dynamic message, WebView2 webView)
        {
            try
            {
                string inputId = message.inputId ?? "";
                string key = message.key ?? "";
                
                switch (inputId)
                {
                    case "crouch-key":
                        _recoilSettings.CrouchKey = key;
                        break;
                    case "forward-key":
                        _recoilSettings.ForwardKey = key;
                        break;
                    case "left-key":
                        _recoilSettings.LeftKey = key;
                        break;
                    case "backward-key":
                        _recoilSettings.BackwardKey = key;
                        break;
                    case "right-key":
                        _recoilSettings.RightKey = key;
                        break;
                }

                KeybindUpdateReceived?.Invoke(this, message);
                StatusChanged?.Invoke(this, $"Keybind updated: {inputId} = {key}");
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"Error updating keybind: {ex.Message}");
                await SendErrorResponse(webView, ex.Message);
            }
        }

        private void HandleSecurityAlert(dynamic message)
        {
            try
            {
                string alertType = message.type ?? "unknown";
                string alertMessage = message.message ?? "No details provided";
                
                StatusChanged?.Invoke(this, $"Security alert: {alertType} - {alertMessage}");
                SecurityAlertReceived?.Invoke(this, message);
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"Error handling security alert: {ex.Message}");
            }
        }

        private async Task HandleCalibrateAccuracy(dynamic message, WebView2 webView)
        {
            try
            {
                string weaponName = message.weapon ?? "unknown";
                StatusChanged?.Invoke(this, $"Starting accuracy calibration for {weaponName}...");

                var result = await _accuracyService.PerformDynamicCalibration(weaponName);

                var response = new
                {
                    action = "accuracyCalibrated",
                    success = result.Success,
                    weapon = weaponName,
                    accuracyScore = result.AccuracyScore,
                    systemLatency = result.SystemLatency,
                    mouseSensitivity = result.MouseSensitivity,
                    message = result.Success ? 
                        $"Calibration complete: {result.AccuracyScore:F1}% accuracy" : 
                        $"Calibration failed: {result.ErrorMessage}"
                };

                var json = JsonConvert.SerializeObject(response);
                webView.CoreWebView2.PostWebMessageAsString(json);
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"Error during accuracy calibration: {ex.Message}");
                await SendErrorResponse(webView, ex.Message);
            }
        }

        private async Task HandleWeaponConfigUpdate(dynamic message, WebView2 webView)
        {
            try
            {
                // Handle weapon configuration updates
                StatusChanged?.Invoke(this, "Weapon configuration updated");
                
                var response = new
                {
                    action = "weaponConfigUpdated",
                    success = true
                };

                var json = JsonConvert.SerializeObject(response);
                webView.CoreWebView2.PostWebMessageAsString(json);
            }
            catch (Exception ex)
            {
                await SendErrorResponse(webView, ex.Message);
            }
        }

        private async Task HandleFeatureToggle(dynamic message, WebView2 webView)
        {
            try
            {
                string feature = message.feature ?? "";
                bool enabled = message.enabled ?? false;
                
                StatusChanged?.Invoke(this, $"Feature toggle: {feature} = {enabled}");
                
                var response = new
                {
                    action = "featureToggled",
                    feature = feature,
                    enabled = enabled,
                    success = true
                };

                var json = JsonConvert.SerializeObject(response);
                webView.CoreWebView2.PostWebMessageAsString(json);
            }
            catch (Exception ex)
            {
                await SendErrorResponse(webView, ex.Message);
            }
        }

        private async Task SendErrorResponse(WebView2 webView, string errorMessage)
        {
            try
            {
                var errorResponse = new
                {
                    action = "error",
                    success = false,
                    message = errorMessage
                };

                var json = JsonConvert.SerializeObject(errorResponse);
                webView.CoreWebView2.PostWebMessageAsString(json);
            }
            catch
            {
                // Ignore errors when sending error responses
            }
        }

        public RecoilSettings GetCurrentSettings()
        {
            return _recoilSettings;
        }

        public void UpdateSettings(RecoilSettings settings)
        {
            _recoilSettings = settings ?? throw new ArgumentNullException(nameof(settings));
        }
    }
}
