#pragma once

namespace m_global
{
	namespace m_settings
	{
		inline float sensitivity = 0.2f;
		inline float ads_sensitivity = 1.0f;
		inline float field_of_view = 90.f;

		inline bool recoil_control = true;
	}

	namespace m_selected
	{
		inline int weapon = 0;
		inline int scope = 0;
		inline int barrel = 0;
	}

	namespace m_keybinds
	{
		inline int crouch = VK_CONTROL;
	}

	namespace m_combo
	{
		namespace m_weapons
		{
			inline int selected = 1;
			inline const char* list[] = { "None", "Assault Rifle", "Lr300 Rifle", "Mp5", "Custom SMG", "Thompson", "Hmlmg", "M249", "Semi Rifle", "P250", "Python", "Prototype 17" };
		}

		namespace m_scopes
		{
			inline int selected = 0;
			inline const char* list[] = { "Ironsight", "Holo Sight", "Simple Sight", "8x Scope", "16x Scope" };
		}

		namespace m_barrels
		{
			inline int selected = 0;
			inline const char* list[] = { "None", "Silencer", "Muzzle Boost" };
		}

		namespace m_hipfire_settings
		{
			inline int selected = 1;
			inline const char* list[] = { "Never", "Always", "On 2nd Shot" };
		}

		namespace m_performance_mode
		{
			inline int selected = 0;
			inline const char* list[] = { "High", "Low" };
		}
	}
}