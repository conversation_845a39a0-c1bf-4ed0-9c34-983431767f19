# Recoil Pattern Dumper - Advanced Statistical Analysis Tool

A comprehensive C# WPF application that automatically extracts and analyzes recoil patterns from Rust game data using statistical methods to determine optimal recoil compensation values.

## Features

### 🔄 Automated Multi-Run Analysis
- Executes Python dumper scripts multiple times (5-50 runs configurable)
- Collects statistical data across multiple dump sessions
- Calculates optimal average values from all successful runs

### 📊 Advanced Statistical Analysis
- **Mean & Standard Deviation** calculations for all recoil parameters
- **Quality Scoring** system to rank weapon pattern reliability
- **Outlier Detection** to identify and handle inconsistent data
- **Confidence Intervals** for statistical reliability assessment

### 🎯 Optimized Pattern Generation
- Generates mathematically optimized recoil patterns
- Calculates best-fit curves for pitch and yaw compensation
- Provides confidence scores for each weapon pattern
- Identifies most reliable weapons based on consistency

### 📈 Comprehensive Reporting
- **Detailed Analysis Reports** with statistical breakdowns
- **Execution Time Statistics** for performance monitoring
- **Pattern Consistency Analysis** showing variation levels
- **Failed Run Analysis** for troubleshooting

### 💾 Multiple Export Formats
- **JSON Format**: Optimized patterns for integration
- **CSV Format**: Statistical data for spreadsheet analysis
- **ESP32 C++ Headers**: Ready-to-use firmware code
- **Chart Data**: JSON format for visualization tools
- **Complete Packages**: All formats bundled with documentation

## Requirements

### System Requirements
- Windows 10/11
- .NET 8.0 Runtime
- Python 3.7+ with UnityPy library
- Rust game installation

### Dependencies
- **Newtonsoft.Json** (13.0.3): JSON serialization
- **MathNet.Numerics** (5.0.0): Statistical calculations
- **UnityPy** (Python): Unity asset extraction

## Installation

1. **Clone or download** the project files
2. **Install .NET 8.0** if not already installed
3. **Build the project** using Visual Studio or `dotnet build`
4. **Ensure Python environment** has UnityPy installed:
   ```bash
   pip install UnityPy
   ```

## Usage

### 1. Configuration
- **Python Path**: Point to your Python executable
- **Script Path**: Select the main.py dumper script
- **Game Path**: Choose your Rust installation directory
- **Run Count**: Set number of analysis runs (10-30 recommended)

### 2. Analysis
- Click **Start Analysis** to begin automated dumping
- Monitor progress in real-time with detailed logging
- View execution statistics and success/failure rates

### 3. Results
- Browse analyzed weapons sorted by quality score
- View detailed statistics for each weapon
- Compare recoil patterns and consistency metrics

### 4. Export
- Choose export formats (JSON, CSV, ESP32, etc.)
- Create complete analysis packages
- Auto-export results after analysis completion

## Statistical Methods

### Quality Scoring Algorithm
```
Quality Score = (Consistency Factor × 40) + 
                (Sample Size Factor × 30) + 
                (Reliability Factor × 30)
```

### Pattern Optimization
- **Weighted Averaging**: More weight to consistent runs
- **Outlier Filtering**: Removes statistical outliers
- **Curve Smoothing**: Reduces noise in recoil patterns
- **Confidence Weighting**: Prioritizes high-confidence data points

## Output Files

### JSON Export (`optimized_patterns.json`)
```json
{
  "WeaponName": "AK47",
  "OptimalPitchPattern": [0.0, -0.1234, -0.2456, ...],
  "OptimalYawPattern": [0.0, 0.0567, -0.0234, ...],
  "ConfidenceScore": 87.5,
  "BasedOnRuns": 25,
  "OptimalProperties": { ... }
}
```

### ESP32 C++ Export (`recoil_patterns.h`)
```cpp
const float ak47_pitch_pattern[] = {
    0.000000f,
    -0.123400f,
    -0.245600f,
    // ... more values
};

const struct {
    const char* name;
    const float* pitch_pattern;
    const float* yaw_pattern;
    int pattern_length;
    float confidence_score;
} ak47_info = { ... };
```

## Integration with ESP32

The generated C++ header files are designed for direct integration with ESP32 recoil compensation firmware:

1. **Include the header** in your ESP32 project
2. **Access weapon patterns** using the lookup arrays
3. **Use confidence scores** to prioritize reliable patterns
4. **Implement timing** based on the optimal properties

## Troubleshooting

### Common Issues

**Python Environment Not Found**
- Ensure Python is in PATH or specify full path
- Verify UnityPy is installed: `pip list | grep UnityPy`

**Script Validation Failed**
- Check that main.py exists and is accessible
- Verify the dumper script dependencies are met

**Game Directory Invalid**
- Ensure Rust is properly installed
- Look for content.bundle files in the game directory

**Analysis Fails**
- Check Python script output in the log
- Verify game files are not corrupted
- Ensure sufficient disk space for temporary files

### Performance Tips

- **Use SSD storage** for faster file I/O operations
- **Close unnecessary applications** during analysis
- **Run 10-20 iterations** for good statistical balance
- **Monitor memory usage** with large datasets

## Technical Architecture

### Services
- **DumperService**: Manages Python script execution
- **AnalysisService**: Performs statistical calculations
- **ExportService**: Handles multiple output formats

### Models
- **RecoilData**: Represents raw dump data
- **WeaponStatistics**: Statistical analysis results
- **OptimizedRecoilPattern**: Final optimized patterns

### UI Components
- **Configuration Tab**: Setup and validation
- **Analysis Tab**: Real-time progress monitoring
- **Results Tab**: Interactive data exploration
- **Export Tab**: Flexible output options

## Contributing

This tool is designed to work with the existing Python dumper scripts. When contributing:

1. **Maintain compatibility** with the current JSON output format
2. **Add statistical methods** that improve pattern accuracy
3. **Enhance export formats** for different use cases
4. **Improve error handling** for edge cases

## License

This project is designed for educational and research purposes. Ensure compliance with game terms of service when using with actual game data.
