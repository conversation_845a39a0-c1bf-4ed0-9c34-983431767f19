using System;
using System.Diagnostics;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;
using OctaneFlasher.Core;
using OctaneFlasher.Services;

namespace OctaneFlasher.Modes
{
    /// <summary>
    /// Flash mode for uploading firmware to ESP32 devices
    /// </summary>
    public class FlashMode
    {
        private readonly HttpClient _httpClient;

        public FlashMode()
        {
            _httpClient = new HttpClient();
        }

        /// <summary>
        /// Execute flash mode
        /// </summary>
        public async Task ExecuteAsync(string licenseKey, string comPort, bool debugMode)
        {
            try
            {
                Console.ForegroundColor = ConsoleColor.Cyan;
                Console.WriteLine("🔥 FLASH MODE - ESP32 Firmware Upload");
                Console.WriteLine("═══════════════════════════════════════");
                Console.ResetColor();
                Console.WriteLine();

                // Validate license
                await LicenseService.ValidateLicenseAsync(licenseKey, debugMode);

                // Download firmware
                var firmwarePath = await DownloadFirmwareAsync(debugMode);
                if (string.IsNullOrEmpty(firmwarePath))
                {
                    Console.ForegroundColor = ConsoleColor.Red;
                    Console.WriteLine("❌ Failed to download firmware");
                    Console.ResetColor();
                    return;
                }

                // Detect ESP32 port if not specified
                if (string.IsNullOrEmpty(comPort) || comPort == "auto")
                {
                    comPort = DetectESP32Port();
                    if (string.IsNullOrEmpty(comPort))
                    {
                        Console.ForegroundColor = ConsoleColor.Red;
                        Console.WriteLine("❌ No ESP32 device detected. Please specify --port manually.");
                        Console.ResetColor();
                        return;
                    }
                }

                Console.WriteLine($"📡 Using COM port: {comPort}");
                Console.WriteLine($"💾 Firmware file: {Path.GetFileName(firmwarePath)}");
                Console.WriteLine();

                // Flash firmware
                await FlashFirmwareAsync(firmwarePath, comPort, debugMode);

                // Cleanup
                if (File.Exists(firmwarePath))
                {
                    File.Delete(firmwarePath);
                }

                Console.ForegroundColor = ConsoleColor.Green;
                Console.WriteLine("✅ Flash operation completed!");
                Console.ResetColor();
            }
            catch (Exception ex)
            {
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine($"❌ Flash mode error: {ex.Message}");
                if (debugMode)
                {
                    Console.WriteLine($"🔍 Stack trace: {ex.StackTrace}");
                }
                Console.ResetColor();
            }
        }



        /// <summary>
        /// Download firmware from VPS
        /// </summary>
        private async Task<string> DownloadFirmwareAsync(bool debugMode)
        {
            try
            {
                Console.WriteLine("📥 Downloading latest firmware from VPS...");
                
                var firmwareUrl = "http://217.154.58.14/firmware/octane_esp32s2_enhanced_latest.bin";
                var tempPath = Path.Combine(Path.GetTempPath(), "octane_firmware.bin");

                if (debugMode)
                {
                    Console.WriteLine($"🔍 Download URL: {firmwareUrl}");
                    Console.WriteLine($"🔍 Temp path: {tempPath}");
                }

                var response = await _httpClient.GetAsync(firmwareUrl);
                response.EnsureSuccessStatusCode();

                var firmwareData = await response.Content.ReadAsByteArrayAsync();
                await File.WriteAllBytesAsync(tempPath, firmwareData);

                Console.ForegroundColor = ConsoleColor.Green;
                Console.WriteLine($"✅ Firmware downloaded: {firmwareData.Length} bytes");
                Console.ResetColor();

                return tempPath;
            }
            catch (Exception ex)
            {
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine($"❌ Firmware download failed: {ex.Message}");
                Console.ResetColor();
                return null;
            }
        }

        /// <summary>
        /// Detect ESP32 COM port
        /// </summary>
        private string DetectESP32Port()
        {
            try
            {
                Console.WriteLine("🔍 Scanning for ESP32 devices...");
                
                // Simple port detection - try common ESP32 ports
                var commonPorts = new[] { "COM3", "COM4", "COM5", "COM6", "COM7", "COM8", "COM9", "COM10", "COM11", "COM12" };
                
                foreach (var port in commonPorts)
                {
                    if (System.IO.Ports.SerialPort.GetPortNames().Contains(port))
                    {
                        Console.WriteLine($"   Found potential ESP32 port: {port}");
                        return port;
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Port detection error: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Flash firmware using esptool
        /// </summary>
        private async Task FlashFirmwareAsync(string firmwarePath, string comPort, bool debugMode)
        {
            try
            {
                Console.WriteLine("🔥 Starting firmware flash...");
                Console.WriteLine("═══════════════════════════════");

                // Prepare esptool command
                var esptoolPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "tools", "esptool.exe");
                var arguments = $"--chip esp32s2 --port {comPort} --baud 921600 " +
                               $"--before default_reset --after hard_reset write_flash -z " +
                               $"--flash_mode dio --flash_freq 80m --flash_size 4MB " +
                               $"0x0 \"{firmwarePath}\"";

                if (debugMode)
                {
                    Console.WriteLine($"🔍 ESPTool path: {esptoolPath}");
                    Console.WriteLine($"🔍 Arguments: {arguments}");
                }

                var processInfo = new ProcessStartInfo
                {
                    FileName = esptoolPath,
                    Arguments = arguments,
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                Console.WriteLine("⚡ Flashing firmware...");
                
                using (var process = Process.Start(processInfo))
                {
                    var output = await process.StandardOutput.ReadToEndAsync();
                    var error = await process.StandardError.ReadToEndAsync();
                    
                    await process.WaitForExitAsync();

                    if (debugMode || process.ExitCode != 0)
                    {
                        Console.WriteLine("📋 ESPTool Output:");
                        Console.WriteLine(output);
                        if (!string.IsNullOrEmpty(error))
                        {
                            Console.ForegroundColor = ConsoleColor.Yellow;
                            Console.WriteLine("⚠️ ESPTool Errors:");
                            Console.WriteLine(error);
                            Console.ResetColor();
                        }
                    }

                    if (process.ExitCode == 0)
                    {
                        Console.ForegroundColor = ConsoleColor.Green;
                        Console.WriteLine("✅ Firmware flashed successfully!");
                        Console.ResetColor();
                    }
                    else
                    {
                        Console.ForegroundColor = ConsoleColor.Red;
                        Console.WriteLine($"❌ Flash failed with exit code: {process.ExitCode}");
                        Console.ResetColor();
                    }
                }
            }
            catch (Exception ex)
            {
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine($"❌ Flash operation failed: {ex.Message}");
                Console.ResetColor();
            }
        }
    }
}
