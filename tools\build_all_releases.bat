@echo off
REM Build All Octane Applications - Release Script
REM This script builds the desktop application, flasher, and debug flasher

title Octane Release Builder

echo.
echo ========================================
echo    Octane Release Builder v1.0.0
echo ========================================
echo.

REM Check if .NET SDK is installed
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: .NET SDK is not installed
    echo Please install .NET 8.0 SDK and try again
    pause
    exit /b 1
)

REM Get version from user
set /p VERSION="Enter version number (e.g., 4.2.1): "
if "%VERSION%"=="" (
    echo ERROR: Version is required
    pause
    exit /b 1
)

REM Create release directory
set RELEASE_DIR=releases\v%VERSION%
if exist "%RELEASE_DIR%" (
    echo Cleaning existing release directory...
    rmdir /s /q "%RELEASE_DIR%"
)
mkdir "%RELEASE_DIR%"

echo.
echo 🏗️  Building Octane Applications v%VERSION%
echo ==========================================

REM Build Debug Flasher (from current directory)
echo.
echo 🐛 Building Debug Flasher...
if exist "OctaneFlasherDebug.cs" (
    mkdir temp_debug_flasher
    cd temp_debug_flasher
    
    REM Create project file
    echo ^<Project Sdk="Microsoft.NET.Sdk"^> > OctaneFlasherDebug.csproj
    echo   ^<PropertyGroup^> >> OctaneFlasherDebug.csproj
    echo     ^<OutputType^>Exe^</OutputType^> >> OctaneFlasherDebug.csproj
    echo     ^<TargetFramework^>net8.0^</TargetFramework^> >> OctaneFlasherDebug.csproj
    echo     ^<Version^>%VERSION%-DEBUG^</Version^> >> OctaneFlasherDebug.csproj
    echo   ^</PropertyGroup^> >> OctaneFlasherDebug.csproj
    echo   ^<ItemGroup^> >> OctaneFlasherDebug.csproj
    echo     ^<PackageReference Include="System.Management" Version="8.0.0" /^> >> OctaneFlasherDebug.csproj
    echo     ^<PackageReference Include="System.IO.Ports" Version="8.0.0" /^> >> OctaneFlasherDebug.csproj
    echo   ^</ItemGroup^> >> OctaneFlasherDebug.csproj
    echo ^</Project^> >> OctaneFlasherDebug.csproj
    
    REM Copy debug flasher source
    copy "..\OctaneFlasherDebug.cs" "Program.cs"
    
    REM Build for Windows x64
    echo    Building Debug Flasher Windows x64...
    dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:PublishTrimmed=true -o "..\%RELEASE_DIR%\DebugFlasher-Windows-x64"
    if errorlevel 1 (
        echo    ❌ Debug flasher build failed
        cd ..
        rmdir /s /q temp_debug_flasher
        pause
        exit /b 1
    )
    
    cd ..
    rmdir /s /q temp_debug_flasher
    echo    ✅ Debug flasher built successfully
) else (
    echo    ⚠️  OctaneFlasherDebug.cs not found, skipping...
)

REM Create default config for debug flasher
echo.
echo ⚙️  Creating Debug Flasher Configuration...
if exist "%RELEASE_DIR%\DebugFlasher-Windows-x64" (
    mkdir "%RELEASE_DIR%\DebugFlasher-Windows-x64\logs" 2>nul
    echo { > "%RELEASE_DIR%\DebugFlasher-Windows-x64\flasher_config.json"
    echo   "DebugMode": true, >> "%RELEASE_DIR%\DebugFlasher-Windows-x64\flasher_config.json"
    echo   "SerialMonitorMode": false, >> "%RELEASE_DIR%\DebugFlasher-Windows-x64\flasher_config.json"
    echo   "VerboseLogging": true, >> "%RELEASE_DIR%\DebugFlasher-Windows-x64\flasher_config.json"
    echo   "SaveLogs": true, >> "%RELEASE_DIR%\DebugFlasher-Windows-x64\flasher_config.json"
    echo   "LogDirectory": "logs", >> "%RELEASE_DIR%\DebugFlasher-Windows-x64\flasher_config.json"
    echo   "SerialBaudRate": 115200, >> "%RELEASE_DIR%\DebugFlasher-Windows-x64\flasher_config.json"
    echo   "VpsUrl": "http://*************:3000", >> "%RELEASE_DIR%\DebugFlasher-Windows-x64\flasher_config.json"
    echo   "FirmwareEndpoint": "/firmware/esp32s2_firmware_latest.bin", >> "%RELEASE_DIR%\DebugFlasher-Windows-x64\flasher_config.json"
    echo   "AutoDetectPort": true, >> "%RELEASE_DIR%\DebugFlasher-Windows-x64\flasher_config.json"
    echo   "ShowHexData": false, >> "%RELEASE_DIR%\DebugFlasher-Windows-x64\flasher_config.json"
    echo   "MonitorAfterFlash": true, >> "%RELEASE_DIR%\DebugFlasher-Windows-x64\flasher_config.json"
    echo   "MonitorDuration": 30 >> "%RELEASE_DIR%\DebugFlasher-Windows-x64\flasher_config.json"
    echo } >> "%RELEASE_DIR%\DebugFlasher-Windows-x64\flasher_config.json"
)

REM Create README for release
echo.
echo 📝 Creating Release Documentation...
echo # Octane Debug Flasher Release v%VERSION% > "%RELEASE_DIR%\README.txt"
echo. >> "%RELEASE_DIR%\README.txt"
echo This release contains: >> "%RELEASE_DIR%\README.txt"
echo. >> "%RELEASE_DIR%\README.txt"
echo ## Debug Flasher >> "%RELEASE_DIR%\README.txt"
echo - DebugFlasher-Windows-x64\OctaneFlasherDebug.exe >> "%RELEASE_DIR%\README.txt"
echo - Advanced debugging and serial monitoring >> "%RELEASE_DIR%\README.txt"
echo - JSON configuration file: flasher_config.json >> "%RELEASE_DIR%\README.txt"
echo - Usage: OctaneFlasherDebug.exe --help >> "%RELEASE_DIR%\README.txt"
echo. >> "%RELEASE_DIR%\README.txt"
echo ## Debug Flasher Features >> "%RELEASE_DIR%\README.txt"
echo - Serial monitor mode: --monitor >> "%RELEASE_DIR%\README.txt"
echo - Verbose logging: --verbose >> "%RELEASE_DIR%\README.txt"
echo - Hex data display: --hex >> "%RELEASE_DIR%\README.txt"
echo - Custom baud rates: --baud 921600 >> "%RELEASE_DIR%\README.txt"
echo - Automatic ESP32 detection >> "%RELEASE_DIR%\README.txt"
echo - Post-flash monitoring >> "%RELEASE_DIR%\README.txt"
echo - Comprehensive logging to files >> "%RELEASE_DIR%\README.txt"
echo. >> "%RELEASE_DIR%\README.txt"
echo ## Usage Examples >> "%RELEASE_DIR%\README.txt"
echo - Flash and monitor: OctaneFlasherDebug.exe --debug --verbose >> "%RELEASE_DIR%\README.txt"
echo - Serial monitor only: OctaneFlasherDebug.exe --monitor --port COM10 >> "%RELEASE_DIR%\README.txt"
echo - Show hex data: OctaneFlasherDebug.exe --debug --hex --baud 921600 >> "%RELEASE_DIR%\README.txt"

echo.
echo 🎯 Built Applications:
echo =====================
if exist "%RELEASE_DIR%\DebugFlasher-Windows-x64\OctaneFlasherDebug.exe" (
    echo ✅ Debug Flasher (x64)
)

echo.
echo 🎉 Build completed successfully!
echo Release available in: %RELEASE_DIR%
echo.
echo 🚀 Next steps:
echo 1. Test the debug flasher
echo 2. Use --monitor mode to debug ESP32 communication
echo 3. Use --verbose for detailed logging
echo 4. Check logs directory for debugging information
echo.
pause
