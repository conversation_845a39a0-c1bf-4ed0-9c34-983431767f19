using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;

namespace OctaneFlasher.Core
{
    /// <summary>
    /// Handles application configuration loading and management
    /// </summary>
    public static class Configuration
    {
        private static JsonDocument? config;
        private static string buildMode = "development";

        /// <summary>
        /// Load configuration from config.json file
        /// </summary>
        public static async Task LoadConfigurationAsync()
        {
            try
            {
                var configPath = Path.Combine(AppContext.BaseDirectory, "config.json");
                if (File.Exists(configPath))
                {
                    var configJson = await File.ReadAllTextAsync(configPath);
                    config = JsonDocument.Parse(configJson);
                    buildMode = GetConfigValue("buildMode", "development");
                    Console.WriteLine($"✅ Configuration loaded: {buildMode} mode");
                }
                else
                {
                    Console.WriteLine("⚠️ Config file not found, using defaults");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Error loading config: {ex.Message}");
            }
        }

        /// <summary>
        /// Get configuration value by path
        /// </summary>
        public static string GetConfigValue(string path, string defaultValue)
        {
            try
            {
                if (config == null) return defaultValue;
                
                var parts = path.Split('.');
                JsonElement current = config.RootElement;
                
                foreach (var part in parts)
                {
                    if (current.TryGetProperty(part, out JsonElement next))
                    {
                        current = next;
                    }
                    else
                    {
                        return defaultValue;
                    }
                }
                
                return current.GetString() ?? defaultValue;
            }
            catch
            {
                return defaultValue;
            }
        }

        /// <summary>
        /// Check if a mode is allowed in current build
        /// </summary>
        public static bool IsModeAllowed(string mode)
        {
            try
            {
                if (config == null) return mode == "flash";
                
                var allowedModes = config.RootElement
                    .GetProperty("features")
                    .GetProperty(buildMode)
                    .GetProperty("allowedModes");
                
                foreach (var allowedMode in allowedModes.EnumerateArray())
                {
                    if (allowedMode.GetString() == mode)
                        return true;
                }
                
                return false;
            }
            catch
            {
                return mode == "flash";
            }
        }

        /// <summary>
        /// Show allowed modes for current build
        /// </summary>
        public static void ShowAllowedModes()
        {
            try
            {
                Console.WriteLine($"\nAllowed modes in {buildMode} build:");
                var allowedModes = config.RootElement
                    .GetProperty("features")
                    .GetProperty(buildMode)
                    .GetProperty("allowedModes");
                
                foreach (var mode in allowedModes.EnumerateArray())
                {
                    Console.WriteLine($"  - {mode.GetString()}");
                }
            }
            catch
            {
                Console.WriteLine("  - flash");
            }
        }

        /// <summary>
        /// Get current build mode
        /// </summary>
        public static string BuildMode => buildMode;

        /// <summary>
        /// Get version from config
        /// </summary>
        public static string Version => GetConfigValue("version", "3.1.0");
    }
}
