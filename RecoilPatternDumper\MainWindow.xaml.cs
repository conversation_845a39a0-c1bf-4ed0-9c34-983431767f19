using System.IO;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;
using RecoilPatternDumper.Models;
using RecoilPatternDumper.Services;
using System.Diagnostics;
using System.Windows.Media;

namespace RecoilPatternDumper
{
    public partial class MainWindow : Window
    {
        private DumperService? _dumperService;
        private AnalysisService _analysisService;
        private ExportService _exportService;
        private CancellationTokenSource? _cancellationTokenSource;
        private Dictionary<string, WeaponStatistics> _weaponStatistics = new();
        private List<OptimizedRecoilPattern> _optimizedPatterns = new();
        private List<DumpRunResult> _allResults = new();
        private Stopwatch _analysisStopwatch = new();

        public MainWindow()
        {
            InitializeComponent();
            _analysisService = new AnalysisService();
            _exportService = new ExportService();
            
            // Set default paths
            ExportPathTextBox.Text = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "RecoilAnalysis");
            
            // Initialize validation
            ValidateConfiguration();
        }

        #region Configuration Events

        private void BrowsePythonButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new OpenFileDialog
            {
                Title = "Select Python Executable",
                Filter = "Executable Files (*.exe)|*.exe|All Files (*.*)|*.*",
                FileName = "python.exe"
            };

            if (dialog.ShowDialog() == true)
            {
                PythonPathTextBox.Text = dialog.FileName;
            }
        }

        private void BrowseScriptButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new OpenFileDialog
            {
                Title = "Select main.py Script",
                Filter = "Python Files (*.py)|*.py|All Files (*.*)|*.*",
                FileName = "main.py"
            };

            if (dialog.ShowDialog() == true)
            {
                ScriptPathTextBox.Text = dialog.FileName;
            }
        }

        private void BrowseGameButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new System.Windows.Forms.FolderBrowserDialog
            {
                Description = "Select Rust Game Directory",
                ShowNewFolderButton = false
            };

            if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                GamePathTextBox.Text = dialog.SelectedPath;
            }
        }

        private void PythonPathTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ValidatePythonPath();
        }

        private void ScriptPathTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ValidateScriptPath();
        }

        private void GamePathTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ValidateGamePath();
        }

        private void RunCountSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            if (RunCountText != null)
            {
                RunCountText.Text = $"{(int)e.NewValue} runs";
            }
        }

        #endregion

        #region Analysis Events

        private async void StartAnalysisButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateConfiguration())
            {
                MessageBox.Show("Please fix configuration issues before starting analysis.", 
                               "Configuration Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                StartAnalysisButton.IsEnabled = false;
                StopAnalysisButton.IsEnabled = true;
                
                _cancellationTokenSource = new CancellationTokenSource();
                
                var pythonPath = PythonPathTextBox.Text;
                var scriptPath = ScriptPathTextBox.Text;
                var gamePath = GamePathTextBox.Text;
                var runCount = (int)RunCountSlider.Value;

                _dumperService = new DumperService(pythonPath, scriptPath, gamePath);
                _dumperService.ProgressUpdated += OnProgressUpdated;
                _dumperService.LogMessage += OnLogMessage;

                LogMessage("Starting recoil pattern analysis...");
                _analysisStopwatch.Restart();
                
                StatusText.Text = "Running dumps...";
                
                _allResults = await _dumperService.RunMultipleDumpsAsync(runCount, _cancellationTokenSource.Token);
                
                LogMessage("Analyzing results...");
                StatusText.Text = "Analyzing results...";
                
                _weaponStatistics = _analysisService.AnalyzeMultipleRuns(_allResults);
                _optimizedPatterns = _analysisService.GenerateOptimizedPatterns(_weaponStatistics);
                
                UpdateResultsTab();
                
                _analysisStopwatch.Stop();
                LogMessage($"Analysis completed in {_analysisStopwatch.Elapsed.TotalMinutes:F2} minutes");
                
                StatusText.Text = $"Analysis complete - {_weaponStatistics.Count} weapons analyzed";
                
                // Enable export buttons
                ExportButton.IsEnabled = true;
                CreatePackageButton.IsEnabled = true;
                
                // Auto-export if enabled
                if (AutoExportCheckBox.IsChecked == true)
                {
                    await AutoExportResults();
                }
            }
            catch (Exception ex)
            {
                LogMessage($"Error during analysis: {ex.Message}");
                MessageBox.Show($"Analysis failed: {ex.Message}", "Error", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                StartAnalysisButton.IsEnabled = true;
                StopAnalysisButton.IsEnabled = false;
                AnalysisProgressBar.Value = 0;
                ProgressText.Text = "Ready";
            }
        }

        private void StopAnalysisButton_Click(object sender, RoutedEventArgs e)
        {
            _cancellationTokenSource?.Cancel();
            LogMessage("Analysis cancelled by user");
            StatusText.Text = "Analysis cancelled";
        }

        private void ClearLogButton_Click(object sender, RoutedEventArgs e)
        {
            LogTextBox.Clear();
        }

        #endregion

        #region Results Events

        private void WeaponListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (WeaponListBox.SelectedItem is string weaponName && _weaponStatistics.ContainsKey(weaponName))
            {
                DisplayWeaponDetails(_weaponStatistics[weaponName]);
            }
        }

        #endregion

        #region Export Events

        private void BrowseExportButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new System.Windows.Forms.FolderBrowserDialog
            {
                Description = "Select Export Directory",
                ShowNewFolderButton = true
            };

            if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                ExportPathTextBox.Text = dialog.SelectedPath;
            }
        }

        private async void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            await ExportResults(false);
        }

        private async void CreatePackageButton_Click(object sender, RoutedEventArgs e)
        {
            await ExportResults(true);
        }

        #endregion

        #region Helper Methods

        private bool ValidateConfiguration()
        {
            bool isValid = true;
            
            isValid &= ValidatePythonPath();
            isValid &= ValidateScriptPath();
            isValid &= ValidateGamePath();
            
            StartAnalysisButton.IsEnabled = isValid;
            return isValid;
        }

        private bool ValidatePythonPath()
        {
            var path = PythonPathTextBox.Text;
            bool isValid = DumperService.ValidatePythonEnvironment(path);
            
            PythonValidationText.Text = isValid ? "✓ Python environment valid" : "✗ Invalid Python environment or missing UnityPy";
            PythonValidationText.Foreground = isValid ? Brushes.LightGreen : Brushes.LightCoral;
            
            return isValid;
        }

        private bool ValidateScriptPath()
        {
            var path = ScriptPathTextBox.Text;
            bool isValid = DumperService.ValidateDumperScript(path);
            
            ScriptValidationText.Text = isValid ? "✓ Script file found" : "✗ Script file not found";
            ScriptValidationText.Foreground = isValid ? Brushes.LightGreen : Brushes.LightCoral;
            
            return isValid;
        }

        private bool ValidateGamePath()
        {
            var path = GamePathTextBox.Text;
            bool isValid = DumperService.ValidateRustGamePath(path);
            
            GameValidationText.Text = isValid ? "✓ Rust game directory valid" : "✗ Invalid game directory or missing content.bundle";
            GameValidationText.Foreground = isValid ? Brushes.LightGreen : Brushes.LightCoral;
            
            return isValid;
        }

        private void OnProgressUpdated(object? sender, DumpProgressEventArgs e)
        {
            Dispatcher.Invoke(() =>
            {
                AnalysisProgressBar.Value = e.ProgressPercentage;
                ProgressText.Text = $"Run {e.CurrentRun}/{e.TotalRuns} ({e.SuccessfulRuns} success, {e.FailedRuns} failed)";
                
                if (_analysisStopwatch.IsRunning)
                {
                    TimerText.Text = $"Elapsed: {_analysisStopwatch.Elapsed:mm\\:ss}";
                }
            });
        }

        private void OnLogMessage(object? sender, string message)
        {
            Dispatcher.Invoke(() =>
            {
                LogMessage(message);
            });
        }

        private void LogMessage(string message)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            LogTextBox.AppendText($"[{timestamp}] {message}\n");
            LogTextBox.ScrollToEnd();
        }

        private void UpdateResultsTab()
        {
            WeaponListBox.Items.Clear();
            
            var sortedWeapons = _weaponStatistics
                .OrderByDescending(w => w.Value.QualityScore)
                .Select(w => w.Key)
                .ToList();
            
            foreach (var weaponName in sortedWeapons)
            {
                WeaponListBox.Items.Add(weaponName);
            }
            
            if (WeaponListBox.Items.Count > 0)
            {
                WeaponListBox.SelectedIndex = 0;
            }
        }

        private void DisplayWeaponDetails(WeaponStatistics stats)
        {
            WeaponDetailsPanel.Children.Clear();
            
            // Add weapon statistics display elements here
            // This would be a complex UI layout showing all the statistical data
            var titleBlock = new TextBlock
            {
                Text = $"{stats.WeaponName} - Quality Score: {stats.QualityScore:F2}/100",
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 10)
            };
            WeaponDetailsPanel.Children.Add(titleBlock);
            
            // Add more detailed statistics display here...
        }

        private async Task AutoExportResults()
        {
            try
            {
                var exportPath = ExportPathTextBox.Text;
                if (!Directory.Exists(exportPath))
                {
                    Directory.CreateDirectory(exportPath);
                }
                
                var report = _analysisService.GenerateDetailedReport(_weaponStatistics, _allResults);
                var packagePath = await _exportService.CreateExportPackageAsync(
                    _optimizedPatterns, _weaponStatistics, report, exportPath);
                
                LogMessage($"Results auto-exported to: {packagePath}");
            }
            catch (Exception ex)
            {
                LogMessage($"Auto-export failed: {ex.Message}");
            }
        }

        private async Task ExportResults(bool createPackage)
        {
            try
            {
                var exportPath = ExportPathTextBox.Text;
                if (string.IsNullOrEmpty(exportPath))
                {
                    MessageBox.Show("Please select an export directory.", "Export Error", 
                                   MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }
                
                if (!Directory.Exists(exportPath))
                {
                    Directory.CreateDirectory(exportPath);
                }
                
                ExportStatusText.Text = "Exporting...";
                
                if (createPackage)
                {
                    var report = _analysisService.GenerateDetailedReport(_weaponStatistics, _allResults);
                    var packagePath = await _exportService.CreateExportPackageAsync(
                        _optimizedPatterns, _weaponStatistics, report, exportPath);
                    
                    ExportStatusText.Text = $"Package created: {Path.GetFileName(packagePath)}";
                    
                    // Open the export directory
                    Process.Start("explorer.exe", packagePath);
                }
                else
                {
                    // Individual exports based on checkboxes
                    var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                    
                    if (ExportJsonCheckBox.IsChecked == true)
                    {
                        await _exportService.ExportOptimizedPatternsAsync(_optimizedPatterns, 
                            Path.Combine(exportPath, $"patterns_{timestamp}.json"));
                    }
                    
                    if (ExportCsvCheckBox.IsChecked == true)
                    {
                        await _exportService.ExportCsvDataAsync(_weaponStatistics, 
                            Path.Combine(exportPath, $"statistics_{timestamp}.csv"));
                    }
                    
                    if (ExportReportCheckBox.IsChecked == true)
                    {
                        var report = _analysisService.GenerateDetailedReport(_weaponStatistics, _allResults);
                        await _exportService.ExportDetailedReportAsync(report, 
                            Path.Combine(exportPath, $"report_{timestamp}.txt"));
                    }
                    
                    if (ExportESP32CheckBox.IsChecked == true)
                    {
                        await _exportService.ExportRecoilPatternsForESP32Async(_optimizedPatterns, 
                            Path.Combine(exportPath, $"recoil_patterns_{timestamp}.h"));
                    }
                    
                    if (ExportChartDataCheckBox.IsChecked == true)
                    {
                        await _exportService.ExportComparisonChartDataAsync(_weaponStatistics, 
                            Path.Combine(exportPath, $"chart_data_{timestamp}.json"));
                    }
                    
                    ExportStatusText.Text = "Export completed successfully";
                }
            }
            catch (Exception ex)
            {
                ExportStatusText.Text = $"Export failed: {ex.Message}";
                MessageBox.Show($"Export failed: {ex.Message}", "Export Error", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion
    }
}
