C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\RecoilController.exe
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\runtimes\win-x64\native\WebView2Loader.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\RecoilController.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\RecoilController.deps.json
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\RecoilController.runtimeconfig.json
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\RecoilController.pdb
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.CSharp.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.VisualBasic.Core.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.Win32.Primitives.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.Win32.Registry.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.AppContext.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Buffers.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Collections.Concurrent.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Collections.Immutable.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Collections.NonGeneric.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Collections.Specialized.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Collections.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.ComponentModel.Annotations.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.ComponentModel.DataAnnotations.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.ComponentModel.EventBasedAsync.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.ComponentModel.Primitives.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.ComponentModel.TypeConverter.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.ComponentModel.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Configuration.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Console.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Core.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Data.Common.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Data.DataSetExtensions.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Data.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Diagnostics.Contracts.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Diagnostics.Debug.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Diagnostics.FileVersionInfo.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Diagnostics.Process.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Diagnostics.StackTrace.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Diagnostics.TextWriterTraceListener.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Diagnostics.Tools.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Diagnostics.TraceSource.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Diagnostics.Tracing.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Drawing.Primitives.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Dynamic.Runtime.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Formats.Asn1.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Formats.Tar.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Globalization.Calendars.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Globalization.Extensions.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Globalization.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.IO.Compression.Brotli.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.IO.Compression.FileSystem.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.IO.Compression.ZipFile.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.IO.Compression.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.IO.FileSystem.AccessControl.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.IO.FileSystem.DriveInfo.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.IO.FileSystem.Primitives.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.IO.FileSystem.Watcher.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.IO.FileSystem.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.IO.IsolatedStorage.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.IO.MemoryMappedFiles.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.IO.Pipes.AccessControl.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.IO.Pipes.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.IO.UnmanagedMemoryStream.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.IO.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Linq.Expressions.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Linq.Parallel.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Linq.Queryable.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Linq.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Memory.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Net.Http.Json.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Net.Http.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Net.HttpListener.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Net.Mail.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Net.NameResolution.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Net.NetworkInformation.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Net.Ping.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Net.Primitives.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Net.Quic.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Net.Requests.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Net.Security.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Net.ServicePoint.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Net.Sockets.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Net.WebClient.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Net.WebHeaderCollection.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Net.WebProxy.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Net.WebSockets.Client.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Net.WebSockets.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Net.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Numerics.Vectors.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Numerics.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.ObjectModel.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Private.CoreLib.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Private.DataContractSerialization.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Private.Uri.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Private.Xml.Linq.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Private.Xml.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Reflection.DispatchProxy.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Reflection.Emit.ILGeneration.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Reflection.Emit.Lightweight.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Reflection.Emit.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Reflection.Extensions.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Reflection.Metadata.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Reflection.Primitives.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Reflection.TypeExtensions.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Reflection.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Resources.Reader.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Resources.ResourceManager.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Resources.Writer.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Runtime.CompilerServices.VisualC.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Runtime.Extensions.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Runtime.Handles.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Runtime.InteropServices.JavaScript.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Runtime.InteropServices.RuntimeInformation.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Runtime.InteropServices.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Runtime.Intrinsics.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Runtime.Loader.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Runtime.Numerics.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Runtime.Serialization.Formatters.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Runtime.Serialization.Json.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Runtime.Serialization.Primitives.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Runtime.Serialization.Xml.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Runtime.Serialization.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Runtime.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Security.AccessControl.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Security.Claims.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Security.Cryptography.Algorithms.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Security.Cryptography.Cng.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Security.Cryptography.Csp.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Security.Cryptography.Encoding.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Security.Cryptography.OpenSsl.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Security.Cryptography.Primitives.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Security.Cryptography.X509Certificates.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Security.Cryptography.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Security.Principal.Windows.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Security.Principal.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Security.SecureString.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Security.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.ServiceModel.Web.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.ServiceProcess.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Text.Encoding.CodePages.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Text.Encoding.Extensions.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Text.Encoding.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Text.RegularExpressions.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Threading.Channels.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Threading.Overlapped.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Threading.Tasks.Dataflow.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Threading.Tasks.Extensions.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Threading.Tasks.Parallel.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Threading.Tasks.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Threading.Thread.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Threading.ThreadPool.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Threading.Timer.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Threading.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Transactions.Local.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Transactions.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.ValueTuple.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Web.HttpUtility.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Web.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Windows.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Xml.Linq.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Xml.ReaderWriter.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Xml.Serialization.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Xml.XDocument.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Xml.XPath.XDocument.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Xml.XPath.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Xml.XmlDocument.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Xml.XmlSerializer.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Xml.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\mscorlib.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\netstandard.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Accessibility.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\DirectWriteForwarder.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.VisualBasic.Forms.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.VisualBasic.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.Win32.Registry.AccessControl.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.Win32.SystemEvents.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\PresentationCore.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\PresentationFramework-SystemCore.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\PresentationFramework-SystemData.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\PresentationFramework-SystemDrawing.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\PresentationFramework-SystemXml.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\PresentationFramework-SystemXmlLinq.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\PresentationFramework.Aero.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\PresentationFramework.Aero2.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\PresentationFramework.AeroLite.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\PresentationFramework.Classic.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\PresentationFramework.Luna.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\PresentationFramework.Royale.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\PresentationFramework.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\PresentationUI.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ReachFramework.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Configuration.ConfigurationManager.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Design.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Diagnostics.EventLog.Messages.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Diagnostics.EventLog.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Diagnostics.PerformanceCounter.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.DirectoryServices.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Drawing.Common.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Drawing.Design.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Drawing.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.IO.Packaging.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Printing.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Resources.Extensions.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Security.Cryptography.Pkcs.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Security.Cryptography.ProtectedData.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Security.Cryptography.Xml.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Security.Permissions.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Threading.AccessControl.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Windows.Controls.Ribbon.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Windows.Extensions.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Windows.Forms.Design.Editors.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Windows.Forms.Design.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Windows.Forms.Primitives.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Windows.Forms.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Windows.Input.Manipulations.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Windows.Presentation.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Xaml.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\UIAutomationClient.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\UIAutomationClientSideProviders.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\UIAutomationProvider.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\UIAutomationTypes.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\WindowsBase.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\WindowsFormsIntegration.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.DiaSymReader.Native.amd64.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.IO.Compression.Native.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\clretwrc.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\clrgc.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\clrjit.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\coreclr.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\createdump.exe
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\hostfxr.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\hostpolicy.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\mscordaccore.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\mscordaccore_amd64_amd64_8.0.1725.26602.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\mscordbi.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\mscorrc.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\msquic.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\D3DCompiler_47_cor3.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\PenImc_cor3.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\PresentationNative_cor3.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\vcruntime140_cor3.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\wpfgfx_cor3.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\cs\Microsoft.VisualBasic.Forms.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\cs\PresentationCore.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\cs\PresentationFramework.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\cs\PresentationUI.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\cs\ReachFramework.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\cs\System.Windows.Controls.Ribbon.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\cs\System.Windows.Forms.Design.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\cs\System.Windows.Forms.Primitives.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\cs\System.Windows.Forms.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\cs\System.Windows.Input.Manipulations.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\cs\System.Xaml.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\cs\UIAutomationClient.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\cs\UIAutomationClientSideProviders.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\cs\UIAutomationProvider.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\cs\UIAutomationTypes.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\cs\WindowsBase.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\cs\WindowsFormsIntegration.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\de\Microsoft.VisualBasic.Forms.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\de\PresentationCore.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\de\PresentationFramework.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\de\PresentationUI.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\de\ReachFramework.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\de\System.Windows.Controls.Ribbon.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\de\System.Windows.Forms.Design.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\de\System.Windows.Forms.Primitives.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\de\System.Windows.Forms.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\de\System.Windows.Input.Manipulations.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\de\System.Xaml.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\de\UIAutomationClient.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\de\UIAutomationClientSideProviders.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\de\UIAutomationProvider.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\de\UIAutomationTypes.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\de\WindowsBase.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\de\WindowsFormsIntegration.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\es\Microsoft.VisualBasic.Forms.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\es\PresentationCore.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\es\PresentationFramework.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\es\PresentationUI.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\es\ReachFramework.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\es\System.Windows.Controls.Ribbon.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\es\System.Windows.Forms.Design.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\es\System.Windows.Forms.Primitives.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\es\System.Windows.Forms.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\es\System.Windows.Input.Manipulations.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\es\System.Xaml.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\es\UIAutomationClient.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\es\UIAutomationClientSideProviders.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\es\UIAutomationProvider.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\es\UIAutomationTypes.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\es\WindowsBase.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\es\WindowsFormsIntegration.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\fr\Microsoft.VisualBasic.Forms.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\fr\PresentationCore.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\fr\PresentationFramework.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\fr\PresentationUI.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\fr\ReachFramework.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\fr\System.Windows.Controls.Ribbon.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\fr\System.Windows.Forms.Design.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\fr\System.Windows.Forms.Primitives.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\fr\System.Windows.Forms.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\fr\System.Windows.Input.Manipulations.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\fr\System.Xaml.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\fr\UIAutomationClient.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\fr\UIAutomationClientSideProviders.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\fr\UIAutomationProvider.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\fr\UIAutomationTypes.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\fr\WindowsBase.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\fr\WindowsFormsIntegration.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\it\Microsoft.VisualBasic.Forms.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\it\PresentationCore.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\it\PresentationFramework.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\it\PresentationUI.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\it\ReachFramework.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\it\System.Windows.Controls.Ribbon.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\it\System.Windows.Forms.Design.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\it\System.Windows.Forms.Primitives.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\it\System.Windows.Forms.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\it\System.Windows.Input.Manipulations.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\it\System.Xaml.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\it\UIAutomationClient.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\it\UIAutomationClientSideProviders.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\it\UIAutomationProvider.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\it\UIAutomationTypes.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\it\WindowsBase.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\it\WindowsFormsIntegration.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ja\Microsoft.VisualBasic.Forms.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ja\PresentationCore.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ja\PresentationFramework.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ja\PresentationUI.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ja\ReachFramework.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ja\System.Windows.Controls.Ribbon.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ja\System.Windows.Forms.Design.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ja\System.Windows.Forms.Primitives.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ja\System.Windows.Forms.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ja\System.Windows.Input.Manipulations.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ja\System.Xaml.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ja\UIAutomationClient.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ja\UIAutomationClientSideProviders.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ja\UIAutomationProvider.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ja\UIAutomationTypes.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ja\WindowsBase.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ja\WindowsFormsIntegration.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ko\Microsoft.VisualBasic.Forms.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ko\PresentationCore.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ko\PresentationFramework.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ko\PresentationUI.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ko\ReachFramework.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ko\System.Windows.Controls.Ribbon.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ko\System.Windows.Forms.Design.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ko\System.Windows.Forms.Primitives.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ko\System.Windows.Forms.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ko\System.Windows.Input.Manipulations.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ko\System.Xaml.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ko\UIAutomationClient.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ko\UIAutomationClientSideProviders.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ko\UIAutomationProvider.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ko\UIAutomationTypes.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ko\WindowsBase.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ko\WindowsFormsIntegration.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pl\Microsoft.VisualBasic.Forms.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pl\PresentationCore.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pl\PresentationFramework.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pl\PresentationUI.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pl\ReachFramework.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pl\System.Windows.Controls.Ribbon.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pl\System.Windows.Forms.Design.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pl\System.Windows.Forms.Primitives.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pl\System.Windows.Forms.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pl\System.Windows.Input.Manipulations.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pl\System.Xaml.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pl\UIAutomationClient.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pl\UIAutomationClientSideProviders.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pl\UIAutomationProvider.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pl\UIAutomationTypes.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pl\WindowsBase.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pl\WindowsFormsIntegration.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pt-BR\Microsoft.VisualBasic.Forms.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pt-BR\PresentationCore.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pt-BR\PresentationFramework.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pt-BR\PresentationUI.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pt-BR\ReachFramework.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pt-BR\System.Windows.Controls.Ribbon.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pt-BR\System.Windows.Forms.Design.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pt-BR\System.Windows.Forms.Primitives.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pt-BR\System.Windows.Forms.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pt-BR\System.Windows.Input.Manipulations.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pt-BR\System.Xaml.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pt-BR\UIAutomationClient.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pt-BR\UIAutomationClientSideProviders.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pt-BR\UIAutomationProvider.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pt-BR\UIAutomationTypes.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pt-BR\WindowsBase.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pt-BR\WindowsFormsIntegration.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ru\Microsoft.VisualBasic.Forms.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ru\PresentationCore.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ru\PresentationFramework.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ru\PresentationUI.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ru\ReachFramework.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ru\System.Windows.Controls.Ribbon.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ru\System.Windows.Forms.Design.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ru\System.Windows.Forms.Primitives.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ru\System.Windows.Forms.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ru\System.Windows.Input.Manipulations.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ru\System.Xaml.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ru\UIAutomationClient.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ru\UIAutomationClientSideProviders.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ru\UIAutomationProvider.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ru\UIAutomationTypes.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ru\WindowsBase.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ru\WindowsFormsIntegration.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\tr\Microsoft.VisualBasic.Forms.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\tr\PresentationCore.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\tr\PresentationFramework.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\tr\PresentationUI.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\tr\ReachFramework.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\tr\System.Windows.Controls.Ribbon.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\tr\System.Windows.Forms.Design.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\tr\System.Windows.Forms.Primitives.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\tr\System.Windows.Forms.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\tr\System.Windows.Input.Manipulations.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\tr\System.Xaml.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\tr\UIAutomationClient.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\tr\UIAutomationClientSideProviders.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\tr\UIAutomationProvider.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\tr\UIAutomationTypes.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\tr\WindowsBase.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\tr\WindowsFormsIntegration.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\zh-Hans\Microsoft.VisualBasic.Forms.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\zh-Hans\PresentationCore.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\zh-Hans\PresentationFramework.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\zh-Hans\PresentationUI.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\zh-Hans\ReachFramework.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\zh-Hans\System.Windows.Controls.Ribbon.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\zh-Hans\System.Windows.Forms.Design.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\zh-Hans\System.Windows.Forms.Primitives.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\zh-Hans\System.Windows.Forms.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\zh-Hans\System.Windows.Input.Manipulations.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\zh-Hans\System.Xaml.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\zh-Hans\UIAutomationClient.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\zh-Hans\UIAutomationClientSideProviders.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\zh-Hans\UIAutomationProvider.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\zh-Hans\UIAutomationTypes.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\zh-Hans\WindowsBase.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\zh-Hans\WindowsFormsIntegration.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\zh-Hant\Microsoft.VisualBasic.Forms.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\zh-Hant\PresentationCore.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\zh-Hant\PresentationFramework.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\zh-Hant\PresentationUI.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\zh-Hant\ReachFramework.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\zh-Hant\System.Windows.Controls.Ribbon.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\zh-Hant\System.Windows.Forms.Design.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\zh-Hant\System.Windows.Forms.Primitives.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\zh-Hant\System.Windows.Forms.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\zh-Hant\System.Windows.Input.Manipulations.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\zh-Hant\System.Xaml.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\zh-Hant\UIAutomationClient.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\zh-Hant\UIAutomationClientSideProviders.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\zh-Hant\UIAutomationProvider.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\zh-Hant\UIAutomationTypes.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\zh-Hant\WindowsBase.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\zh-Hant\WindowsFormsIntegration.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\CommunityToolkit.Mvvm.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.Extensions.Configuration.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.Extensions.Configuration.Abstractions.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.Extensions.Configuration.Binder.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.Extensions.Configuration.CommandLine.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.Extensions.Configuration.EnvironmentVariables.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.Extensions.Configuration.FileExtensions.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.Extensions.Configuration.Json.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.Extensions.Configuration.UserSecrets.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.Extensions.FileProviders.Abstractions.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.Extensions.FileProviders.Physical.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.Extensions.FileSystemGlobbing.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.Extensions.Hosting.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.Extensions.Hosting.Abstractions.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.Extensions.Logging.Configuration.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.Extensions.Logging.Console.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.Extensions.Logging.Debug.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.Extensions.Logging.EventLog.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.Extensions.Logging.EventSource.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.Extensions.Options.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.Extensions.Options.ConfigurationExtensions.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.Web.WebView2.Core.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.Web.WebView2.WinForms.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.Web.WebView2.Wpf.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Microsoft.Xaml.Behaviors.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ModernWpf.Controls.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ModernWpf.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\Newtonsoft.Json.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.CodeDom.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.IO.Pipelines.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.IO.Ports.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Management.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Text.Encodings.Web.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\System.Text.Json.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\af-ZA\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\af-ZA\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\am-ET\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\am-ET\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ar-SA\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ar-SA\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\az-Latn-AZ\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\az-Latn-AZ\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\be-BY\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\be-BY\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\bg-BG\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\bg-BG\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\bn-BD\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\bn-BD\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\bs-Latn-BA\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\bs-Latn-BA\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ca-ES\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ca-ES\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\cs-CZ\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\cs-CZ\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\da-DK\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\da-DK\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\de-DE\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\de-DE\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\el-GR\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\el-GR\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\en-GB\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\en-GB\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\es-ES\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\es-ES\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\es-MX\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\es-MX\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\et-EE\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\et-EE\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\eu-ES\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\eu-ES\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\fa-IR\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\fa-IR\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\fi-FI\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\fi-FI\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\fil-PH\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\fil-PH\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\fr-CA\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\fr-CA\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\fr-FR\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\fr-FR\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\gl-ES\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\gl-ES\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\he-IL\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\he-IL\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\hi-IN\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\hi-IN\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\hr-HR\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\hr-HR\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\hu-HU\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\hu-HU\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\id-ID\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\id-ID\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\is-IS\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\is-IS\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\it-IT\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\it-IT\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ja-JP\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ja-JP\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ka-GE\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ka-GE\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\kk-KZ\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\kk-KZ\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\km-KH\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\km-KH\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\kn-IN\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\kn-IN\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ko-KR\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ko-KR\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\lo-LA\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\lo-LA\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\lt-LT\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\lt-LT\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\lv-LV\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\lv-LV\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\mk-MK\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\mk-MK\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ml-IN\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ml-IN\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ms-MY\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ms-MY\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\nb-NO\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\nb-NO\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\nl-NL\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\nl-NL\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\nn-NO\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\nn-NO\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pl-PL\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pl-PL\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pt-BR\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pt-BR\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pt-PT\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\pt-PT\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ro-RO\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ro-RO\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ru-RU\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ru-RU\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\sk-SK\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\sk-SK\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\sl-SI\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\sl-SI\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\sq-AL\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\sq-AL\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\sr-Latn-RS\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\sr-Latn-RS\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\sv-SE\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\sv-SE\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\sw-KE\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\sw-KE\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ta-IN\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\ta-IN\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\te-IN\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\te-IN\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\th-TH\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\th-TH\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\tr-TR\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\tr-TR\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\uk-UA\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\uk-UA\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\uz-Latn-UZ\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\uz-Latn-UZ\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\vi-VN\ModernWpf.Controls.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\vi-VN\ModernWpf.resources.dll
C:\Users\<USER>\Desktop\recoil\desktop-app\builds\debug\WebView2Loader.dll
