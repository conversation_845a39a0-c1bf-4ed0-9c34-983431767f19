const { Client, GatewayIntentBits, SlashCommandBuilder, EmbedBuilder, REST, Routes } = require('discord.js');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
require('dotenv').config();

class OctaneDiscordBot {
    constructor() {
        // Validate Discord token
        this.token = process.env.DISCORD_TOKEN || process.env.DISCORD_BOT_TOKEN;
        if (!this.token) {
            console.error('❌ Discord Bot - No token found in environment variables');
            console.error('   Please set DISCORD_TOKEN or DISCORD_BOT_TOKEN in .env file');
            process.exit(1);
        }

        console.log('✅ Discord Bot - Token loaded successfully');
        console.log(`   Token preview: ${this.token.substring(0, 20)}...`);

        this.client = new Client({
            intents: [
                GatewayIntentBits.Guilds,
                GatewayIntentBits.GuildMessages,
                GatewayIntentBits.MessageContent
            ]
        });

        this.commands = new Map();
        this.isReady = false;
        this.db = null;

        this.initializeDatabase();
        this.setupCommands();
        this.setupEventHandlers();
    }

    initializeDatabase() {
        const dbPath = path.join(__dirname, 'octane.db');
        this.db = new sqlite3.Database(dbPath, (err) => {
            if (err) {
                console.error('❌ Discord Bot - Database connection error:', err);
            } else {
                console.log('✅ Discord Bot - Connected to database');
            }
        });
    }

    setupCommands() {
        // System Status Command
        this.commands.set('status', {
            data: new SlashCommandBuilder()
                .setName('status')
                .setDescription('Check system status'),
            execute: async (interaction) => {
                const embed = new EmbedBuilder()
                    .setTitle('🟢 Octane System Status')
                    .setColor(0x00ff88)
                    .addFields(
                        { name: 'Backend', value: '✅ Online', inline: true },
                        { name: 'Database', value: '✅ Connected', inline: true },
                        { name: 'Discord Bot', value: '✅ Active', inline: true },
                        { name: 'Uptime', value: this.getUptime(), inline: true }
                    )
                    .setTimestamp();
                
                await interaction.reply({ embeds: [embed] });
            }
        });

        // Create License Command
        this.commands.set('create-license', {
            data: new SlashCommandBuilder()
                .setName('create-license')
                .setDescription('Create a new license key')
                .addStringOption(option =>
                    option.setName('duration')
                        .setDescription('License duration')
                        .setRequired(true)
                        .addChoices(
                            { name: '1 Day', value: '1day' },
                            { name: '1 Week', value: '1week' },
                            { name: '1 Month', value: '1month' },
                            { name: '3 Months', value: '3months' }
                        ))
                .addStringOption(option =>
                    option.setName('username')
                        .setDescription('Username for the license')
                        .setRequired(false)),
            execute: async (interaction) => {
                const duration = interaction.options.getString('duration');
                const username = interaction.options.getString('username') || 'Discord User';
                
                try {
                    const license = await this.createLicense(duration, username, interaction.user.username);
                    
                    const embed = new EmbedBuilder()
                        .setTitle('🔑 License Created Successfully')
                        .setColor(0x00d4ff)
                        .addFields(
                            { name: 'License Key', value: `\`${license.key}\``, inline: false },
                            { name: 'Duration', value: duration, inline: true },
                            { name: 'Created For', value: username, inline: true },
                            { name: 'Created By', value: interaction.user.username, inline: true }
                        )
                        .setTimestamp();
                    
                    await interaction.reply({ embeds: [embed], ephemeral: true });
                    
                    // Log to Discord webhook
                    await this.sendWebhook('SECURITY', {
                        embeds: [new EmbedBuilder()
                            .setTitle('🔑 License Created via Discord')
                            .setColor(0x00ff88)
                            .addFields(
                                { name: 'Created By', value: interaction.user.username, inline: true },
                                { name: 'Duration', value: duration, inline: true },
                                { name: 'Username', value: username, inline: true }
                            )
                            .setTimestamp()]
                    });
                } catch (error) {
                    console.error('Error creating license:', error);
                    await interaction.reply({ content: '❌ Failed to create license', ephemeral: true });
                }
            }
        });

        // License Stats Command
        this.commands.set('stats', {
            data: new SlashCommandBuilder()
                .setName('stats')
                .setDescription('Show license statistics'),
            execute: async (interaction) => {
                try {
                    const stats = await this.getLicenseStats();
                    
                    const embed = new EmbedBuilder()
                        .setTitle('📊 License Statistics')
                        .setColor(0x00ff88)
                        .addFields(
                            { name: 'Total Licenses', value: stats.total.toString(), inline: true },
                            { name: 'Active Licenses', value: stats.active.toString(), inline: true },
                            { name: 'Expired Licenses', value: stats.expired.toString(), inline: true }
                        )
                        .setTimestamp();
                    
                    await interaction.reply({ embeds: [embed] });
                } catch (error) {
                    console.error('Error getting stats:', error);
                    await interaction.reply({ content: '❌ Failed to get statistics', ephemeral: true });
                }
            }
        });

        // Emergency Disable Command
        this.commands.set('emergency-disable', {
            data: new SlashCommandBuilder()
                .setName('emergency-disable')
                .setDescription('Emergency disable authentication (Admin only)'),
            execute: async (interaction) => {
                // Check if user has admin permissions
                if (!interaction.member.permissions.has('ADMINISTRATOR')) {
                    await interaction.reply({ content: '❌ Admin permissions required', ephemeral: true });
                    return;
                }
                
                const embed = new EmbedBuilder()
                    .setTitle('🚨 Emergency Disable Activated')
                    .setColor(0xff4757)
                    .setDescription('Authentication has been temporarily disabled')
                    .addFields(
                        { name: 'Activated By', value: interaction.user.username, inline: true },
                        { name: 'Time', value: new Date().toISOString(), inline: true }
                    )
                    .setTimestamp();
                
                await interaction.reply({ embeds: [embed] });
                
                // Send to security webhook
                await this.sendWebhook('SECURITY', {
                    embeds: [embed]
                });
            }
        });

        // Health Check Command
        this.commands.set('health', {
            data: new SlashCommandBuilder()
                .setName('health')
                .setDescription('Check system health'),
            execute: async (interaction) => {
                try {
                    const response = await fetch('http://localhost:3000/health');
                    const health = await response.json();
                    
                    const embed = new EmbedBuilder()
                        .setTitle('🏥 System Health Check')
                        .setColor(0x00ff88)
                        .addFields(
                            { name: 'Status', value: health.status === 'healthy' ? '✅ Healthy' : '❌ Unhealthy', inline: true },
                            { name: 'Uptime', value: this.formatUptime(health.uptime), inline: true },
                            { name: 'Memory Usage', value: `${Math.round(health.memory.heapUsed / 1024 / 1024)}MB`, inline: true }
                        )
                        .setTimestamp();
                    
                    await interaction.reply({ embeds: [embed] });
                } catch (error) {
                    console.error('Error checking health:', error);
                    await interaction.reply({ content: '❌ Failed to check system health', ephemeral: true });
                }
            }
        });
    }

    setupEventHandlers() {
        this.client.once('ready', () => {
            console.log(`✅ Discord bot logged in as ${this.client.user.tag}`);
            this.isReady = true;
            this.registerCommands();
        });

        this.client.on('interactionCreate', async (interaction) => {
            if (!interaction.isChatInputCommand()) return;

            const command = this.commands.get(interaction.commandName);
            if (!command) return;

            try {
                await command.execute(interaction);
            } catch (error) {
                console.error('Error executing command:', error);
                const reply = { content: '❌ There was an error executing this command!', ephemeral: true };
                
                if (interaction.replied || interaction.deferred) {
                    await interaction.followUp(reply);
                } else {
                    await interaction.reply(reply);
                }
            }
        });

        this.client.on('error', (error) => {
            console.error('Discord client error:', error);
        });
    }

    async registerCommands() {
        try {
            const rest = new REST({ version: '10' }).setToken(this.token);
            const commands = Array.from(this.commands.values()).map(cmd => cmd.data.toJSON());

            console.log('🔄 Registering Discord slash commands...');

            await rest.put(
                Routes.applicationCommands(this.client.user.id),
                { body: commands }
            );

            console.log(`✅ Successfully registered ${commands.length} slash commands`);
        } catch (error) {
            console.error('❌ Error registering commands:', error);
            console.error('   Token being used:', this.token ? `${this.token.substring(0, 20)}...` : 'NONE');
        }
    }

    async createLicense(duration, username, createdBy) {
        return new Promise((resolve, reject) => {
            const licenseKey = this.generateLicenseKey();
            const expiresAt = this.calculateExpiration(duration);
            
            this.db.run(
                `INSERT INTO licenses (key, duration, expires_at, notes, created_by) VALUES (?, ?, ?, ?, ?)`,
                [licenseKey, duration, expiresAt.toISOString(), `Created for ${username}`, createdBy],
                function(err) {
                    if (err) {
                        reject(err);
                    } else {
                        resolve({
                            id: this.lastID,
                            key: licenseKey,
                            duration: duration,
                            expiresAt: expiresAt
                        });
                    }
                }
            );
        });
    }

    async getLicenseStats() {
        return new Promise((resolve, reject) => {
            this.db.all(
                `SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active,
                    SUM(CASE WHEN datetime(expires_at) < datetime('now') THEN 1 ELSE 0 END) as expired
                FROM licenses`,
                [],
                (err, rows) => {
                    if (err) {
                        reject(err);
                    } else {
                        resolve(rows[0] || { total: 0, active: 0, expired: 0 });
                    }
                }
            );
        });
    }

    generateLicenseKey() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        const segments = [];
        
        for (let i = 0; i < 3; i++) {
            let segment = '';
            for (let j = 0; j < 3; j++) {
                segment += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            segments.push(segment);
        }
        
        return segments.join('-');
    }

    calculateExpiration(duration) {
        const now = new Date();
        switch (duration) {
            case '1day': return new Date(now.getTime() + 24 * 60 * 60 * 1000);
            case '1week': return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
            case '1month': return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
            case '3months': return new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000);
            case '6months': return new Date(now.getTime() + 180 * 24 * 60 * 60 * 1000);
            case '1year': return new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000);
            case 'lifetime': return new Date('2099-12-31');
            default: return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
        }
    }

    getUptime() {
        const uptime = process.uptime();
        const hours = Math.floor(uptime / 3600);
        const minutes = Math.floor((uptime % 3600) / 60);
        return `${hours}h ${minutes}m`;
    }

    formatUptime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        return `${hours}h ${minutes}m`;
    }

    async sendWebhook(type, data) {
        const webhooks = {
            'SECURITY': process.env.SECURITY_WEBHOOK || '1398506465882411079/49oCxzr04R0v7JwYhpxveww1x3_H6pW7VoDocPa6FYPaykMJJegkNddJjkqh2CsbYbKy',
            'ESP32': process.env.ESP32_WEBHOOK || '1398551599818866719/JqAUxaYYv4V2o1ncPfZeB2TtloxU3roht5r4sT3S_7MQ4hN3m3AU3Ah8EAKCQIFxRDLW',
            'BACKEND': process.env.BACKEND_WEBHOOK || '1398551716869443654/FIcTJWf78O1reAqOMAM-1vLylabv_9QAe9LZ7EtHPB-QGsLcL55iJp04VcxcZx7Xv63i'
        };

        const webhookUrl = webhooks[type];
        if (!webhookUrl) return false;

        try {
            const response = await fetch(`https://discord.com/api/webhooks/${webhookUrl}`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            });
            return response.ok;
        } catch (error) {
            console.error(`Webhook error (${type}):`, error);
            return false;
        }
    }

    async start() {
        try {
            console.log('🔄 Starting Discord bot...');
            console.log(`   Using token: ${this.token.substring(0, 20)}...`);
            await this.client.login(this.token);
            console.log('🚀 Discord bot started successfully');
        } catch (error) {
            console.error('❌ Failed to start Discord bot:', error);
            console.error('   Token used:', this.token ? `${this.token.substring(0, 20)}...` : 'NONE');
            console.error('   Full error:', error.message);
            process.exit(1);
        }
    }

    async stop() {
        if (this.db) {
            this.db.close();
        }
        if (this.client) {
            await this.client.destroy();
        }
        console.log('🛑 Discord bot stopped');
    }
}

// Initialize and start the bot
const bot = new OctaneDiscordBot();

// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('🛑 Received SIGINT, shutting down Discord bot...');
    await bot.stop();
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('🛑 Received SIGTERM, shutting down Discord bot...');
    await bot.stop();
    process.exit(0);
});

// Start the bot
bot.start();
