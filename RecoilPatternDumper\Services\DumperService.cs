using System.Diagnostics;
using System.Text;
using Newtonsoft.Json;
using RecoilPatternDumper.Models;

namespace RecoilPatternDumper.Services
{
    public class DumperService
    {
        private readonly string _pythonExecutable;
        private readonly string _dumperScriptPath;
        private readonly string _rustGamePath;

        public event EventHandler<DumpProgressEventArgs>? ProgressUpdated;
        public event EventHandler<string>? LogMessage;

        public DumperService(string pythonExecutable, string dumperScriptPath, string rustGamePath)
        {
            _pythonExecutable = pythonExecutable;
            _dumperScriptPath = dumperScriptPath;
            _rustGamePath = rustGamePath;
        }

        public async Task<List<DumpRunResult>> RunMultipleDumpsAsync(int numberOfRuns, CancellationToken cancellationToken = default)
        {
            var results = new List<DumpRunResult>();
            
            LogMessage?.Invoke(this, $"Starting {numberOfRuns} dump runs...");

            for (int i = 1; i <= numberOfRuns; i++)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                LogMessage?.Invoke(this, $"Starting dump run {i}/{numberOfRuns}");
                
                var result = await RunSingleDumpAsync(i, cancellationToken);
                results.Add(result);

                ProgressUpdated?.Invoke(this, new DumpProgressEventArgs
                {
                    CurrentRun = i,
                    TotalRuns = numberOfRuns,
                    SuccessfulRuns = results.Count(r => r.Success),
                    FailedRuns = results.Count(r => !r.Success)
                });

                if (result.Success)
                {
                    LogMessage?.Invoke(this, $"Dump run {i} completed successfully in {result.ExecutionTime.TotalSeconds:F2}s");
                }
                else
                {
                    LogMessage?.Invoke(this, $"Dump run {i} failed: {result.ErrorMessage}");
                }

                // Small delay between runs to prevent system overload
                if (i < numberOfRuns)
                {
                    await Task.Delay(1000, cancellationToken);
                }
            }

            LogMessage?.Invoke(this, $"Completed all dump runs. Success: {results.Count(r => r.Success)}, Failed: {results.Count(r => !r.Success)}");
            return results;
        }

        private async Task<DumpRunResult> RunSingleDumpAsync(int runNumber, CancellationToken cancellationToken)
        {
            var result = new DumpRunResult
            {
                RunNumber = runNumber,
                Timestamp = DateTime.Now,
                Success = false
            };

            var stopwatch = Stopwatch.StartNew();

            try
            {
                var processInfo = new ProcessStartInfo
                {
                    FileName = _pythonExecutable,
                    Arguments = $"\"{_dumperScriptPath}\"",
                    UseShellExecute = false,
                    RedirectStandardInput = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true,
                    WorkingDirectory = Path.GetDirectoryName(_dumperScriptPath)
                };

                using var process = new Process { StartInfo = processInfo };
                
                var outputBuilder = new StringBuilder();
                var errorBuilder = new StringBuilder();

                process.OutputDataReceived += (sender, e) =>
                {
                    if (!string.IsNullOrEmpty(e.Data))
                        outputBuilder.AppendLine(e.Data);
                };

                process.ErrorDataReceived += (sender, e) =>
                {
                    if (!string.IsNullOrEmpty(e.Data))
                        errorBuilder.AppendLine(e.Data);
                };

                process.Start();
                process.BeginOutputReadLine();
                process.BeginErrorReadLine();

                // Send the Rust game path to the Python script
                await process.StandardInput.WriteLineAsync(_rustGamePath);
                await process.StandardInput.FlushAsync();

                // Wait for the process to complete with timeout
                var processTask = process.WaitForExitAsync(cancellationToken);
                var timeoutTask = Task.Delay(TimeSpan.FromMinutes(5), cancellationToken);

                var completedTask = await Task.WhenAny(processTask, timeoutTask);

                if (completedTask == timeoutTask)
                {
                    process.Kill();
                    result.ErrorMessage = "Process timed out after 5 minutes";
                    return result;
                }

                stopwatch.Stop();
                result.ExecutionTime = stopwatch.Elapsed;

                if (process.ExitCode == 0)
                {
                    // Try to read the generated dump.json file
                    var dumpFilePath = Path.Combine(Path.GetDirectoryName(_dumperScriptPath)!, "dump.json");
                    
                    if (File.Exists(dumpFilePath))
                    {
                        var jsonContent = await File.ReadAllTextAsync(dumpFilePath, cancellationToken);
                        result.Data = JsonConvert.DeserializeObject<RecoilDump>(jsonContent);
                        result.Success = result.Data != null;

                        // Clean up the dump file for next run
                        File.Delete(dumpFilePath);
                    }
                    else
                    {
                        result.ErrorMessage = "Dump file was not created";
                    }
                }
                else
                {
                    result.ErrorMessage = $"Process exited with code {process.ExitCode}. Error: {errorBuilder}";
                }
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                result.ExecutionTime = stopwatch.Elapsed;
                result.ErrorMessage = $"Exception occurred: {ex.Message}";
            }

            return result;
        }

        public static bool ValidatePythonEnvironment(string pythonExecutable)
        {
            try
            {
                var processInfo = new ProcessStartInfo
                {
                    FileName = pythonExecutable,
                    Arguments = "-c \"import UnityPy; print('UnityPy available')\"",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                using var process = Process.Start(processInfo);
                if (process == null) return false;

                process.WaitForExit();
                return process.ExitCode == 0;
            }
            catch
            {
                return false;
            }
        }

        public static bool ValidateDumperScript(string scriptPath)
        {
            return File.Exists(scriptPath) && Path.GetExtension(scriptPath).Equals(".py", StringComparison.OrdinalIgnoreCase);
        }

        public static bool ValidateRustGamePath(string gamePath)
        {
            if (!Directory.Exists(gamePath))
                return false;

            var bundlePath = Path.Combine(gamePath, "Bundles", "shared", "content.bundle");
            return File.Exists(bundlePath);
        }
    }

    public class DumpProgressEventArgs : EventArgs
    {
        public int CurrentRun { get; set; }
        public int TotalRuns { get; set; }
        public int SuccessfulRuns { get; set; }
        public int FailedRuns { get; set; }
        public double ProgressPercentage => (double)CurrentRun / TotalRuns * 100;
    }
}
