@echo off
REM ESP32 Firmware Build and Deploy Script
REM This script builds the latest firmware and uploads it to the VPS

title ESP32 Firmware Deployment

echo.
echo ========================================
echo    ESP32 Firmware Build & Deploy
echo ========================================
echo.

REM Check if PlatformIO is available
platformio --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: PlatformIO is not installed or not in PATH
    echo Please install PlatformIO and try again
    echo.
    echo Installation options:
    echo 1. Install PlatformIO IDE
    echo 2. Install PlatformIO Core: pip install platformio
    pause
    exit /b 1
)

REM Check if we're in the correct directory
if not exist "platformio.ini" (
    echo ERROR: platformio.ini not found
    echo Please run this script from the ESP32 project directory
    echo Expected location: C:\Users\<USER>\Desktop\recoil\esp32-hid-firmware\Custom farmer for ESP
    pause
    exit /b 1
)

REM Get version from user (optional)
set /p VERSION="Enter firmware version (optional, press Enter to skip): "
if "%VERSION%"=="" (
    set VERSION=latest
)

echo.
echo 🔨 Building ESP32 Firmware v%VERSION%
echo =====================================

REM Clean previous build
echo Cleaning previous build...
platformio run --target clean
if errorlevel 1 (
    echo ❌ Clean failed
    pause
    exit /b 1
)

REM Build firmware
echo.
echo 🏗️  Building firmware...
platformio run --environment lolin_s2_mini
if errorlevel 1 (
    echo ❌ Build failed
    echo.
    echo Common issues:
    echo 1. Check for syntax errors in src/main.cpp
    echo 2. Verify all libraries are installed
    echo 3. Check platformio.ini configuration
    pause
    exit /b 1
)

echo ✅ Build successful!

REM Check if firmware file exists
if not exist ".pio\build\lolin_s2_mini\firmware.bin" (
    echo ❌ Firmware binary not found at .pio\build\lolin_s2_mini\firmware.bin
    pause
    exit /b 1
)

REM Get firmware size
for %%A in (".pio\build\lolin_s2_mini\firmware.bin") do set FIRMWARE_SIZE=%%~zA
echo 📦 Firmware size: %FIRMWARE_SIZE% bytes

echo.
echo 🚀 Deploying to VPS
echo ===================

REM Check if SSH key exists
if not exist "C:\Users\<USER>\Desktop\recoil\auth-backend\deploy\octane_key" (
    echo ❌ SSH key not found at C:\Users\<USER>\Desktop\recoil\auth-backend\deploy\octane_key
    echo Please ensure the SSH key is in the correct location
    pause
    exit /b 1
)

REM Create firmware directory on VPS if it doesn't exist
echo Creating firmware directory on VPS...
ssh -i "C:\Users\<USER>\Desktop\recoil\auth-backend\deploy\octane_key" root@************* "mkdir -p /opt/octane-auth/public/firmware"
if errorlevel 1 (
    echo ❌ Failed to create firmware directory on VPS
    pause
    exit /b 1
)

REM Upload firmware with version-specific name
echo.
echo 📤 Uploading firmware to VPS...
scp -i "C:\Users\<USER>\Desktop\recoil\auth-backend\deploy\octane_key" ".pio\build\lolin_s2_mini\firmware.bin" root@*************:/opt/octane-auth/public/firmware/esp32s2_firmware_%VERSION%.bin
if errorlevel 1 (
    echo ❌ Failed to upload versioned firmware
    pause
    exit /b 1
)

REM Also upload as latest firmware (for flasher to download)
echo 📤 Uploading as latest firmware...
scp -i "C:\Users\<USER>\Desktop\recoil\auth-backend\deploy\octane_key" ".pio\build\lolin_s2_mini\firmware.bin" root@*************:/opt/octane-auth/public/firmware/esp32s2_firmware_latest.bin
if errorlevel 1 (
    echo ❌ Failed to upload latest firmware
    pause
    exit /b 1
)

REM Set proper permissions
echo 🔐 Setting file permissions...
ssh -i "C:\Users\<USER>\Desktop\recoil\auth-backend\deploy\octane_key" root@************* "chown octane:octane /opt/octane-auth/public/firmware/*.bin && chmod 644 /opt/octane-auth/public/firmware/*.bin"
if errorlevel 1 (
    echo ⚠️  Warning: Failed to set file permissions (firmware still uploaded)
)

REM Verify upload
echo.
echo 🔍 Verifying upload...
ssh -i "C:\Users\<USER>\Desktop\recoil\auth-backend\deploy\octane_key" root@************* "ls -la /opt/octane-auth/public/firmware/ | grep esp32s2"
if errorlevel 1 (
    echo ❌ Verification failed
    pause
    exit /b 1
)

REM Test firmware download URL
echo.
echo 🌐 Testing firmware download URL...
ssh -i "C:\Users\<USER>\Desktop\recoil\auth-backend\deploy\octane_key" root@************* "curl -I http://localhost:3000/firmware/esp32s2_firmware_latest.bin"
if errorlevel 1 (
    echo ⚠️  Warning: Could not test download URL (firmware still uploaded)
)

echo.
echo ✅ Deployment Complete!
echo ======================
echo.
echo 📊 Deployment Summary:
echo   Firmware Version: %VERSION%
echo   Local Size: %FIRMWARE_SIZE% bytes
echo   VPS Location: /opt/octane-auth/public/firmware/
echo   Download URL: http://*************:3000/firmware/esp32s2_firmware_latest.bin
echo   Versioned URL: http://*************:3000/firmware/esp32s2_firmware_%VERSION%.bin
echo.
echo 🎯 Next Steps:
echo   1. Test the flasher with the new firmware
echo   2. Verify LED patterns work correctly
echo   3. Test hardware ID generation
echo   4. Update desktop application if needed
echo.
echo 🔧 Flasher Usage:
echo   Standard: OctaneFlasher.exe
echo   Debug: OctaneFlasherDebug.exe --verbose --monitor
echo.

REM Ask if user wants to test with debug flasher
set /p TEST_FLASHER="Would you like to test with the debug flasher? (y/n): "
if /i "%TEST_FLASHER%"=="y" (
    echo.
    echo 🐛 Starting Debug Flasher Test Mode...
    if exist "C:\Users\<USER>\Desktop\recoil\auth-backend\releases\*\DebugFlasher-Windows-x64\OctaneFlasherDebug.exe" (
        for /d %%D in ("C:\Users\<USER>\Desktop\recoil\auth-backend\releases\*") do (
            if exist "%%D\DebugFlasher-Windows-x64\OctaneFlasherDebug.exe" (
                echo Found debug flasher at: %%D\DebugFlasher-Windows-x64\
                cd /d "%%D\DebugFlasher-Windows-x64\"
                OctaneFlasherDebug.exe --monitor --verbose
                goto :END_TEST
            )
        )
    ) else (
        echo ⚠️  Debug flasher not found. Please build it first using build_all_releases.bat
    )
)

:END_TEST
echo.
echo 🎉 Firmware deployment completed successfully!
echo The latest firmware is now available on the VPS for download.
echo.
pause
