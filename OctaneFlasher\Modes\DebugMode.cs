using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using OctaneFlasher.Communication;
using OctaneFlasher.Services;

namespace OctaneFlasher.Modes
{
    /// <summary>
    /// Debug mode for ESP32 communication analysis
    /// </summary>
    public class DebugMode
    {
        private readonly List<PacketData> capturedPackets = new();
        private readonly CancellationTokenSource cancellationTokenSource = new();
        private bool isMonitoring = false;
        private HidDevice? hidDevice;

        /// <summary>
        /// Execute debug mode
        /// </summary>
        public async Task ExecuteAsync(string? licenseKeyArg, string? comPortArg, bool debugMode)
        {
            Console.ForegroundColor = ConsoleColor.Cyan;
            Console.WriteLine("🐛 DEBUG MODE - ESP32 Communication Analysis");
            Console.WriteLine("═══════════════════════════════════════════");
            Console.ResetColor();

            try
            {
                // License validation
                await LicenseService.ValidateLicenseAsync(licenseKeyArg, debugMode);

                // Detect ESP32 device
                var deviceInfo = await DetectESP32DeviceAsync(comPortArg, debugMode);

                if (string.IsNullOrEmpty(deviceInfo))
                {
                    Console.ForegroundColor = ConsoleColor.Red;
                    Console.WriteLine("❌ No ESP32 device found!");
                    Console.WriteLine("   Make sure your ESP32 is connected and drivers are installed.");
                    Console.ResetColor();
                    return;
                }

                Console.WriteLine($"📡 Connecting to ESP32 device: {deviceInfo}...");

                // Connect to HID device
                if (await ConnectToHidDeviceAsync(deviceInfo))
                {
                    Console.WriteLine("✅ Connected! Monitoring ESP32 communication...");
                    Console.WriteLine("📊 Debug Information:");
                    Console.WriteLine("   - Press 'q' to quit");
                    Console.WriteLine("   - Press 's' to send test command");
                    Console.WriteLine("   - Press 'h' to send HID report");
                    Console.WriteLine("   - Press 'c' to clear screen");
                    Console.WriteLine();

                    isMonitoring = true;

                    // Start monitoring in background
                    var monitoringTask = Task.Run(async () => await MonitorDeviceAsync());

                    // Handle user input
                    await HandleUserInputAsync();

                    // Stop monitoring
                    isMonitoring = false;
                    await monitoringTask;
                }
                else
                {
                    Console.ForegroundColor = ConsoleColor.Red;
                    Console.WriteLine("❌ Failed to connect to ESP32 device");
                    Console.ResetColor();
                }
            }
            catch (Exception ex)
            {
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine($"❌ Debug mode error: {ex.Message}");
                Console.ResetColor();
            }
            finally
            {
                await DisconnectDeviceAsync();
            }
        }

        /// <summary>
        /// Detect ESP32 device
        /// </summary>
        private async Task<string?> DetectESP32DeviceAsync(string? comPortArg, bool debugMode)
        {
            Console.WriteLine("🔍 Detecting ESP32 device...");

            // Find HID devices (ESP32 as mouse/keyboard)
            var hidDevices = await HidDevice.FindESP32HidDevicesAsync();

            if (hidDevices.Count > 0)
            {
                Console.WriteLine($"✅ Found {hidDevices.Count} ESP32 HID device(s):");
                for (int i = 0; i < hidDevices.Count; i++)
                {
                    Console.WriteLine($"   {i + 1}. {hidDevices[i]}");
                }

                return hidDevices[0]; // Use first device
            }

            Console.WriteLine("⚠️ No ESP32 HID devices found");
            return null;
        }

        /// <summary>
        /// Connect to HID device
        /// </summary>
        private async Task<bool> ConnectToHidDeviceAsync(string devicePath)
        {
            hidDevice = new HidDevice();
            return await hidDevice.ConnectAsync(devicePath);
        }

        /// <summary>
        /// Monitor device for incoming data
        /// </summary>
        private async Task MonitorDeviceAsync()
        {
            if (hidDevice == null || !hidDevice.IsConnected) return;

            while (isMonitoring && !cancellationTokenSource.Token.IsCancellationRequested)
            {
                try
                {
                    var data = await hidDevice.ReadAsync();
                    if (data != null && data.Length > 0)
                    {
                        var packet = new PacketData(data, "IN");
                        capturedPackets.Add(packet);

                        Console.ForegroundColor = ConsoleColor.Yellow;
                        Console.WriteLine(packet.ToDebugFormat());
                        Console.ResetColor();
                    }

                    await Task.Delay(10); // Small delay to prevent excessive CPU usage
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error reading HID data: {ex.Message}");
                    break;
                }
            }
        }

        /// <summary>
        /// Handle user input during monitoring
        /// </summary>
        private async Task HandleUserInputAsync()
        {
            while (isMonitoring && !cancellationTokenSource.Token.IsCancellationRequested)
            {
                if (Console.KeyAvailable)
                {
                    var key = Console.ReadKey(true);
                    switch (key.KeyChar)
                    {
                        case 'q':
                        case 'Q':
                            isMonitoring = false;
                            break;
                        case 's':
                        case 'S':
                            await SendTestCommandAsync();
                            break;
                        case 'h':
                        case 'H':
                            await SendHidReportAsync();
                            break;
                        case 'c':
                        case 'C':
                            Console.Clear();
                            break;
                    }
                }
                await Task.Delay(100);
            }
        }

        /// <summary>
        /// Send test command to device
        /// </summary>
        private async Task SendTestCommandAsync()
        {
            if (hidDevice == null || !hidDevice.IsConnected)
            {
                Console.WriteLine("No HID device connected");
                return;
            }

            try
            {
                // Send a test HID report
                byte[] report = new byte[64];
                report[0] = 0x01; // Report ID
                report[1] = 0xAA; // Test data
                report[2] = 0xBB;
                report[3] = 0xCC;

                if (await hidDevice.WriteAsync(report))
                {
                    var packet = new PacketData(report, "OUT");
                    capturedPackets.Add(packet);

                    Console.ForegroundColor = ConsoleColor.Green;
                    Console.WriteLine(packet.ToDebugFormat());
                    Console.ResetColor();
                }
                else
                {
                    Console.WriteLine("Failed to send test command");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error sending test command: {ex.Message}");
            }
        }

        /// <summary>
        /// Send HID report to device
        /// </summary>
        private async Task SendHidReportAsync()
        {
            await SendTestCommandAsync(); // For now, same as test command
        }

        /// <summary>
        /// Disconnect from device
        /// </summary>
        private async Task DisconnectDeviceAsync()
        {
            try
            {
                hidDevice?.Disconnect();
                hidDevice?.Dispose();
                hidDevice = null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error disconnecting: {ex.Message}");
            }
        }
    }
}
