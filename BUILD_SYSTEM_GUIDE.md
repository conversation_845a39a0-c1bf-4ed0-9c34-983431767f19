# Octane Recoil - Build System Guide

## Overview

The Octane Recoil desktop application now supports two distinct build configurations:

- **Debug Mode**: For development with all debugging features enabled
- **Production Mode**: For distribution with full security features enabled

## Quick Start

### Building Debug Version
```bash
.\build-debug.bat
```

### Building Production Version
```bash
.\build-production.bat
```

## Build Configurations

### Debug Configuration
**Purpose**: Development and testing
**Output**: `builds\debug\` (multiple files)

**Features Enabled:**
- ✅ F12 Developer Tools
- ✅ Right-click context menu
- ✅ Text selection
- ✅ Console logging
- ✅ Full debugging symbols
- ✅ Detailed error messages

**Security Features:**
- ❌ Hardware ID tracking disabled
- ❌ Discord security alerts disabled
- ❌ Process monitoring disabled
- ❌ Anti-debugging protection disabled

### Production Configuration
**Purpose**: Distribution to end users
**Output**: `builds\production\RecoilController.exe` (single file)

**Security Features Enabled:**
- 🔒 Developer tools BLOCKED (F12, Ctrl+Shift+I, etc.)
- 🔒 Text selection DISABLED
- 🔒 Right-click context menu DISABLED
- 🔒 Console methods OVERRIDDEN
- 🔒 Hardware ID tracking ENABLED
- 🔒 Discord security logging ENABLED
- 🔒 Anti-debugging protection ENABLED
- 🔒 Process monitoring ENABLED
- 🔒 Single-file executable with compression

**Anti-Debugging Features:**
- Blocks common debugging tools (x64dbg, Cheat Engine, etc.)
- Detects developer tools opening
- Monitors for suspicious processes
- Hardware fingerprinting for user tracking
- Automatic security alerts to Discord

## Technical Implementation

### Conditional Compilation
The build system uses C# preprocessor directives to enable/disable features:

```csharp
#if DEBUG && ALLOW_DEV_TOOLS
    // Debug-only code
#elif PRODUCTION_BUILD && BLOCK_DEV_TOOLS
    // Production security code
#endif
```

### Build Symbols
- **Debug**: `DEBUG;TRACE;ALLOW_DEV_TOOLS`
- **Production**: `RELEASE;TRACE;PRODUCTION_BUILD;BLOCK_DEV_TOOLS;ENABLE_SECURITY`

### WebView2 Security
Production builds inject JavaScript and CSS to:
- Block F12 and developer shortcuts
- Disable text selection and right-click
- Override console methods
- Detect developer tools opening

### Hardware ID Generation
Production builds generate unique hardware fingerprints using:
- CPU information
- Machine name
- OS version
- Hardware serials (motherboard, disk, BIOS)
- MAC address

## File Structure

```
builds/
├── debug/                    # Debug build output
│   ├── RecoilController.exe  # Main executable
│   ├── *.dll                 # Dependencies
│   └── *.pdb                 # Debug symbols
└── production/               # Production build output
    └── RecoilController.exe  # Single-file executable
```

## Security Monitoring

### Discord Integration
Production builds automatically send security alerts to Discord when:
- Developer tools are detected
- Suspicious processes are found
- Hardware ID tracking fails
- Anti-debugging triggers activate

### Monitored Processes
- x64dbg, x32dbg, ollydbg, windbg
- Cheat Engine, Process Hacker
- IDA Pro, dnSpy, Reflexil
- Wireshark, Fiddler, Burp Suite

### Blocked Processes
Critical debugging tools are automatically terminated:
- x64dbg, x32dbg, ollydbg
- Cheat Engine
- dnSpy

## Distribution

### Debug Build
- **Use**: Development and testing only
- **Size**: ~200MB (multiple files)
- **Security**: None
- **Distribution**: Never distribute to end users

### Production Build
- **Use**: End user distribution
- **Size**: ~50MB (single file)
- **Security**: Full protection enabled
- **Distribution**: Ready for deployment

## Troubleshooting

### Build Errors
1. Ensure .NET 8.0 SDK is installed
2. Close any running instances of the application
3. Clear build cache: `dotnet clean`

### Security Issues
- Hardware ID tracking requires Windows Management Instrumentation
- Discord webhooks require internet connectivity
- Some antivirus software may flag the production build due to security features

## Development Workflow

1. **Development**: Use debug builds for coding and testing
2. **Testing**: Test both debug and production builds
3. **Distribution**: Only distribute production builds
4. **Updates**: Always rebuild production builds for releases

## Important Notes

⚠️ **Never distribute debug builds** - they contain no security features
🔒 **Production builds are secure** - ready for end user distribution
🛡️ **Security features cannot be disabled** in production builds
📊 **All security events are logged** and sent to Discord in production
