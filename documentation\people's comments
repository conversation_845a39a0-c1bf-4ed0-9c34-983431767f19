Makes sense. Thanks for taking your time to post this
replaced is offline
Reply With Quote
 
Old 9th June 2022, 01:10 AM	  #3
KappaLEL
Posting Well


KappaLEL's Avatar

Join Date: Mar 2018
Posts: 33
Reputation: 137
Rep Power: 179
KappaLEL is in the shadow of all hacking legendsKappaLEL is in the shadow of all hacking legends
Points: 3,590, Level: 5
Points: 3,590, Level: 5	Points: 3,590, Level: 5	Points: 3,590, Level: 5
Level up: 99%, 10 Points needed
Level up: 99%	Level up: 99%	Level up: 99%
Activity: 0%
Activity: 0%	Activity: 0%	Activity: 0%
Last Achievements
Rust's new recoil system, explained.<PERSON>ust's new recoil system, explained.
Great Post!

Is there something I'm doing wrong?

Assuming Sens is 0.5 and FOV 90

Multiplier is = (-0.03 * (0.5 * 3.0) * (90.0 / 100.0)) = -0.0405

When I apply the multiplier, I get those values:

Bullet 1: 49.3827 | -0
Bullet 2: 49.3827 | -8.07768
Bullet 3: 49.3827 | -15.0283
Bullet 4: 49.3827 | -20.9101
etc...

For me, they are far from "pretty good", not sure what I'm missing.
KappaLEL is offline
Reply With Quote
 
Old 9th June 2022, 01:59 AM	  #4
mxslider
n00bie


mxslider's Avatar

Join Date: Jan 2021
Posts: 23
Reputation: 27
Rep Power: 111
mxslider has made posts that are generally average in quality
Points: 3,744, Level: 6
Points: 3,744, Level: 6	Points: 3,744, Level: 6	Points: 3,744, Level: 6
Level up: 16%, 756 Points needed
Level up: 16%	Level up: 16%	Level up: 16%
Activity: 6.1%
Activity: 6.1%	Activity: 6.1%	Activity: 6.1%
Last Achievements
Rust's new recoil system, explained.Rust's new recoil system, explained.
Quote:
Originally Posted by KappaLEL View Post
Great Post!

Is there something I'm doing wrong?

Assuming Sens is 0.5 and FOV 90

Multiplier is = (-0.03 * (0.5 * 3.0) * (90.0 / 100.0)) = -0.0405

When I apply the multiplier, I get those values:

Bullet 1: 49.3827 | -0
Bullet 2: 49.3827 | -8.07768
Bullet 3: 49.3827 | -15.0283
Bullet 4: 49.3827 | -20.9101
etc...

For me, they are far from "pretty good", not sure what I'm missing.

I got something like this
In principle, quite acceptable, given the randomization

Code:
FoV: 90
Sensitivity: 0.5
Weapon [Assualt Rifle]
Code:
 {-0.000000, 55.747951},
 {-7.981284, 56.808840},
 {-16.039333, 56.784173},
 {-20.957679, 55.778617},
 {-26.553284, 57.381407},
 {-31.320765, 54.714963},
 {-32.863284, 55.223605},
 {-33.008222, 54.770444},
 {-37.173235, 52.924790},
 {-37.146247, 55.138049},
 {-35.607802, 56.054173},
....
mxslider is offline
Reply With Quote
 
Old 9th June 2022, 10:48 AM	  #5
kmlozcnn
n00bie


kmlozcnn's Avatar

Join Date: Nov 2015
Posts: 12
Reputation: -181
Rep Power: 0
kmlozcnn is nearly a complete Unknown Asshatkmlozcnn is nearly a complete Unknown Asshat
Points: 6,789, Level: 9
Points: 6,789, Level: 9	Points: 6,789, Level: 9	Points: 6,789, Level: 9
Level up: 27%, 811 Points needed
Level up: 27%	Level up: 27%	Level up: 27%
Activity: 3.1%
Activity: 3.1%	Activity: 3.1%	Activity: 3.1%
Last Achievements
Rust's new recoil system, explained.Rust's new recoil system, explained.
Quote:
Originally Posted by mxslider View Post
I got something like this
In principle, quite acceptable, given the randomization

Code:
FoV: 90
Sensitivity: 0.5
Weapon [Assualt Rifle]
Code:
 {-0.000000, 55.747951},
 {-7.981284, 56.808840},
 {-16.039333, 56.784173},
 {-20.957679, 55.778617},
 {-26.553284, 57.381407},
 {-31.320765, 54.714963},
 {-32.863284, 55.223605},
 {-33.008222, 54.770444},
 {-37.173235, 52.924790},
 {-37.146247, 55.138049},
 {-35.607802, 56.054173},
....
hello, can you share with us the processes you have done to find these values?
kmlozcnn is offline
Reply With Quote
 
Old 9th June 2022, 11:19 AM	  #6
winKaa
n00bie


winKaa's Avatar

Threadstarter
Join Date: Jun 2022
Posts: 23
Reputation: 1809
Rep Power: 78
winKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all die
Points: 939, Level: 2
Points: 939, Level: 2	Points: 939, Level: 2	Points: 939, Level: 2
Level up: 8%, 461 Points needed
Level up: 8%	Level up: 8%	Level up: 8%
Activity: 0%
Activity: 0%	Activity: 0%	Activity: 0%
Quote:
Originally Posted by kmlozcnn View Post
hello, can you share with us the processes you have done to find these values?
Code:
mult = -0.03 * (sensitivity * 3.0) * (fov / 100.0)
for i in range(len(weapon.recoil.pitch_curve)):
    px_x = weapon.recoil.pitch_curve[i] / mult
    px_y = weapon.recoil.yaw_curve[i] / mult
winKaa is offline
Reply With Quote
 
Old 9th June 2022, 11:40 AM	  #7
kmlozcnn
n00bie


kmlozcnn's Avatar

Join Date: Nov 2015
Posts: 12
Reputation: -181
Rep Power: 0
kmlozcnn is nearly a complete Unknown Asshatkmlozcnn is nearly a complete Unknown Asshat
Points: 6,789, Level: 9
Points: 6,789, Level: 9	Points: 6,789, Level: 9	Points: 6,789, Level: 9
Level up: 27%, 811 Points needed
Level up: 27%	Level up: 27%	Level up: 27%
Activity: 3.1%
Activity: 3.1%	Activity: 3.1%	Activity: 3.1%
Last Achievements
Rust's new recoil system, explained.Rust's new recoil system, explained.
Quote:
Originally Posted by winKaa View Post
Code:
mult = -0.03 * (sensitivity * 3.0) * (fov / 100.0)
for i in range(len(weapon.recoil.pitch_curve)):
    px_x = weapon.recoil.pitch_curve[i] / mult
    px_y = weapon.recoil.yaw_curve[i] / mult
can you add me in discord ? Revolex#1405
kmlozcnn is offline
Reply With Quote
 
Old 9th June 2022, 01:07 PM	  #8
winKaa
n00bie


winKaa's Avatar

Threadstarter
Join Date: Jun 2022
Posts: 23
Reputation: 1809
Rep Power: 78
winKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all die
Points: 939, Level: 2
Points: 939, Level: 2	Points: 939, Level: 2	Points: 939, Level: 2
Level up: 8%, 461 Points needed
Level up: 8%	Level up: 8%	Level up: 8%
Activity: 0%
Activity: 0%	Activity: 0%	Activity: 0%
Quote:
Originally Posted by KappaLEL View Post
Great Post!

Is there something I'm doing wrong?

Assuming Sens is 0.5 and FOV 90

Multiplier is = (-0.03 * (0.5 * 3.0) * (90.0 / 100.0)) = -0.0405

When I apply the multiplier, I get those values:

Bullet 1: 49.3827 | -0
Bullet 2: 49.3827 | -8.07768
Bullet 3: 49.3827 | -15.0283
Bullet 4: 49.3827 | -20.9101
etc...

For me, they are far from "pretty good", not sure what I'm missing.
It looks like you have the x, and y values reversed - if they are in that order in the table you posted here.
winKaa is offline
Reply With Quote
 
Old 9th June 2022, 03:01 PM	  #9
replaced
h4x0!2


replaced's Avatar

Join Date: Jan 2013
Posts: 110
Reputation: 231
Rep Power: 306
replaced is becoming A true Rep whorereplaced is becoming A true Rep whorereplaced is becoming A true Rep whore
Recognitions
Members who have contributed financial support towards UnKnoWnCheaTs. Donator (1)
Points: 9,647, Level: 11
Points: 9,647, Level: 11	Points: 9,647, Level: 11	Points: 9,647, Level: 11
Level up: 87%, 153 Points needed
Level up: 87%	Level up: 87%	Level up: 87%
Activity: 6.7%
Activity: 6.7%	Activity: 6.7%	Activity: 6.7%
Last Achievements
Rust's new recoil system, explained.Rust's new recoil system, explained.Rust's new recoil system, explained.
Quote:
Originally Posted by kmlozcnn View Post
can you add me in discord ? Revolex#1405
This guys begging for help to update his P2C script 
replaced is offline
Reply With Quote
 
Old 9th June 2022, 04:04 PM	  #10
kmlozcnn
n00bie


kmlozcnn's Avatar

Join Date: Nov 2015
Posts: 12
Reputation: -181
Rep Power: 0
kmlozcnn is nearly a complete Unknown Asshatkmlozcnn is nearly a complete Unknown Asshat
Points: 6,789, Level: 9
Points: 6,789, Level: 9	Points: 6,789, Level: 9	Points: 6,789, Level: 9
Level up: 27%, 811 Points needed
Level up: 27%	Level up: 27%	Level up: 27%
Activity: 3.1%
Activity: 3.1%	Activity: 3.1%	Activity: 3.1%
Last Achievements
Rust's new recoil system, explained.Rust's new recoil system, explained.
Quote:
Originally Posted by replaced View Post
This guys begging for help to update his P2C script 
I already updated it manually. But I want to understand the logic.
kmlozcnn is offline
Reply With Quote
 
Old 9th June 2022, 04:14 PM	  #11
otiosum
Ban reason: Custom / undisclosed

otiosum's Avatar

Join Date: Aug 2017
Posts: 626
Reputation: 46864
Rep Power: 0
otiosum has a huge epeen!otiosum has a huge epeen!otiosum has a huge epeen!otiosum has a huge epeen!otiosum has a huge epeen!otiosum has a huge epeen!otiosum has a huge epeen!otiosum has a huge epeen!otiosum has a huge epeen!otiosum has a huge epeen!otiosum has a huge epeen!
Recognitions
Award symbolizing a retired staff member who dedicated a notable amount of time and effort to their past staff position. Former Staff
Points: 73,424, Level: 39
Points: 73,424, Level: 39	Points: 73,424, Level: 39	Points: 73,424, Level: 39
Level up: 79%, 876 Points needed
Level up: 79%	Level up: 79%	Level up: 79%
Activity: 1.8%
Activity: 1.8%	Activity: 1.8%	Activity: 1.8%
Last Achievements
Rust's new recoil system, explained.Rust's new recoil system, explained.
File Approved
Rust's new recoil system, explained.
SHA256: d5409467e5d22db4e2f429ad233e84cbe33466628afbafa49adfb3da0c02f90b - BundleDumper.zip
Interested in how we analyze files? Click here to find out.


Download: https://www.unknowncheats.me/forum/d...=file&id=37258

Thanks for sharing!
otiosum is offline
Reply With Quote
 
Old 9th June 2022, 08:03 PM	  #12
mort1fy
n00bie


mort1fy's Avatar

Join Date: Nov 2021
Posts: 15
Reputation: 10
Rep Power: 91
mort1fy has made posts that are generally average in quality
Points: 502, Level: 1
Points: 502, Level: 1	Points: 502, Level: 1	Points: 502, Level: 1
Level up: 21%, 398 Points needed
Level up: 21%	Level up: 21%	Level up: 21%
Activity: 0%
Activity: 0%	Activity: 0%	Activity: 0%
Quote:
Originally Posted by winKaa View Post
Code:
mult = -0.03 * (sensitivity * 3.0) * (fov / 100.0)
for i in range(len(weapon.recoil.pitch_curve)):
    px_x = weapon.recoil.pitch_curve[i] / mult
    px_y = weapon.recoil.yaw_curve[i] / mult
Followed your guide and seemed to got most things working as it moves the mouse in a similar movement to that off what is required. However something seem to be offsetting the values that are being controlled compared to what it should be. And I know your dump is correct and I have checked most things. Any Ideas? It over compensates x values and is good for y values at the start then it changes and is then good for x values but undercompensates for y values. Any help or ideas would be great and I'm standing when testing this as I have accounted for crouching in my script yet want to atleast get standing working well.
mort1fy is offline
Reply With Quote
 
Old 9th June 2022, 08:46 PM	  #13
winKaa
n00bie


winKaa's Avatar

Threadstarter
Join Date: Jun 2022
Posts: 23
Reputation: 1809
Rep Power: 78
winKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all die
Points: 939, Level: 2
Points: 939, Level: 2	Points: 939, Level: 2	Points: 939, Level: 2
Level up: 8%, 461 Points needed
Level up: 8%	Level up: 8%	Level up: 8%
Activity: 0%
Activity: 0%	Activity: 0%	Activity: 0%
Quote:
Originally Posted by mort1fy View Post
Followed your guide and seemed to got most things working as it moves the mouse in a similar movement to that off what is required. However something seem to be offsetting the values that are being controlled compared to what it should be. And I know your dump is correct and I have checked most things. Any Ideas? It over compensates x values and is good for y values at the start then it changes and is then good for x values but undercompensates for y values. Any help or ideas would be great and I'm standing when testing this as I have accounted for crouching in my script yet want to atleast get standing working well.
I'm currently rewriting my script to use this new dump, I'll let you know asap. I feel I've missed something in the dumper in relation to this issue and will fix ASAP. Sorry for the confusion x)
winKaa is offline
Reply With Quote
 
Old 9th June 2022, 09:21 PM	  #14
mort1fy
n00bie


mort1fy's Avatar

Join Date: Nov 2021
Posts: 15
Reputation: 10
Rep Power: 91
mort1fy has made posts that are generally average in quality
Points: 502, Level: 1
Points: 502, Level: 1	Points: 502, Level: 1	Points: 502, Level: 1
Level up: 21%, 398 Points needed
Level up: 21%	Level up: 21%	Level up: 21%
Activity: 0%
Activity: 0%	Activity: 0%	Activity: 0%
Quote:
Originally Posted by winKaa View Post
I'm currently rewriting my script to use this new dump, I'll let you know asap. I feel I've missed something in the dumper in relation to this issue and will fix ASAP. Sorry for the confusion x)
Don't be sorry I appreciate you doing this I probably won't even play rust anymore don't like the way its going anymore that or I will start to learn how to develop cheats for it. But no rush when ever you get round to it and could let me know would be nice to be able to understand it a bit more. Cheers.
mort1fy is offline
Reply With Quote
 
Old 9th June 2022, 09:59 PM	  #15
KappaLEL
Posting Well


KappaLEL's Avatar

Join Date: Mar 2018
Posts: 33
Reputation: 137
Rep Power: 179
KappaLEL is in the shadow of all hacking legendsKappaLEL is in the shadow of all hacking legends
Points: 3,590, Level: 5
Points: 3,590, Level: 5	Points: 3,590, Level: 5	Points: 3,590, Level: 5
Level up: 99%, 10 Points needed
Level up: 99%	Level up: 99%	Level up: 99%
Activity: 0%
Activity: 0%	Activity: 0%	Activity: 0%
Last Achievements
Rust's new recoil system, explained.Rust's new recoil system, explained.
Quote:
Originally Posted by winKaa View Post
It looks like you have the x, and y values reversed - if they are in that order in the table you posted here.
It was only reversed when I printed the values. It's a great write-up btw! Still seems like something is missing, and its not the randomization. If you spray the ak for example, it will always kinda go to the top right a little. For example the ak 2.0 yaw seems to consistently under compensate but if you use the average of what you get when you print the view angles which seems to be something around 2.25 it's near "perfect".
Last edited by KappaLEL; 9th June 2022 at 10:02 PM.
KappaLEL is offline
Reply With Quote
 
Old 9th June 2022, 10:05 PM	  #16
winKaa
n00bie


winKaa's Avatar

Threadstarter
Join Date: Jun 2022
Posts: 23
Reputation: 1809
Rep Power: 78
winKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all die
Points: 939, Level: 2
Points: 939, Level: 2	Points: 939, Level: 2	Points: 939, Level: 2
Level up: 8%, 461 Points needed
Level up: 8%	Level up: 8%	Level up: 8%
Activity: 0%
Activity: 0%	Activity: 0%	Activity: 0%
Quote:
Originally Posted by mort1fy View Post
Don't be sorry I appreciate you doing this I probably won't even play rust anymore don't like the way its going anymore that or I will start to learn how to develop cheats for it. But no rush when ever you get round to it and could let me know would be nice to be able to understand it a bit more. Cheers.
Okay, after playing around with the values a little, the yaw does undercompensate, for yaw I just do (yawMin + yawMax) * 0.5, this produces values that undercompensate, I changed it in my script to do (yawMin + yawMax) * 0.55, and it's perfect. I guess the random function always leans closer to yawMax? idk, I don't like using magic values but this works for me until I can figure it out

Quote:
Originally Posted by KappaLEL View Post
It was only reversed when I printed the values. It's a great write-up btw! Still seems like something is missing, and its not the randomization. If you spray the ak for example, it will always kinda go to the top right a little. For example the ak 2.0 yaw seems to consistently under compensate but if you use the average of what you get when you print the view angles which seems to be something around 2.25 it's near "perfect".
Yeah, you're 100% correct. This is my current script, with the dumper modified to produce yaw values closer to the yawMax, producing 2.25 for the AK.

https://streamable.com/6dvqh8

standing is completely [removed]ed, it still stays on target but it's wobbly as [removed].

Quote:
Originally Posted by winKaa View Post
Okay, after playing around with the values a little, the yaw does undercompensate, for yaw I just do (yawMin + yawMax) * 0.5, this produces values that undercompensate, I changed it in my script to do (yawMin + yawMax) * 0.55, and it's perfect. I guess the random function always leans closer to yawMax? idk, I don't like using magic values but this works for me until I can figure it out



Yeah, you're 100% correct. This is my current script, with the dumper modified to produce yaw values closer to the yawMax, producing 2.25 for the AK.

https://streamable.com/6dvqh8

standing is completely [removed]ed, it still stays on target but it's wobbly as [removed].
Okay I figured this out. They're using the yawMin/yawMax from the original recoil files, and everything else in the override file is used. Which explains some weapons having yawMin/yawMax of -1.0 to 1.0, but producing viewangles beyond that range when you hook AddPunch. Also, make sure you are applying the weapon's ads scale to your values. So, for ak

avg = (ak.yawMin + ak.yawMax) * 0.5 = 3.0

Then, the ADS scale is applied.
AK's ADS scale is 0.75

avg * 0.75 = 2.25 

ads scalar as far as I can see is only applied to yaw, this system is super wonky

I'll update the dumper/dump tomorrow with proper viewangles. There's still some stuff with other weapons that's wonky so I'm missing one or two more things here.
Last edited by winKaa; 10th June 2022 at 12:06 AM.
winKaa is offline
Reply With Quote
 
Old 10th June 2022, 03:38 AM	  #17
littlex88
Member


littlex88's Avatar

Join Date: Mar 2021
Posts: 68
Reputation: -101
Rep Power: 0
littlex88 is an outcastlittlex88 is an outcast
Points: 3,554, Level: 5
Points: 3,554, Level: 5	Points: 3,554, Level: 5	Points: 3,554, Level: 5
Level up: 95%, 46 Points needed
Level up: 95%	Level up: 95%	Level up: 95%
Activity: 1.9%
Activity: 1.9%	Activity: 1.9%	Activity: 1.9%
Last Achievements
Rust's new recoil system, explained.Rust's new recoil system, explained.
Quote:
Originally Posted by winKaa View Post
Okay, after playing around with the values a little, the yaw does undercompensate, for yaw I just do (yawMin + yawMax) * 0.5, this produces values that undercompensate, I changed it in my script to do (yawMin + yawMax) * 0.55, and it's perfect. I guess the random function always leans closer to yawMax? idk, I don't like using magic values but this works for me until I can figure it out



Yeah, you're 100% correct. This is my current script, with the dumper modified to produce yaw values closer to the yawMax, producing 2.25 for the AK.

https://streamable.com/6dvqh8

standing is completely [removed]ed, it still stays on target but it's wobbly as [removed].



Okay I figured this out. They're using the yawMin/yawMax from the original recoil files, and everything else in the override file is used. Which explains some weapons having yawMin/yawMax of -1.0 to 1.0, but producing viewangles beyond that range when you hook AddPunch. Also, make sure you are applying the weapon's ads scale to your values. So, for ak

avg = (ak.yawMin + ak.yawMax) * 0.5 = 3.0

Then, the ADS scale is applied.
AK's ADS scale is 0.75

avg * 0.75 = 2.25 

ads scalar as far as I can see is only applied to yaw, this system is super wonky

I'll update the dumper/dump tomorrow with proper viewangles. There's still some stuff with other weapons that's wonky so I'm missing one or two more things here.
Thanks for sharing, but there is a problem that I don't quite understand. In your dumper, the yaw-max of AK is 2.5, and the yaw-min is 1.5.
avg = (ak.yawMin + ak.yawMax) * 0.5 = (2.5****)*0.5=2
How did you calculate 3,
Looking forward to your reply
littlex88 is offline
Reply With Quote
 
Old 10th June 2022, 05:03 AM	  #18
winKaa
n00bie


winKaa's Avatar

Threadstarter
Join Date: Jun 2022
Posts: 23
Reputation: 1809
Rep Power: 78
winKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all diewinKaa -- If this mans rep is lowered; we will all die
Points: 939, Level: 2
Points: 939, Level: 2	Points: 939, Level: 2	Points: 939, Level: 2
Level up: 8%, 461 Points needed
Level up: 8%	Level up: 8%	Level up: 8%
Activity: 0%
Activity: 0%	Activity: 0%	Activity: 0%
Quote:
Originally Posted by littlex88 View Post
Thanks for sharing, but there is a problem that I don't quite understand. In your dumper, the yaw-max of AK is 2.5, and the yaw-min is 1.5.
avg = (ak.yawMin + ak.yawMax) * 0.5 = (2.5****)*0.5=2
How did you calculate 3,
Looking forward to your reply
They're using a combo of the old recoil files and the new.

For ak that's AR.Recoil and Ak47u.nopattern.recoil
winKaa is offline
Reply With Quote
 
Old 10th June 2022, 05:36 AM	  #19
littlex88
Member


littlex88's Avatar

Join Date: Mar 2021
Posts: 68
Reputation: -101
Rep Power: 0
littlex88 is an outcastlittlex88 is an outcast
Points: 3,554, Level: 5
Points: 3,554, Level: 5	Points: 3,554, Level: 5	Points: 3,554, Level: 5
Level up: 95%, 46 Points needed
Level up: 95%	Level up: 95%	Level up: 95%
Activity: 1.9%
Activity: 1.9%	Activity: 1.9%	Activity: 1.9%
Last Achievements
Rust's new recoil system, explained.Rust's new recoil system, explained.
Thumbs up
Quote:
Originally Posted by winKaa View Post
They're using a combo of the old recoil files and the new.

For ak that's AR.Recoil and Ak47u.nopattern.recoil
Thank you for your reply, I was careless before, but now I understand.
They're using the yawMin/yawMax from the original recoil files whit all weapons?
littlex88 is offline
Reply With Quote
 
Old 10th June 2022, 06:11 AM	  #20
naumenkoff
h4x0!2


naumenkoff's Avatar

Join Date: Aug 2019
Location: Russia
Posts: 97
Reputation: 374
Rep Power: 145
naumenkoff is a cheater commited to the causenaumenkoff is a cheater commited to the causenaumenkoff is a cheater commited to the causenaumenkoff is a cheater commited to the cause
Points: 5,565, Level: 8
Points: 5,565, Level: 8	Points: 5,565, Level: 8	Points: 5,565, Level: 8
Level up: 15%, 935 Points needed
Level up: 15%	Level up: 15%	Level up: 15%
Activity: 7.1%
Activity: 7.1%	Activity: 7.1%	Activity: 7.1%
Last Achievements
Rust's new recoil system, explained.Rust's new recoil system, explained.
What i calculated is wrong
pattern[0] = new Vector2(0.32714619787037374 / -0.0009, -2.0 / -0.0009)

In another method, i calculate the vector using sens/fov
pattern[0] = pattern[0] / sensitivity / fov = Vector2(-363.49577541152638, 2222.2222222222222) / 0.425 / 90 = Vector2(-9.503157527098729, 58.097312999273782)

Then i apply avg to these calculations
avg = (1.5 + 2.5) * 0.55 * 0.75 = 1.6500000000000001
Vector2(-9.503157527098729, 58.097312999273782) / 1.6500000000000001 = Vector2(-5.7594894103628658, 35.210492726832591)

These values already look more attractive, the sight almost never leaves the bot, but... what to do with the animation time?
float anim_time = (weapon.timeToTakeMin + weapon.timeToTakeMax) * 0.5f;
If I substitute the values here, we get the following
float anim_time = (100.00000149011612 + 100.00000149011612) * 0.5 = 200.00000298023224 * 0.5 = 100,00000149011612

For me, if i use recoil compensation with animtime, and not with repeat-delay, everything breaks to hell.
I thought it would be easier, but with the release of this post, what i did in may is undoubtedly worse than what i have done now, 55%/100m vs 75%, but.. i dont understand anything anyway. need to help with the animation time, it's killing me.

how it looks like
Last edited by naumenkoff; 10th June 2022 at 06:20 AM. Reason: added youtube link