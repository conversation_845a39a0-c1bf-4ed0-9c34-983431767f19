@echo off
echo ========================================
echo  OCTANE RECOIL SCRIPTS - PRODUCTION BUILD
echo ========================================
echo.

echo [1/5] Cleaning previous builds...
dotnet clean RecoilController --configuration Release --verbosity quiet
if errorlevel 1 (
    echo ❌ Clean failed!
    pause
    exit /b 1
)

echo [2/5] Restoring packages...
dotnet restore RecoilController --verbosity quiet
if errorlevel 1 (
    echo ❌ Restore failed!
    pause
    exit /b 1
)

echo [3/5] Building PRODUCTION configuration...
dotnet build RecoilController --configuration Release --verbosity minimal
if errorlevel 1 (
    echo ❌ Build failed!
    pause
    exit /b 1
)

echo [4/5] Publishing PRODUCTION build...
dotnet publish RecoilController --configuration Release --output "builds\production" --verbosity minimal
if errorlevel 1 (
    echo ❌ Publish failed!
    pause
    exit /b 1
)

echo [5/5] Applying final security measures...
echo    - Removing debug symbols...
del /q "builds\production\*.pdb" 2>nul
echo    - Removing development files...
del /q "builds\production\*.xml" 2>nul
del /q "builds\production\*.deps.json" 2>nul
echo    - Cleaning up...
rmdir /s /q "builds\production\runtimes" 2>nul

echo.
echo ✅ PRODUCTION BUILD COMPLETED SUCCESSFULLY!
echo.
echo 📁 Output: builds\production\
echo 🔒 Security features enabled:
echo    - Developer tools BLOCKED (F12, Ctrl+Shift+I, etc.)
echo    - Text selection DISABLED
echo    - Right-click context menu DISABLED
echo    - Console methods OVERRIDDEN
echo    - Hardware ID tracking ENABLED
echo    - Discord security logging ENABLED
echo    - Anti-debugging protection ENABLED
echo    - Process monitoring ENABLED
echo    - Single-file executable with compression
echo.
echo 🚀 This build is ready for distribution!
echo.
pause
