using System;
using System.Windows;
using System.Windows.Input;

namespace RecoilController.Services
{
    /// <summary>
    /// Handles window management, panic mode, and UI interactions
    /// </summary>
    public class WindowManagementService
    {
        private readonly Window _window;
        private bool _isPanicMode = false;
        private WindowState _previousWindowState;
        private bool _previousShowInTaskbar;

        public event EventHandler<string>? StatusChanged;
        public event EventHandler<bool>? PanicModeChanged;

        public bool IsPanicMode => _isPanicMode;

        public WindowManagementService(Window window)
        {
            _window = window ?? throw new ArgumentNullException(nameof(window));
            
            // Store initial window state
            _previousWindowState = _window.WindowState;
            _previousShowInTaskbar = _window.ShowInTaskbar;
            
            // Set up keyboard shortcuts
            SetupKeyboardShortcuts();
        }

        /// <summary>
        /// Set up global keyboard shortcuts
        /// </summary>
        private void SetupKeyboardShortcuts()
        {
            _window.KeyDown += (sender, e) =>
            {
                // Ctrl+Shift+P for panic mode
                if (e.Key == Key.P && 
                    Keyboard.Modifiers == (ModifierKeys.Control | ModifierKeys.Shift))
                {
                    TogglePanicMode();
                    e.Handled = true;
                }
                
                // Ctrl+Shift+R to restore from panic mode
                if (e.Key == Key.R && 
                    Keyboard.Modifiers == (ModifierKeys.Control | ModifierKeys.Shift))
                {
                    RestoreFromPanicMode();
                    e.Handled = true;
                }
                
                // F1 for help (could be extended)
                if (e.Key == Key.F1)
                {
                    ShowHelp();
                    e.Handled = true;
                }
            };
        }

        /// <summary>
        /// Toggle panic mode on/off
        /// </summary>
        public void TogglePanicMode()
        {
            _isPanicMode = !_isPanicMode;
            
            if (_isPanicMode)
            {
                ActivatePanicMode();
            }
            else
            {
                DeactivatePanicMode();
            }
            
            PanicModeChanged?.Invoke(this, _isPanicMode);
        }

        /// <summary>
        /// Activate panic mode - hide application completely
        /// </summary>
        private void ActivatePanicMode()
        {
            try
            {
                // Store current state
                _previousWindowState = _window.WindowState;
                _previousShowInTaskbar = _window.ShowInTaskbar;
                
                // Hide window completely
                _window.WindowState = WindowState.Minimized;
                _window.ShowInTaskbar = false;
                _window.Hide();
                
                // Create system tray icon for recovery
                CreateSystemTrayIcon();
                
                StatusChanged?.Invoke(this, "Panic mode activated - Application hidden");
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"Error activating panic mode: {ex.Message}");
            }
        }

        /// <summary>
        /// Deactivate panic mode - restore application
        /// </summary>
        private void DeactivatePanicMode()
        {
            try
            {
                // Restore window
                _window.Show();
                _window.ShowInTaskbar = _previousShowInTaskbar;
                _window.WindowState = _previousWindowState;
                _window.Activate();
                _window.Focus();
                
                StatusChanged?.Invoke(this, "Panic mode deactivated - Application restored");
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"Error deactivating panic mode: {ex.Message}");
            }
        }

        /// <summary>
        /// Restore from panic mode if currently active
        /// </summary>
        public void RestoreFromPanicMode()
        {
            if (_isPanicMode)
            {
                TogglePanicMode();
            }
        }

        /// <summary>
        /// Handle title bar mouse interactions
        /// </summary>
        public void HandleTitleBarMouseDown(MouseButtonEventArgs e)
        {
            if (e.ClickCount == 2)
            {
                // Double-click to maximize/restore
                _window.WindowState = _window.WindowState == WindowState.Maximized 
                    ? WindowState.Normal 
                    : WindowState.Maximized;
            }
            else if (e.LeftButton == MouseButtonState.Pressed)
            {
                // Single click to drag
                try
                {
                    _window.DragMove();
                }
                catch (InvalidOperationException)
                {
                    // Ignore - can happen if window is maximized
                }
            }
        }

        /// <summary>
        /// Minimize window
        /// </summary>
        public void MinimizeWindow()
        {
            _window.WindowState = WindowState.Minimized;
            StatusChanged?.Invoke(this, "Window minimized");
        }

        /// <summary>
        /// Maximize or restore window
        /// </summary>
        public void MaximizeRestoreWindow()
        {
            _window.WindowState = _window.WindowState == WindowState.Maximized 
                ? WindowState.Normal 
                : WindowState.Maximized;
            
            StatusChanged?.Invoke(this, $"Window {(_window.WindowState == WindowState.Maximized ? "maximized" : "restored")}");
        }

        /// <summary>
        /// Close window
        /// </summary>
        public void CloseWindow()
        {
            _window.Close();
        }

        /// <summary>
        /// Hide from task manager (minimize and hide from taskbar)
        /// </summary>
        public void HideFromTaskManager()
        {
            try
            {
                _window.WindowState = WindowState.Minimized;
                _window.ShowInTaskbar = false;
                StatusChanged?.Invoke(this, "Hidden from task manager");
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"Error hiding from task manager: {ex.Message}");
            }
        }

        /// <summary>
        /// Show in task manager
        /// </summary>
        public void ShowInTaskManager()
        {
            try
            {
                _window.ShowInTaskbar = true;
                StatusChanged?.Invoke(this, "Shown in task manager");
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"Error showing in task manager: {ex.Message}");
            }
        }

        /// <summary>
        /// Create system tray icon for panic mode recovery
        /// </summary>
        private void CreateSystemTrayIcon()
        {
            try
            {
                // Note: This would require additional NuGet package like Hardcodet.NotifyIcon.Wpf
                // For now, just log the action
                StatusChanged?.Invoke(this, "System tray icon created (placeholder)");
                
                // In a full implementation, you would:
                // 1. Create NotifyIcon
                // 2. Set icon and tooltip
                // 3. Add context menu with restore option
                // 4. Handle click events to restore from panic mode
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"Failed to create system tray icon: {ex.Message}");
            }
        }

        /// <summary>
        /// Show help information
        /// </summary>
        private void ShowHelp()
        {
            var helpMessage = @"Keyboard Shortcuts:
• Ctrl+Shift+P: Toggle Panic Mode
• Ctrl+Shift+R: Restore from Panic Mode
• F1: Show this help

Panic Mode:
Completely hides the application from view and taskbar.
Use Ctrl+Shift+R to restore.";

            MessageBox.Show(helpMessage, "Help - Keyboard Shortcuts", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// Set window always on top
        /// </summary>
        public void SetAlwaysOnTop(bool alwaysOnTop)
        {
            _window.Topmost = alwaysOnTop;
            StatusChanged?.Invoke(this, $"Always on top: {alwaysOnTop}");
        }

        /// <summary>
        /// Set window opacity
        /// </summary>
        public void SetWindowOpacity(double opacity)
        {
            opacity = Math.Max(0.1, Math.Min(1.0, opacity));
            _window.Opacity = opacity;
            StatusChanged?.Invoke(this, $"Window opacity: {opacity:P0}");
        }

        /// <summary>
        /// Center window on screen
        /// </summary>
        public void CenterWindow()
        {
            _window.WindowStartupLocation = WindowStartupLocation.CenterScreen;
            
            // If window is already shown, manually center it
            if (_window.IsLoaded)
            {
                var screenWidth = SystemParameters.PrimaryScreenWidth;
                var screenHeight = SystemParameters.PrimaryScreenHeight;
                
                _window.Left = (screenWidth - _window.Width) / 2;
                _window.Top = (screenHeight - _window.Height) / 2;
            }
            
            StatusChanged?.Invoke(this, "Window centered");
        }

        /// <summary>
        /// Get current window state information
        /// </summary>
        public WindowStateInfo GetWindowState()
        {
            return new WindowStateInfo
            {
                WindowState = _window.WindowState,
                IsVisible = _window.IsVisible,
                ShowInTaskbar = _window.ShowInTaskbar,
                IsPanicMode = _isPanicMode,
                IsTopmost = _window.Topmost,
                Opacity = _window.Opacity,
                Left = _window.Left,
                Top = _window.Top,
                Width = _window.Width,
                Height = _window.Height
            };
        }
    }

    /// <summary>
    /// Window state information
    /// </summary>
    public class WindowStateInfo
    {
        public WindowState WindowState { get; set; }
        public bool IsVisible { get; set; }
        public bool ShowInTaskbar { get; set; }
        public bool IsPanicMode { get; set; }
        public bool IsTopmost { get; set; }
        public double Opacity { get; set; }
        public double Left { get; set; }
        public double Top { get; set; }
        public double Width { get; set; }
        public double Height { get; set; }
    }
}
