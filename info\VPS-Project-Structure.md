# VPS Project Structure Documentation

## Server Information
- **IP Address**: *************
- **OS**: Ubuntu 6.8.0-64-generic (Linux)
- **Hostname**: optimistic-satoshi.217-154-58-14.plesk.page
- **Architecture**: x86_64

## System Resources
- **Memory**: 848MB total, ~500MB used
- **Disk**: 8.7GB total, 8.2GB used (95% full - **CRITICAL**)
- **Swap**: 1.0GB

## Main Application Directory: `/opt/octane-auth/`

### Core Application Files
```
/opt/octane-auth/
├── server.js                 # Main application server
├── package.json              # Node.js dependencies
├── package-lock.json         # Dependency lock file
├── .env                      # Environment variables (sensitive)
├── octane.db                 # SQLite database file
└── ecosystem.config.js       # PM2 configuration
```

### Directory Structure
```
├── middleware/               # Express middleware
├── models/                   # Database models
├── routes/                   # API route handlers
├── services/                 # Business logic services
├── public/                   # Static web files
│   ├── css/                 # Stylesheets
│   ├── js/                  # Client-side JavaScript
│   ├── firmware/            # ESP32 firmware files
│   ├── index.html           # Main dashboard
│   ├── search.html          # Search page
│   ├── discord.html         # Discord integration
│   ├── key-maintenance.html # License key management
│   ├── reminders.html       # Reminder system
│   ├── security-alerts.html # Security monitoring
│   ├── settings.html        # System settings
│   ├── system-status.html   # System status page
│   └── user-management.html # User management
├── database/                # Database files and backups
├── backups/                 # System backups
├── scripts/                 # Utility scripts
├── firmware/                # ESP32 firmware storage
│   └── esp32s2_enhanced/    # Enhanced ESP32-S2 firmware
├── esp32-firmware/          # Additional firmware files
└── node_modules/            # Node.js dependencies
```

### Configuration Files
- **`.env`**: Environment variables including Discord tokens, Firebase config, admin credentials
- **`ecosystem.config.js`**: PM2 process manager configuration
- **`package.json`**: Node.js project configuration and dependencies

### Key Dependencies
- **express**: Web framework
- **sqlite3**: Database driver
- **discord.js**: Discord bot integration
- **bcrypt**: Password hashing
- **jsonwebtoken**: JWT authentication
- **helmet**: Security middleware
- **cors**: Cross-origin resource sharing
- **express-rate-limit**: Rate limiting

## Services Running

### PM2 Process Manager
- **Service**: `pm2-octane.service`
- **Status**: Active
- **User**: octane
- **Process**: octane-auth

### Nginx Web Server
- **Service**: `nginx.service`
- **Status**: Active (running)
- **Configuration**: `/etc/nginx/sites-available/octane-auth`
- **Proxy**: Forwards port 80 to localhost:3000

## Database
- **Type**: SQLite
- **File**: `/opt/octane-auth/octane.db`
- **Backup Location**: `/opt/octane-auth/backups/`

## Security Configuration
- **Admin IP Whitelist**: **************
- **Rate Limiting**: 100 requests per 15 minutes
- **JWT Authentication**: Enabled
- **CORS**: Configured for specific origins

## Discord Integration
- **Bot Token**: Configured in .env
- **Server ID**: 1350622776943444040
- **Channel ID**: 1350622776943444043
- **Admin User ID**: 1156721390720925828
- **Webhooks**: 
  - Security Alerts
  - ESP32 Errors
  - Backend Errors

## Firebase Configuration
- **Project ID**: authenticator-678a2
- **Auth Domain**: authenticator-678a2.firebaseapp.com
- **Storage Bucket**: authenticator-678a2.firebasestorage.app

## Backup System
- **Location**: `/opt/octane-auth/backups/`
- **Automated**: Database backups with date stamps
- **Retention**: 7 days (older backups auto-deleted)

## ESP32 Firmware Management
- **Storage**: `/opt/octane-auth/firmware/esp32s2_enhanced/`
- **Public Access**: `/opt/octane-auth/public/firmware/`
- **Auto-build**: Firmware uploaded after successful compilation

## Log Files
- **PM2 Logs**: `/opt/octane-auth/.pm2/logs/`
- **Nginx Logs**: `/var/log/nginx/`
- **Application Logs**: Managed by PM2

## User Accounts
- **octane**: System user for running the application
- **root**: Administrative access

## Network Configuration
- **Port 80**: HTTP (Nginx)
- **Port 3000**: Node.js application (internal)
- **Port 22**: SSH access
- **Firewall**: UFW enabled with SSH, HTTP, HTTPS allowed

## Critical Notes
- **Disk Space**: 95% full - immediate cleanup required
- **Memory**: High usage (~60%) - monitoring recommended
- **Security**: Admin IP restricted, rate limiting active
- **Backups**: Automated but limited by disk space
