@echo off
echo.
echo OCTANE FIRMWARE UPLOAD TO VPS
echo =============================
echo.

set VPS_IP=*************
set VPS_USER=root
set SSH_KEY=%~dp0octane_key
set FIRMWARE_SOURCE=%~dp0..\..\esp32-hid-firmware\Custom farmer for ESP\.pio\build\lolin_s2_mini
set REMOTE_FIRMWARE_PATH=/opt/octane-auth/firmware

echo VPS Details:
echo    IP: %VPS_IP%
echo    User: %VPS_USER%
echo    Remote Path: %REMOTE_FIRMWARE_PATH%
echo    Source: %FIRMWARE_SOURCE%
echo.

echo Checking firmware files...
if not exist "%FIRMWARE_SOURCE%\firmware.bin" (
    echo ERROR: firmware.bin not found!
    echo Please build the firmware first using PlatformIO.
    pause
    exit /b 1
)

if not exist "%FIRMWARE_SOURCE%\bootloader.bin" (
    echo ERROR: bootloader.bin not found!
    pause
    exit /b 1
)

if not exist "%FIRMWARE_SOURCE%\partitions.bin" (
    echo ERROR: partitions.bin not found!
    pause
    exit /b 1
)

echo ✅ All firmware files found!
echo.

set /p CONTINUE="Ready to upload firmware to VPS? (y/N): "
if /i not "%CONTINUE%"=="y" (
    echo Upload cancelled.
    pause
    exit /b
)

echo.
echo STEP 1: Creating firmware directory on VPS...
echo ============================================
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "mkdir -p %REMOTE_FIRMWARE_PATH%/esp32s2_enhanced"

echo.
echo STEP 2: Uploading firmware files...
echo ==================================

echo Uploading main firmware...
scp -i "%SSH_KEY%" "%FIRMWARE_SOURCE%\firmware.bin" %VPS_USER%@%VPS_IP%:%REMOTE_FIRMWARE_PATH%/esp32s2_enhanced/firmware.bin

echo Uploading bootloader...
scp -i "%SSH_KEY%" "%FIRMWARE_SOURCE%\bootloader.bin" %VPS_USER%@%VPS_IP%:%REMOTE_FIRMWARE_PATH%/esp32s2_enhanced/bootloader.bin

echo Uploading partitions...
scp -i "%SSH_KEY%" "%FIRMWARE_SOURCE%\partitions.bin" %VPS_USER%@%VPS_IP%:%REMOTE_FIRMWARE_PATH%/esp32s2_enhanced/partitions.bin

if %ERRORLEVEL% NEQ 0 (
    echo Upload failed!
    pause
    exit /b 1
)

echo.
echo STEP 3: Setting permissions...
echo =============================
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "chown -R octane:octane %REMOTE_FIRMWARE_PATH%"
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "chmod -R 644 %REMOTE_FIRMWARE_PATH%/*.bin"

echo.
echo STEP 4: Verifying upload...
echo ==========================
echo Checking file sizes on VPS...
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "ls -la %REMOTE_FIRMWARE_PATH%/esp32s2_enhanced/"

echo.
echo STEP 5: Restarting backend service...
echo ====================================
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "sudo -u octane pm2 restart octane-auth"

echo.
echo FIRMWARE UPLOAD COMPLETE!
echo ========================
echo.
echo Firmware files uploaded to VPS:
echo    • firmware.bin (main ESP32-S2 firmware)
echo    • bootloader.bin (ESP32-S2 bootloader)
echo    • partitions.bin (partition table)
echo.
echo The VPS flasher system is now ready to distribute the latest firmware!
echo.

pause
