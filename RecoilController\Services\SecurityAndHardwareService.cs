using System;
using System.Management;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Net.Http;
using Newtonsoft.Json;
using RecoilController.Security;

namespace RecoilController.Services
{
    /// <summary>
    /// Handles security, hardware identification, and backend communication
    /// </summary>
    public class SecurityAndHardwareService
    {
        private EnhancedSecurityManager? _securityManager;
        private string _hardwareId = string.Empty;
        private readonly HttpClient _httpClient;

        public event EventHandler<string>? StatusChanged;
        public event EventHandler<object>? SecurityAlertTriggered;

        public SecurityAndHardwareService()
        {
            _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpan.FromSeconds(10);
            
            InitializeSecurity();
            _hardwareId = GenerateHardwareId();
        }

        /// <summary>
        /// Initialize security manager based on build configuration
        /// </summary>
        private void InitializeSecurity()
        {
#if PRODUCTION_BUILD && ENABLE_SECURITY
            try
            {
                _securityManager = new EnhancedSecurityManager();
                _securityManager.SecurityAlertTriggered += OnSecurityAlertTriggered;
                // Note: EnhancedSecurityManager doesn't have StartMonitoring method
                StatusChanged?.Invoke(this, "Enhanced security monitoring activated");
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"Security initialization failed: {ex.Message}");
            }
#else
            StatusChanged?.Invoke(this, "Security monitoring disabled (Debug build)");
#endif
        }

        /// <summary>
        /// Generate unique hardware identifier
        /// </summary>
        public string GenerateHardwareId()
        {
            if (!string.IsNullOrEmpty(_hardwareId))
                return _hardwareId;

            try
            {
                var hardwareInfo = new StringBuilder();

                // Get CPU information
                using (var searcher = new ManagementObjectSearcher("SELECT ProcessorId FROM Win32_Processor"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        hardwareInfo.Append(obj["ProcessorId"]?.ToString() ?? "");
                        break;
                    }
                }

                // Get motherboard information
                using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BaseBoard"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        hardwareInfo.Append(obj["SerialNumber"]?.ToString() ?? "");
                        break;
                    }
                }

                // Get BIOS information
                using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BIOS"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        hardwareInfo.Append(obj["SerialNumber"]?.ToString() ?? "");
                        break;
                    }
                }

                // Create hash of hardware information
                using (var sha256 = SHA256.Create())
                {
                    var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(hardwareInfo.ToString()));
                    _hardwareId = Convert.ToBase64String(hashBytes)[..16]; // Take first 16 characters
                }

                StatusChanged?.Invoke(this, $"Hardware ID generated: {_hardwareId}");
                return _hardwareId;
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"Hardware ID generation failed: {ex.Message}");
                _hardwareId = "FALLBACK_" + Environment.MachineName;
                return _hardwareId;
            }
        }

        /// <summary>
        /// Send hardware ID to backend for tracking
        /// </summary>
        public async Task<bool> SendHardwareIdToBackend()
        {
            try
            {
                var data = new
                {
                    hardware_id = _hardwareId,
                    machine_name = Environment.MachineName,
                    os_version = Environment.OSVersion.ToString(),
                    timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ"),
                    app_version = "1.0.0"
                };

                var json = JsonConvert.SerializeObject(data);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync("https://your-backend-url.com/api/hardware-tracking", content);
                
                if (response.IsSuccessStatusCode)
                {
                    StatusChanged?.Invoke(this, "Hardware ID sent to backend successfully");
                    return true;
                }
                else
                {
                    StatusChanged?.Invoke(this, $"Backend communication failed: {response.StatusCode}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"Failed to send hardware ID: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Send error report to Discord webhook
        /// </summary>
        public async Task<bool> SendErrorToDiscord(string errorType, string errorMessage, string stackTrace = "")
        {
            try
            {
                var errorData = new
                {
                    content = $"🚨 **{errorType}**",
                    embeds = new[]
                    {
                        new
                        {
                            title = errorType,
                            description = errorMessage,
                            color = 15158332, // Red color
                            fields = new[]
                            {
                                new { name = "Hardware ID", value = _hardwareId, inline = true },
                                new { name = "Machine", value = Environment.MachineName, inline = true },
                                new { name = "Timestamp", value = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss UTC"), inline = true },
                                new { name = "Stack Trace", value = string.IsNullOrEmpty(stackTrace) ? "N/A" : stackTrace.Length > 1000 ? stackTrace[..1000] + "..." : stackTrace, inline = false }
                            }
                        }
                    }
                };

                var json = JsonConvert.SerializeObject(errorData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                // Replace with actual Discord webhook URL
                var webhookUrl = "YOUR_DISCORD_WEBHOOK_URL";
                var response = await _httpClient.PostAsync(webhookUrl, content);

                if (response.IsSuccessStatusCode)
                {
                    StatusChanged?.Invoke(this, "Error report sent to Discord");
                    return true;
                }
                else
                {
                    StatusChanged?.Invoke(this, $"Discord webhook failed: {response.StatusCode}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"Failed to send Discord alert: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Send ESP32 error to Discord webhook
        /// </summary>
        public async Task<bool> SendESP32ErrorToDiscord(string errorType, string errorMessage)
        {
            try
            {
                var errorData = new
                {
                    content = $"🔌 **ESP32 {errorType}**",
                    embeds = new[]
                    {
                        new
                        {
                            title = $"ESP32 {errorType}",
                            description = errorMessage,
                            color = 16776960, // Yellow color
                            fields = new[]
                            {
                                new { name = "Hardware ID", value = _hardwareId, inline = true },
                                new { name = "Machine", value = Environment.MachineName, inline = true },
                                new { name = "Timestamp", value = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss UTC"), inline = true }
                            }
                        }
                    }
                };

                var json = JsonConvert.SerializeObject(errorData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                // Replace with actual Discord webhook URL
                var webhookUrl = "YOUR_DISCORD_WEBHOOK_URL";
                var response = await _httpClient.PostAsync(webhookUrl, content);

                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"Failed to send ESP32 error to Discord: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Verify backend connectivity
        /// </summary>
        public async Task<bool> VerifyBackendConnectivity()
        {
            try
            {
                var response = await _httpClient.GetAsync("https://your-backend-url.com/api/health");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Handle security alerts from the security manager
        /// </summary>
        private void OnSecurityAlertTriggered(object? sender, object e)
        {
#if PRODUCTION_BUILD && ENABLE_SECURITY && !DEBUG && !DISABLE_SECURITY
            // Take immediate action on security violations
            SecurityAlertTriggered?.Invoke(this, e);
            
            // Send alert to Discord
            _ = Task.Run(async () =>
            {
                await SendErrorToDiscord("Security Violation", "Potential tampering detected", "Security manager alert");
            });
#endif
        }

        /// <summary>
        /// Start backend monitoring
        /// </summary>
        public void StartBackendMonitoring()
        {
            var timer = new System.Windows.Threading.DispatcherTimer();
            timer.Interval = TimeSpan.FromSeconds(30);
            timer.Tick += async (s, e) =>
            {
                var isConnected = await VerifyBackendConnectivity();
                StatusChanged?.Invoke(this, $"Backend status: {(isConnected ? "Connected" : "Disconnected")}");
            };
            timer.Start();
        }

        /// <summary>
        /// Get current hardware ID
        /// </summary>
        public string GetHardwareId()
        {
            return _hardwareId;
        }

        /// <summary>
        /// Check if security is enabled
        /// </summary>
        public bool IsSecurityEnabled()
        {
#if PRODUCTION_BUILD && ENABLE_SECURITY
            return _securityManager != null;
#else
            return false;
#endif
        }

        /// <summary>
        /// Configure WebView2 security
        /// </summary>
        public async Task ConfigureWebViewSecurityAsync(Microsoft.Web.WebView2.Wpf.WebView2 webView)
        {
#if PRODUCTION_BUILD && ENABLE_SECURITY
            // Configure security settings for production
            webView.CoreWebView2.Settings.AreDevToolsEnabled = false;
            webView.CoreWebView2.Settings.AreDefaultContextMenusEnabled = false;
            webView.CoreWebView2.Settings.IsGeneralAutofillEnabled = false;
            webView.CoreWebView2.Settings.IsPasswordAutosaveEnabled = false;
            webView.CoreWebView2.Settings.AreBrowserAcceleratorKeysEnabled = false;
#endif
            await Task.CompletedTask;
            StatusChanged?.Invoke(this, "WebView security configured");
        }

        /// <summary>
        /// Handle security alert
        /// </summary>
        public async Task HandleSecurityAlert(string alertType, string description)
        {
#if PRODUCTION_BUILD && ENABLE_SECURITY
            if (_securityManager != null)
            {
                await _securityManager.TriggerSecurityAlert(alertType, description, RecoilController.Models.ThreatLevel.Medium);
            }
#endif
            StatusChanged?.Invoke(this, $"Security Alert: {alertType} - {description}");
        }

        /// <summary>
        /// Save settings (placeholder for future implementation)
        /// </summary>
        public async Task SaveSettings(object settings)
        {
            // Placeholder for settings saving functionality
            await Task.CompletedTask;
            StatusChanged?.Invoke(this, "Settings saved");
        }

        /// <summary>
        /// Dispose resources
        /// </summary>
        public void Dispose()
        {
            _httpClient?.Dispose();

#if PRODUCTION_BUILD && ENABLE_SECURITY
            _securityManager?.Dispose();
#endif
        }
    }
}
