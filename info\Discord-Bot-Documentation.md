# Discord Bot Documentation

## Current Status: ❌ NOT WORKING
**Issue**: Invalid Discord bot token - needs to be regenerated from Discord Developer Portal

## Bot Information
- **Name**: Octane Discord Bot
- **File**: `discord-bot.js`
- **Process**: Managed by PM2 as `discord-bot`
- **Database**: SQLite (`octane.db`)
- **Framework**: discord.js v14

## Environment Variables Required
```env
# Primary token variable (used by bot)
DISCORD_TOKEN=YOUR_BOT_TOKEN_HERE

# Backup token variable (for compatibility)
DISCORD_BOT_TOKEN=YOUR_BOT_TOKEN_HERE

# Bot configuration
DISCORD_APPLICATION_ID=1398460876402593924
DISCORD_PUBLIC_KEY=38424df4880709b105e0da73ed5ffa8c529f67de6f691c2618e1bb3f055a8919
DISCORD_CLIENT_ID=1156721390720925828
DISCORD_GUILD_ID=1350622776943444040
DISCORD_CHANNEL_ID=1350622776943444043
DISCORD_ADMIN_USER_ID=1156721390720925828

# Webhooks for notifications
WEBHOOK_SECURITY_ALERTS=https://discord.com/api/webhooks/1398506465882411079/...
WEBHOOK_ESP32_ERRORS=https://discord.com/api/webhooks/1398551599818866719/...
WEBHOOK_BACKEND_ERRORS=https://discord.com/api/webhooks/1398551716869443654/...
```

## Bot Features & Commands

### Slash Commands
1. **`/status`** - Check system status
   - Shows backend, database, and bot status
   - Displays system uptime
   - Color: Green (0x00ff88)

2. **`/create-license`** - Create new license keys
   - Duration options: 1 day, 1 week, 1 month, 3 months
   - Optional username parameter
   - Logs creation to security webhook
   - Returns ephemeral response with license key

3. **`/stats`** - Show license statistics
   - Total licenses count
   - Active licenses count
   - Expired licenses count

4. **`/health`** - System health check
   - Checks API endpoint (http://localhost:3000/health)
   - Shows memory usage and uptime
   - Formatted health metrics

5. **`/emergency-disable`** - Emergency authentication disable
   - Admin permissions required
   - Sends alert to security webhook
   - Critical system function

## Bot Architecture

### Class Structure
```javascript
class OctaneDiscordBot {
    constructor()     // Initialize client, commands, database
    setupCommands()   // Define all slash commands
    setupEventHandlers() // Handle Discord events
    registerCommands() // Register commands with Discord API
    start()          // Login and start bot
    stop()           // Graceful shutdown
}
```

### Key Methods
- `createLicense(duration, username, createdBy)` - Database license creation
- `getLicenseStats()` - Query license statistics
- `generateLicenseKey()` - Generate XXX-XXX-XXX format keys
- `calculateExpiration(duration)` - Calculate expiry dates
- `sendWebhook(type, data)` - Send notifications to Discord webhooks

## Database Integration
- **Connection**: SQLite database (`octane.db`)
- **Tables Used**: `licenses`
- **Operations**: INSERT (create), SELECT (stats)
- **Error Handling**: Promise-based with try/catch

## Webhook System
Three webhook types:
- **SECURITY**: License creation, emergency actions
- **ESP32**: ESP32 device errors
- **BACKEND**: Backend system errors

## Current Issues & Fixes Needed

### 1. Invalid Token (CRITICAL)
**Problem**: Current token `MTM5ODQ2MDg3NjQwMjU5...` is invalid
**Solution**: 
1. Go to Discord Developer Portal
2. Navigate to your application
3. Go to Bot section
4. Reset token and copy new one
5. Update `.env` file with new token
6. Restart bot: `sudo -u octane pm2 restart discord-bot`

### 2. Token Reading Logic
**Fixed**: Bot now checks both `DISCORD_TOKEN` and `DISCORD_BOT_TOKEN`
**Code**: 
```javascript
this.token = process.env.DISCORD_TOKEN || process.env.DISCORD_BOT_TOKEN;
```

### 3. Error Handling
**Enhanced**: Added detailed error logging with token preview
**Features**:
- Token validation on startup
- Connection error details
- Command execution error handling

## Management Commands

### Start/Stop Bot
```bash
# Start bot
sudo -u octane pm2 start discord-bot.js --name discord-bot

# Restart bot
sudo -u octane pm2 restart discord-bot

# Stop bot
sudo -u octane pm2 stop discord-bot

# Delete bot process
sudo -u octane pm2 delete discord-bot
```

### View Logs
```bash
# Real-time logs
sudo -u octane pm2 logs discord-bot

# Last 20 lines
sudo -u octane pm2 logs discord-bot --lines 20

# Error logs only
sudo -u octane pm2 logs discord-bot --err
```

### Check Status
```bash
# PM2 status
sudo -u octane pm2 status

# Detailed info
sudo -u octane pm2 show discord-bot

# Monitor resources
sudo -u octane pm2 monit
```

## Troubleshooting Guide

### Bot Won't Start
1. **Check token**: Verify `DISCORD_TOKEN` in `.env`
2. **Check permissions**: Ensure file ownership `chown octane:octane`
3. **Check network**: Test `ping discord.com`
4. **Check DNS**: Restart `systemctl restart systemd-resolved`
5. **Check logs**: `sudo -u octane pm2 logs discord-bot`

### Commands Not Working
1. **Re-register commands**: Bot auto-registers on startup
2. **Check permissions**: Bot needs application commands permission
3. **Check guild**: Ensure bot is in correct Discord server
4. **Check logs**: Look for command registration errors

### Database Errors
1. **Check file**: Ensure `octane.db` exists and is readable
2. **Check permissions**: `chown octane:octane octane.db`
3. **Check schema**: Verify `licenses` table exists
4. **Check disk space**: Ensure sufficient space for database operations

### Network Issues
1. **DNS resolution**: `nslookup discord.com`
2. **Firewall**: Check if Discord IPs are blocked
3. **Proxy**: Ensure no proxy blocking Discord API
4. **Rate limiting**: Check for API rate limit errors

## Security Considerations

### Token Security
- Store token in `.env` file only
- Never commit token to version control
- Use file permissions 600 for `.env`
- Rotate token regularly

### Permissions
- Bot runs as `octane` user (non-root)
- Minimal Discord permissions required
- Admin commands check user permissions
- Database access restricted to bot user

### Logging
- All command usage logged
- Security actions sent to webhooks
- Error logs monitored
- No sensitive data in logs

## Performance Optimization

### Memory Usage
- Current: ~60MB per bot instance
- Optimized with VM settings
- Automatic restart on high memory usage
- Connection pooling for database

### CPU Usage
- Minimal CPU usage when idle
- Spike during command processing
- Efficient event handling
- Optimized database queries

## Integration Points

### Main Application
- Shares same database (`octane.db`)
- Uses same environment variables
- Coordinated logging system
- Shared webhook endpoints

### ESP32 System
- Receives error notifications
- License creation for devices
- Status monitoring integration
- Hardware ID tracking

### Admin Panel
- License statistics display
- Bot status monitoring
- Command execution logs
- System health integration

## Future Enhancements

### Planned Features
- License expiry notifications
- Bulk license operations
- Advanced statistics
- User management commands
- Automated backups

### Monitoring
- Health check endpoints
- Performance metrics
- Error rate tracking
- Uptime monitoring

## Recovery Procedures

### Complete Bot Reset
```bash
# Stop bot
sudo -u octane pm2 stop discord-bot

# Clear PM2 logs
sudo -u octane pm2 flush

# Restart with fresh config
sudo -u octane pm2 start discord-bot.js --name discord-bot

# Save PM2 config
sudo -u octane pm2 save
```

### Emergency Disable
If bot is causing issues:
```bash
# Immediate stop
sudo -u octane pm2 stop discord-bot

# Remove from startup
sudo -u octane pm2 delete discord-bot

# Check system impact
sudo -u octane pm2 status
```

## Contact & Support
- Check logs first: `sudo -u octane pm2 logs discord-bot`
- Verify token validity in Discord Developer Portal
- Test network connectivity to Discord
- Review environment variables in `.env`
- Monitor system resources with `htop`
