Note
This is my last post on this topic, hopefully I can fully explain everything here, and link it all in to 'Everything you need to paste a recoil script', so that everything is fully understood and there's no more need for posts about the new system / recoil tables / etc. I'll provide a dumper that auto formats the data from content.bundle for you guys.

Based off of Everything you need to paste a recoil script

The new recoil system
As you guys know, <PERSON><PERSON>'s old recoil system mostly relied on a mix of 'linear' weapons, and weapons using two AnimationCurve objects to produce patterns. These two systems have now been combined into one, now there are weapons using a hybrid of the linear system, and the curve system, and there the linear weapons.

So, with the update we got some new fields added to the BaseProjectile class. These are:

- curvesAsScalar
- shotsUntilMax
- maxRecoilRadius

These three new fields are used for the 'hybrid' recoil system, in which the curves are generated to be scalars for the weapon pitch. With yaw remaining much the same as the linear recoil system.

So, let's get into how the hybrid system generates viewangles.

	This image has been resized. Click this bar to view the full image. The original image is sized 1223x711.


The above code is from my dumper, this will produce the raw viewangles for each weapon in the game, whether it uses curvesAsScalar or not.
Essentially, they generate the yawCurve given the keyframes, and use currentShot/shotsUntilMax to generate 't' for the AnimationCurve.evaluate(). shotsUntilMax is not the magazine capacity, however, it's an arbitrary value used to produce the 'gradient' they speak about in the devblog. For the AK it's 10, so after 10 shots, the pitch values are the same until shot 30, looking something like
Code:
0.0,
                    0.32714619787037374,
                    0.6086477036476136,
                    0.84685909371078,
                    1.0441349444389343,
                    1.2028298322111368,
                    1.3252983334064483,
                    1.41389502440393,
                    1.4709744815826413,
                    1.4988912813216446,
                    1.5,
                    1.5,
                    1.5,
                    1.5,
                    1.5,
                    1.5,
                    1.5,
                    1.5,
                    1.5,
                    1.5,
                    1.5,
                    1.5,
                    1.5,
                    1.5,
                    1.5,
                    1.5,
                    1.5,
                    1.5,
                    1.5,
                    1.5
Anyways, if any of these generated values for pitch go beyond the maxRecoilRadius value, they generate a random value between (-maxRecoilRadius, maxRecoilRadius), and replace the pitch value with that, this makes weapons like the mp5, smg, thompson, etc. even more random than others, when it comes to pitch. The AK is the only weapon you can generate pretty good values for that won't be super random.

FOR PEOPLE THAT SAY SOME VALUES ARE WRONG.
For weapons that do use this random maxRecoilRadius, I just set the pitch value to maxRecoilRadius * 0.25, which is why you will see a bunch of weapons with 0.25/0.75/etc. for every pitch value. I wasn't really sure what to do here, maybe you guys can edit the dumper and come up with a better solution, but just know these values aren't **wrong**, you can't get perfect values for weapons that exceed maxRecoilRadius.


How to script in the new update.
My dumper will produce the deltas already for your viewangle->pixel conversion, the same math applies as in @awhall56's thread, but you don't need to do current - last to get the delta.

the multiplier is the same
Code:
 float mult = -0.03f * (sensitivity * 3.0f) * (fov / 100.0f);
Pretty much everything in the post applies to my new dump, nothing else has changed from what I can tell, except for animation times. We no longer determine those based on the curve, and the formula is the same for every weapon, now.

Code:
float anim_time = (weapon.timeToTakeMin + weapon.timeToTakeMax) * 0.5f;
Moving on from the new system, some info that Everything you need to paste a recoil script left out.

Attachments.
For attachments, people apply wonky multipliers to the recoil values for scopes. For example, if you have the holo on, you multiply by 0.8 or 0.95, but if you look at my dump, or the dump everybody has been using since it was released by @awhall56, each attachment has a zoom factor, this is used to produce the proper scalar. The actual multiplier you can apply to your weapons looks something like this (written in python)



Code:
 recoil_mult = (
            (
                # Base viewangles -> pixels multiplier 
                -0.03 * (sens * 3.0) * (fov / 100.0)
            ) /
            (
                # Factor in the weapon's zoom factor
                (45.0 / (75.0 if hipfire else self.get_fov_offset()))
            )
You can then use the scalars in the recoil objects in the dump to modify your values. Here are two functions I wrote to get the scalars for recoil, and repeat delay.

	This image has been resized. Click this bar to view the full image. The original image is sized 845x339.



And you can use those in your code like so:

Code:
px_x = (viewangle_x / mult) * self.get_recoil_scalar()
px_y = (viewangle_y / mult) * self.get_recoil_scalar()
 
time_to_take = ((time_min + time_max) * 0.5) * self.get_repeat_scalar()
The dumper.
Honestly, I'm not 100% sure this will dump perfect viewangles for every weapon, I've only tested on a few and compared to the values given by hooking AddPunch, mostly testing useCurvesAsScalar weapons, if linear weapons are off, please leave a comment and I will fix the src and re-upload asap. Please excuse the mess, I tried to comment as much as I could  if anything you need in the dump from BaseProjectile/IronSights/RecoilProperties is missing, lemme know or edit the dumper yourself and fix it.

Dumper requires python 3.6+ and for you to pip install UnityPy.

The dumper does not require you to use Asset Studio, it will handle dumping content.bundle automatically, and should take less than 70 seconds to fully dump everything.

Here's the current dump from the game while the dumper gets approved.