using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Management;

namespace OctaneFlasher.Communication
{
    /// <summary>
    /// HID device communication and management
    /// </summary>
    public class HidDevice : IDisposable
    {
        private IntPtr deviceHandle = IntPtr.Zero;
        private string devicePath = "";
        private bool disposed = false;

        public bool IsConnected => deviceHandle != IntPtr.Zero;
        public string DevicePath => devicePath;

        /// <summary>
        /// Find all ESP32 HID devices
        /// </summary>
        public static async Task<List<string>> FindESP32HidDevicesAsync()
        {
            var devices = new List<string>();

            try
            {
                Console.WriteLine("🔍 Starting ESP32 device detection using WMI...");

                // Use WMI to find ESP32 devices - more reliable than direct HID API
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_PnPEntity WHERE DeviceID LIKE '%VID_303A%'"))
                {
                    foreach (ManagementObject device in searcher.Get())
                    {
                        var deviceId = device["DeviceID"]?.ToString();
                        var name = device["Name"]?.ToString();
                        var status = device["Status"]?.ToString();

                        Console.WriteLine($"🔍 Found ESP32 device: {name}");
                        Console.WriteLine($"🔍 Device ID: {deviceId}");
                        Console.WriteLine($"🔍 Status: {status}");

                        if (!string.IsNullOrEmpty(deviceId) && deviceId.Contains("VID_303A"))
                        {
                            Console.WriteLine($"✅ ESP32 device confirmed: {deviceId}");
                            devices.Add(deviceId);
                        }
                    }
                }

                // Fallback to HID API if WMI doesn't find anything
                if (devices.Count == 0)
                {
                    Console.WriteLine("🔍 No ESP32 found via WMI, trying HID API...");
                    return await FindESP32HidDevicesViaHidApiAsync();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error in WMI device detection: {ex.Message}");
                // Fallback to HID API
                return await FindESP32HidDevicesViaHidApiAsync();
            }

            return devices;
        }

        private static async Task<List<string>> FindESP32HidDevicesViaHidApiAsync()
        {
            var devices = new List<string>();

            try
            {
                Console.WriteLine("🔍 Starting HID device enumeration...");

                Guid hidGuid;
                HidApi.HidD_GetHidGuid(out hidGuid);
                Console.WriteLine($"🔍 HID GUID: {hidGuid}");

                IntPtr deviceInfoSet = HidApi.SetupDiGetClassDevs(ref hidGuid, null, IntPtr.Zero,
                    HidApi.DIGCF_PRESENT | HidApi.DIGCF_DEVICEINTERFACE);

                if (deviceInfoSet != IntPtr.Zero)
                {
                    Console.WriteLine("🔍 Device info set created successfully");
                    var deviceInterfaceData = new HidApi.SP_DEVICE_INTERFACE_DATA();
                    deviceInterfaceData.cbSize = (uint)Marshal.SizeOf(deviceInterfaceData);

                    uint memberIndex = 0;
                    Console.WriteLine("🔍 Enumerating HID device interfaces...");

                    while (HidApi.SetupDiEnumDeviceInterfaces(deviceInfoSet, IntPtr.Zero, ref hidGuid, memberIndex, ref deviceInterfaceData))
                    {
                        Console.WriteLine($"🔍 Found HID interface #{memberIndex}");
                        uint requiredSize;
                        HidApi.SetupDiGetDeviceInterfaceDetail(deviceInfoSet, ref deviceInterfaceData, IntPtr.Zero, 0, out requiredSize, IntPtr.Zero);

                        if (requiredSize > 0)
                        {
                            IntPtr detailDataBuffer = Marshal.AllocHGlobal((int)requiredSize);
                            try
                            {
                                var detailData = new HidApi.SP_DEVICE_INTERFACE_DETAIL_DATA();
                                detailData.cbSize = (uint)(IntPtr.Size == 8 ? 8 : 6); // Different sizes for x64/x86

                                Marshal.StructureToPtr(detailData, detailDataBuffer, false);

                                if (HidApi.SetupDiGetDeviceInterfaceDetail(deviceInfoSet, ref deviceInterfaceData, detailDataBuffer, requiredSize, out requiredSize, IntPtr.Zero))
                                {
                                    // Read the structure back from memory
                                    var deviceDetail = Marshal.PtrToStructure<HidApi.SP_DEVICE_INTERFACE_DETAIL_DATA>(detailDataBuffer);
                                    var devicePath = deviceDetail.devicePath;

                                    Console.WriteLine($"🔍 Device path: {devicePath}");

                                    // Check if this might be our ESP32 device
                                    if (!string.IsNullOrEmpty(devicePath) && await IsESP32HidDeviceAsync(devicePath))
                                    {
                                        Console.WriteLine($"✅ ESP32 device found: {devicePath}");
                                        devices.Add(devicePath);
                                    }
                                    else
                                    {
                                        Console.WriteLine($"❌ Not ESP32 device: {devicePath}");
                                    }
                                }
                            }
                            finally
                            {
                                Marshal.FreeHGlobal(detailDataBuffer);
                            }
                        }

                        memberIndex++;
                    }

                    HidApi.SetupDiDestroyDeviceInfoList(deviceInfoSet);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Error detecting HID devices: {ex.Message}");
            }

            return devices;
        }

        /// <summary>
        /// Check if device path is an ESP32 HID device
        /// </summary>
        private static async Task<bool> IsESP32HidDeviceAsync(string devicePath)
        {
            try
            {
                // Try to open the device briefly to check if it's accessible
                IntPtr handle = HidApi.CreateFile(devicePath, 
                    HidApi.GENERIC_READ | HidApi.GENERIC_WRITE,
                    HidApi.FILE_SHARE_READ | HidApi.FILE_SHARE_WRITE,
                    IntPtr.Zero, HidApi.OPEN_EXISTING, 0, IntPtr.Zero);

                if (handle != HidApi.INVALID_HANDLE_VALUE)
                {
                    HidApi.CloseHandle(handle);
                    
                    // Check if this is our ESP32 device by looking for specific identifiers
                    return devicePath.ToLower().Contains("vid_303a") || // ESP32 Vendor ID
                           devicePath.ToLower().Contains("esp32") ||     // ESP32 in device path
                           devicePath.ToLower().Contains("octane");      // Our custom device name
                }
            }
            catch
            {
                // Ignore errors, device might be in use
            }

            return false;
        }

        /// <summary>
        /// Connect to HID device
        /// </summary>
        public async Task<bool> ConnectAsync(string devicePath)
        {
            try
            {
                deviceHandle = HidApi.CreateFile(devicePath,
                    HidApi.GENERIC_READ | HidApi.GENERIC_WRITE,
                    HidApi.FILE_SHARE_READ | HidApi.FILE_SHARE_WRITE,
                    IntPtr.Zero, HidApi.OPEN_EXISTING, 0, IntPtr.Zero);

                if (deviceHandle != HidApi.INVALID_HANDLE_VALUE)
                {
                    this.devicePath = devicePath;
                    Console.WriteLine($"✅ Connected to HID device: {devicePath}");
                    return true;
                }
                else
                {
                    Console.WriteLine($"❌ Failed to open HID device: {devicePath}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error connecting to HID device: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Read data from HID device
        /// </summary>
        public async Task<byte[]?> ReadAsync()
        {
            if (!IsConnected) return null;

            try
            {
                byte[] buffer = new byte[64]; // Standard HID report size
                uint bytesRead;
                
                if (HidApi.ReadFile(deviceHandle, buffer, (uint)buffer.Length, out bytesRead, IntPtr.Zero))
                {
                    if (bytesRead > 0)
                    {
                        var data = new byte[bytesRead];
                        Array.Copy(buffer, data, bytesRead);
                        return data;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error reading HID data: {ex.Message}");
            }

            return null;
        }

        /// <summary>
        /// Write data to HID device
        /// </summary>
        public async Task<bool> WriteAsync(byte[] data)
        {
            if (!IsConnected) return false;

            try
            {
                // Ensure data fits in HID report
                byte[] report = new byte[64];
                Array.Copy(data, report, Math.Min(data.Length, report.Length));
                
                uint bytesWritten;
                if (HidApi.WriteFile(deviceHandle, report, (uint)report.Length, out bytesWritten, IntPtr.Zero))
                {
                    return bytesWritten > 0;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error writing HID data: {ex.Message}");
            }

            return false;
        }

        /// <summary>
        /// Disconnect from device
        /// </summary>
        public void Disconnect()
        {
            if (deviceHandle != IntPtr.Zero)
            {
                HidApi.CloseHandle(deviceHandle);
                deviceHandle = IntPtr.Zero;
                devicePath = "";
                Console.WriteLine("🔌 HID device disconnected");
            }
        }

        public void Dispose()
        {
            if (!disposed)
            {
                Disconnect();
                disposed = true;
            }
        }
    }
}
