@echo off
echo Building Recoil Pattern Dumper...
echo.

REM Check if .NET 8.0 is installed
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: .NET 8.0 is not installed or not in PATH
    echo Please install .NET 8.0 SDK from https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

echo .NET version:
dotnet --version
echo.

REM Restore packages
echo Restoring NuGet packages...
dotnet restore
if errorlevel 1 (
    echo ERROR: Failed to restore packages
    pause
    exit /b 1
)

REM Build the project
echo Building project...
dotnet build --configuration Release
if errorlevel 1 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

echo.
echo Build completed successfully!
echo.
echo Output location: bin\Release\net8.0-windows\
echo.

REM Optional: Create a publish build
set /p publish="Create publish build? (y/n): "
if /i "%publish%"=="y" (
    echo.
    echo Creating publish build...
    dotnet publish --configuration Release --output publish --self-contained false
    if errorlevel 1 (
        echo ERROR: Publish failed
        pause
        exit /b 1
    )
    echo.
    echo Publish completed! Files are in the 'publish' folder.
)

echo.
echo You can now run the application from:
echo - bin\Release\net8.0-windows\RecoilPatternDumper.exe
if exist "publish\RecoilPatternDumper.exe" (
    echo - publish\RecoilPatternDumper.exe
)
echo.
pause
