# PowerShell script to list all HID devices
Write-Host "=== HID DEVICE DETECTION TEST ===" -ForegroundColor Cyan
Write-Host ""

# Get all HID devices from Device Manager
Write-Host "HID Devices from Device Manager:" -ForegroundColor Yellow
Get-WmiObject -Class Win32_PnPEntity | Where-Object { $_.Name -like "*HID*" -or $_.DeviceID -like "*HID*" } | ForEach-Object {
    Write-Host "  Name: $($_.Name)" -ForegroundColor Green
    Write-Host "  DeviceID: $($_.DeviceID)" -ForegroundColor Gray
    Write-Host "  Status: $($_.Status)" -ForegroundColor Gray
    Write-Host ""
}

# Get all mouse devices
Write-Host "Mouse Devices:" -ForegroundColor Yellow
Get-WmiObject -Class Win32_PointingDevice | ForEach-Object {
    Write-Host "  Name: $($_.Name)" -ForegroundColor Green
    Write-Host "  DeviceID: $($_.DeviceID)" -ForegroundColor Gray
    Write-Host "  Status: $($_.Status)" -ForegroundColor Gray
    Write-Host ""
}

# Get all COM ports
Write-Host "COM Ports:" -ForegroundColor Yellow
Get-WmiObject -Class Win32_SerialPort | ForEach-Object {
    Write-Host "  Name: $($_.Name)" -ForegroundColor Green
    Write-Host "  DeviceID: $($_.DeviceID)" -ForegroundColor Gray
    Write-Host "  Status: $($_.Status)" -ForegroundColor Gray
    Write-Host ""
}

Write-Host "=== END OF DEVICE LIST ===" -ForegroundColor Cyan
