/*
 * Firmware Configuration Header
 * Contains all firmware constants, version info, and compile-time settings
 */

#ifndef FIRMWARE_CONFIG_H
#define FIRMWARE_CONFIG_H

#include <Arduino.h>

// ===== FIRMWARE VERSION INFORMATION =====
#define FIRMWARE_VERSION "2.1.0"
#define HARDWARE_VERSION "ESP32-S2-Enhanced"
#define SECURITY_LEVEL "ADVANCED"
#define BUILD_DATE __DATE__
#define BUILD_TIME __TIME__

// ===== HARDWARE CONFIGURATION =====
#define SERIAL_BAUD 115200
#define LED_PIN 15
#define USB_VENDOR_ID 0x303A
#define USB_PRODUCT_ID 0x8001

// ===== COMMAND SYSTEM CONFIGURATION =====
#define QUEUE_SIZE 128
#define COMMAND_BUFFER_SIZE 1024
#define JSON_BUFFER_SIZE 2048
#define MAX_COMMAND_LENGTH 256

// ===== SECURITY CONFIGURATION =====
#define DEVICE_SIGNATURE_LENGTH 32
#define DEVICE_ID_LENGTH 24
#define OBFUSCATION_ROUNDS 7
#define ENTROPY_SOURCES 12
#define SECURITY_CHECK_INTERVAL 5000  // milliseconds
#define ANTI_DETECTION_INTERVAL 1000  // milliseconds

// ===== RECOIL ENGINE CONFIGURATION =====
#define TEMPLATE_DELAY_MS 133          // Template: assault_rifle delay = 133
#define MAX_INTERPOLATION_STEPS 100    // Template interpolation steps
#define RECOIL_PRECISION_MULTIPLIER 1000  // For sub-pixel precision
#define MAX_RECOIL_PATTERN_SIZE 30     // Maximum bullets in pattern

// ===== TIMING CONFIGURATION =====
#define MICROSECOND_PRECISION_THRESHOLD 1000  // 1ms
#define ZERO_DELAY_THRESHOLD 100              // 100 microseconds
#define HEARTBEAT_INTERVAL 10000               // 10 seconds
#define STATUS_UPDATE_INTERVAL 1000            // 1 second

// ===== MEMORY OPTIMIZATION =====
#define STACK_SIZE_COMMAND_TASK 4096
#define STACK_SIZE_SECURITY_TASK 2048
#define STACK_SIZE_RECOIL_TASK 8192
#define HEAP_RESERVE_MINIMUM 8192

// ===== DEBUG CONFIGURATION =====
#ifdef DEBUG_MODE
    #define DEBUG_SERIAL_ENABLED true
    #define DEBUG_LED_ENABLED true
    #define DEBUG_TIMING_ENABLED true
    #define DEBUG_SECURITY_ENABLED false  // Security debug disabled by default
#else
    #define DEBUG_SERIAL_ENABLED false
    #define DEBUG_LED_ENABLED false
    #define DEBUG_TIMING_ENABLED false
    #define DEBUG_SECURITY_ENABLED false
#endif

// ===== FEATURE FLAGS =====
#define FEATURE_ANTI_DETECTION_ENABLED true
#define FEATURE_STEALTH_MODE_ENABLED true
#define FEATURE_TEMPLATE_RECOIL_ENABLED true
#define FEATURE_ZERO_DELAY_ENABLED true
#define FEATURE_SECURITY_AUDIT_ENABLED true

// ===== PERFORMANCE TUNING =====
#define CPU_FREQUENCY_MHZ 240
#define FLASH_FREQUENCY_MHZ 80
#define PSRAM_ENABLED false

// ===== COMMUNICATION TIMEOUTS =====
#define SERIAL_TIMEOUT_MS 100
#define USB_TIMEOUT_MS 50
#define COMMAND_TIMEOUT_MS 1000

// ===== ERROR HANDLING =====
#define MAX_RETRY_ATTEMPTS 3
#define ERROR_RECOVERY_DELAY_MS 100
#define WATCHDOG_TIMEOUT_SECONDS 30

#endif // FIRMWARE_CONFIG_H
