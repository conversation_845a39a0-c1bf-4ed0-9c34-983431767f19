using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using OctaneFlasher.Core;

namespace OctaneFlasher.Services
{
    /// <summary>
    /// Handles license validation with the backend server
    /// </summary>
    public static class LicenseService
    {
        private const string LICENSE_VALIDATION_URL = "http://217.154.58.14/api/validate";
        private static readonly HttpClient httpClient = new HttpClient();
        private static string licenseKey = "";
        private static string downloadToken = "";
        private static string downloadTimestamp = "";

        /// <summary>
        /// Validate license with backend server
        /// </summary>
        public static async Task ValidateLicenseAsync(string? licenseKeyArg = null, bool debugMode = false)
        {
            Console.ForegroundColor = ConsoleColor.Yellow;
            Console.WriteLine("🔐 LICENSE VALIDATION REQUIRED");
            Console.WriteLine("═══════════════════════════════");
            Console.ResetColor();

            var hardwareId = HardwareIdentification.GenerateHardwareId();
            Console.WriteLine($"Hardware ID: {hardwareId}");

            // Get license key
            if (!string.IsNullOrEmpty(licenseKeyArg))
            {
                licenseKey = licenseKeyArg;
                Console.WriteLine($"Using license key from command line: {licenseKey}");
            }
            else
            {
                Console.Write("Enter your license key: ");
                licenseKey = Console.ReadLine() ?? "";
            }

            if (string.IsNullOrEmpty(licenseKey))
            {
                throw new InvalidOperationException("License key is required");
            }

            Console.WriteLine("🔍 Validating license...");

            // Prepare validation request
            var validationData = new
            {
                licenseKey = licenseKey,
                hardwareId = hardwareId
            };

            var jsonPayload = JsonSerializer.Serialize(validationData);
            var content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

            if (debugMode)
            {
                Console.WriteLine($"🔍 Debug: Sending request to {LICENSE_VALIDATION_URL}");
                Console.WriteLine($"🔍 Debug: Request payload: {jsonPayload}");
            }

            // Attempt validation with retries
            const int maxRetries = 3;
            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    var response = await httpClient.PostAsync(LICENSE_VALIDATION_URL, content);
                    var responseBody = await response.Content.ReadAsStringAsync();

                    if (debugMode)
                    {
                        Console.WriteLine($"🔍 Debug: Response status: {response.StatusCode}");
                        Console.WriteLine($"🔍 Debug: Response body: {responseBody}");
                    }

                    if (response.IsSuccessStatusCode)
                    {
                        var validationResponse = JsonSerializer.Deserialize<ValidationResponse>(responseBody);
                        
                        if (validationResponse?.success == true)
                        {
                            Console.ForegroundColor = ConsoleColor.Green;
                            Console.WriteLine("✅ License validation successful!");
                            Console.WriteLine($"   License expires: {validationResponse.expiresAt}");
                            Console.WriteLine($"   Remaining time: {validationResponse.remainingTime} seconds");
                            Console.ResetColor();

                            // Store tokens for firmware download
                            downloadToken = validationResponse.downloadToken ?? "";
                            downloadTimestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ");
                            
                            return;
                        }
                        else
                        {
                            throw new UnauthorizedAccessException(validationResponse?.message ?? "License validation failed");
                        }
                    }
                    else
                    {
                        var errorResponse = JsonSerializer.Deserialize<ValidationResponse>(responseBody);
                        throw new HttpRequestException($"Server error: {response.StatusCode} - {errorResponse?.message ?? responseBody}");
                    }
                }
                catch (HttpRequestException ex)
                {
                    if (attempt < maxRetries)
                    {
                        Console.ForegroundColor = ConsoleColor.Yellow;
                        Console.WriteLine($"⚠️ Connection error: {ex.Message}, retrying...");
                        Console.WriteLine($"🔄 Retrying validation... (Attempt {attempt + 1}/{maxRetries})");
                        Console.ResetColor();
                        await Task.Delay(2000); // Wait 2 seconds before retry
                        
                        // Create new content for retry
                        content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");
                    }
                    else
                    {
                        throw;
                    }
                }
            }
        }

        /// <summary>
        /// Get download token for firmware download
        /// </summary>
        public static string GetDownloadToken() => downloadToken;

        /// <summary>
        /// Get download timestamp
        /// </summary>
        public static string GetDownloadTimestamp() => downloadTimestamp;

        /// <summary>
        /// Clear sensitive license data
        /// </summary>
        public static void ClearSensitiveData()
        {
            licenseKey = "";
            downloadToken = "";
            downloadTimestamp = "";
        }

        /// <summary>
        /// Validation response model
        /// </summary>
        private class ValidationResponse
        {
            public bool success { get; set; }
            public string? message { get; set; }
            public long remainingTime { get; set; }
            public string? expiresAt { get; set; }
            public string? downloadToken { get; set; }
        }
    }
}
