const express = require('express');
const crypto = require('crypto');
const fs = require('fs').promises;
const path = require('path');
const rateLimit = require('express-rate-limit');
const router = express.Router();

// ===== SECURITY CONFIGURATION =====
const FIRMWARE_ENCRYPTION_KEY = process.env.FIRMWARE_ENCRYPTION_KEY || crypto.randomBytes(32);
const FIRMWARE_ACCESS_TOKEN = process.env.FIRMWARE_ACCESS_TOKEN || 'octane_firmware_2024_secure';
const FIRMWARE_DIRECTORY = path.join(__dirname, '../firmware');

// Rate limiting for firmware downloads
const firmwareDownloadLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 10, // Limit each IP to 10 firmware downloads per windowMs
    message: {
        error: 'Too many firmware download requests',
        retryAfter: '15 minutes'
    },
    standardHeaders: true,
    legacyHeaders: false,
});

// ===== FIRMWARE METADATA =====
const FIRMWARE_VERSIONS = {
    'esp32s2_enhanced': {
        version: '2.1.0',
        description: 'ESP32-S2 Enhanced HID Mouse Firmware',
        files: {
            bootloader: 'bootloader_esp32s2_enhanced.bin',
            firmware: 'octane_esp32s2_enhanced_v2.1.0.bin',
            partitions: 'partitions_esp32s2_enhanced.bin'
        },
        checksum: null, // Will be calculated dynamically
        size: 0, // Will be calculated dynamically
        security_level: 'ADVANCED',
        anti_detection: true
    },
    'esp32s2_standard': {
        version: '2.0.0',
        description: 'ESP32-S2 Standard HID Mouse Firmware',
        files: {
            bootloader: 'bootloader_esp32s2.bin',
            firmware: 'octane_esp32s2_v2.0.0.bin',
            partitions: 'partitions_esp32s2.bin'
        },
        checksum: null,
        size: 0,
        security_level: 'STANDARD',
        anti_detection: false
    }
};

// ===== UTILITY FUNCTIONS =====
function generateSecureToken(hardwareId, timestamp) {
    const data = `${hardwareId}:${timestamp}:${FIRMWARE_ACCESS_TOKEN}`;
    return crypto.createHmac('sha256', FIRMWARE_ENCRYPTION_KEY)
                 .update(data)
                 .digest('hex');
}

function validateSecureToken(hardwareId, timestamp, token) {
    const expectedToken = generateSecureToken(hardwareId, timestamp);
    const currentTime = Date.now();
    const tokenTime = parseInt(timestamp);
    
    // Token must be used within 5 minutes
    if (currentTime - tokenTime > 5 * 60 * 1000) {
        return false;
    }
    
    return crypto.timingSafeEqual(
        Buffer.from(token, 'hex'),
        Buffer.from(expectedToken, 'hex')
    );
}

async function encryptFirmware(firmwareBuffer) {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher('aes-256-cbc', FIRMWARE_ENCRYPTION_KEY);
    
    let encrypted = cipher.update(firmwareBuffer);
    encrypted = Buffer.concat([encrypted, cipher.final()]);
    
    return {
        iv: iv.toString('hex'),
        data: encrypted.toString('base64')
    };
}

async function calculateFileChecksum(filePath) {
    try {
        const fileBuffer = await fs.readFile(filePath);
        return crypto.createHash('sha256').update(fileBuffer).digest('hex');
    } catch (error) {
        return null;
    }
}

async function getFirmwareMetadata(firmwareType) {
    const metadata = FIRMWARE_VERSIONS[firmwareType];
    if (!metadata) return null;
    
    // Calculate checksums and sizes if not cached
    if (!metadata.checksum) {
        const firmwarePath = path.join(FIRMWARE_DIRECTORY, metadata.files.firmware);
        metadata.checksum = await calculateFileChecksum(firmwarePath);
        
        try {
            const stats = await fs.stat(firmwarePath);
            metadata.size = stats.size;
        } catch (error) {
            metadata.size = 0;
        }
    }
    
    return metadata;
}

// ===== AUTHENTICATION MIDDLEWARE =====
function authenticateFirmwareRequest(req, res, next) {
    const { hardware_id, timestamp, token } = req.headers;
    
    if (!hardware_id || !timestamp || !token) {
        return res.status(401).json({
            error: 'Missing authentication headers',
            required: ['hardware_id', 'timestamp', 'token']
        });
    }
    
    if (!validateSecureToken(hardware_id, timestamp, token)) {
        return res.status(401).json({
            error: 'Invalid authentication token'
        });
    }
    
    req.hardwareId = hardware_id;
    next();
}

// ===== ROUTES =====

// Get available firmware versions
router.get('/versions', firmwareDownloadLimiter, async (req, res) => {
    try {
        const versions = {};
        
        for (const [key, firmware] of Object.entries(FIRMWARE_VERSIONS)) {
            const metadata = await getFirmwareMetadata(key);
            if (metadata) {
                versions[key] = {
                    version: metadata.version,
                    description: metadata.description,
                    size: metadata.size,
                    security_level: metadata.security_level,
                    anti_detection: metadata.anti_detection
                };
            }
        }
        
        res.json({
            success: true,
            versions: versions,
            timestamp: Date.now()
        });
    } catch (error) {
        console.error('Error getting firmware versions:', error);
        res.status(500).json({
            error: 'Failed to retrieve firmware versions'
        });
    }
});

// Generate download token
router.post('/token', firmwareDownloadLimiter, async (req, res) => {
    try {
        const { hardware_id, firmware_type } = req.body;
        
        if (!hardware_id || !firmware_type) {
            return res.status(400).json({
                error: 'Missing required fields',
                required: ['hardware_id', 'firmware_type']
            });
        }
        
        if (!FIRMWARE_VERSIONS[firmware_type]) {
            return res.status(400).json({
                error: 'Invalid firmware type',
                available: Object.keys(FIRMWARE_VERSIONS)
            });
        }
        
        const timestamp = Date.now().toString();
        const token = generateSecureToken(hardware_id, timestamp);
        
        res.json({
            success: true,
            token: token,
            timestamp: timestamp,
            expires_in: 300, // 5 minutes
            firmware_type: firmware_type
        });
    } catch (error) {
        console.error('Error generating firmware token:', error);
        res.status(500).json({
            error: 'Failed to generate download token'
        });
    }
});

// Download firmware file
router.get('/download/:firmware_type/:file_type', 
    firmwareDownloadLimiter, 
    authenticateFirmwareRequest, 
    async (req, res) => {
    try {
        const { firmware_type, file_type } = req.params;
        const { encrypt } = req.query;
        
        const metadata = await getFirmwareMetadata(firmware_type);
        if (!metadata) {
            return res.status(404).json({
                error: 'Firmware type not found'
            });
        }
        
        const fileName = metadata.files[file_type];
        if (!fileName) {
            return res.status(404).json({
                error: 'File type not found',
                available: Object.keys(metadata.files)
            });
        }
        
        const filePath = path.join(FIRMWARE_DIRECTORY, fileName);
        
        try {
            const fileBuffer = await fs.readFile(filePath);
            
            if (encrypt === 'true') {
                // Return encrypted firmware
                const encrypted = await encryptFirmware(fileBuffer);
                
                res.json({
                    success: true,
                    firmware_type: firmware_type,
                    file_type: file_type,
                    version: metadata.version,
                    encrypted: true,
                    iv: encrypted.iv,
                    data: encrypted.data,
                    checksum: crypto.createHash('sha256').update(fileBuffer).digest('hex'),
                    size: fileBuffer.length,
                    timestamp: Date.now()
                });
            } else {
                // Return raw firmware (for development/testing)
                res.set({
                    'Content-Type': 'application/octet-stream',
                    'Content-Disposition': `attachment; filename="${fileName}"`,
                    'Content-Length': fileBuffer.length,
                    'X-Firmware-Version': metadata.version,
                    'X-Firmware-Checksum': crypto.createHash('sha256').update(fileBuffer).digest('hex')
                });
                
                res.send(fileBuffer);
            }
            
            // Log download
            console.log(`Firmware download: ${firmware_type}/${file_type} by ${req.hardwareId}`);
            
        } catch (fileError) {
            console.error('Error reading firmware file:', fileError);
            res.status(404).json({
                error: 'Firmware file not found'
            });
        }
        
    } catch (error) {
        console.error('Error downloading firmware:', error);
        res.status(500).json({
            error: 'Failed to download firmware'
        });
    }
});

// Get firmware metadata
router.get('/metadata/:firmware_type', async (req, res) => {
    try {
        const { firmware_type } = req.params;
        
        const metadata = await getFirmwareMetadata(firmware_type);
        if (!metadata) {
            return res.status(404).json({
                error: 'Firmware type not found'
            });
        }
        
        res.json({
            success: true,
            firmware_type: firmware_type,
            metadata: {
                version: metadata.version,
                description: metadata.description,
                security_level: metadata.security_level,
                anti_detection: metadata.anti_detection,
                size: metadata.size,
                checksum: metadata.checksum,
                files: Object.keys(metadata.files)
            },
            timestamp: Date.now()
        });
    } catch (error) {
        console.error('Error getting firmware metadata:', error);
        res.status(500).json({
            error: 'Failed to retrieve firmware metadata'
        });
    }
});

// Health check
router.get('/health', (req, res) => {
    res.json({
        success: true,
        service: 'Octane Firmware Distribution Service',
        version: '1.0.0',
        status: 'operational',
        timestamp: Date.now()
    });
});

module.exports = router;
