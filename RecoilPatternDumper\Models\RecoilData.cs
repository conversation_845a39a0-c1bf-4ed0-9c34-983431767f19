using Newtonsoft.Json;

namespace RecoilPatternDumper.Models
{
    public class RecoilDump
    {
        [JsonProperty("weapons")]
        public List<Weapon> Weapons { get; set; } = new();

        [JsonProperty("attachments")]
        public List<Attachment> Attachments { get; set; } = new();
    }

    public class Weapon
    {
        [JsonProperty("name")]
        public string Name { get; set; } = string.Empty;

        [JsonProperty("repeat-delay")]
        public double RepeatDelay { get; set; }

        [JsonProperty("is-automatic")]
        public bool IsAutomatic { get; set; }

        [JsonProperty("has-ads")]
        public bool HasAds { get; set; }

        [JsonProperty("mag-cap")]
        public int MagCap { get; set; }

        [JsonProperty("stance-penalty")]
        public double StancePenalty { get; set; }

        [JsonProperty("fov-offset")]
        public double FovOffset { get; set; }

        [JsonProperty("zoom-factor")]
        public double ZoomFactor { get; set; }

        [JsonProperty("move-penalty")]
        public double MovePenalty { get; set; }

        [JsonProperty("recoil")]
        public RecoilProperties Recoil { get; set; } = new();
    }

    public class RecoilProperties
    {
        [JsonProperty("curves-as-scalar")]
        public bool CurvesAsScalar { get; set; }

        [JsonProperty("yaw-max")]
        public double YawMax { get; set; }

        [JsonProperty("yaw-min")]
        public double YawMin { get; set; }

        [JsonProperty("pitch-max")]
        public double PitchMax { get; set; }

        [JsonProperty("pitch-min")]
        public double PitchMin { get; set; }

        [JsonProperty("time-min")]
        public double TimeMin { get; set; }

        [JsonProperty("time-max")]
        public double TimeMax { get; set; }

        [JsonProperty("ads-scale")]
        public double AdsScale { get; set; }

        [JsonProperty("max-radius")]
        public double MaxRadius { get; set; }

        [JsonProperty("shots-until-max")]
        public int ShotsUntilMax { get; set; }

        [JsonProperty("pitch-curve")]
        public List<double> PitchCurve { get; set; } = new();

        [JsonProperty("yaw-curve")]
        public List<double> YawCurve { get; set; } = new();
    }

    public class Attachment
    {
        [JsonProperty("name")]
        public string Name { get; set; } = string.Empty;

        [JsonProperty("type")]
        public string Type { get; set; } = string.Empty;

        [JsonProperty("fov-bias")]
        public double FovBias { get; set; }

        [JsonProperty("fov-offset")]
        public double FovOffset { get; set; }

        [JsonProperty("zoom-factor")]
        public double ZoomFactor { get; set; }

        [JsonProperty("modifiers")]
        public AttachmentModifiers Modifiers { get; set; } = new();
    }

    public class AttachmentModifiers
    {
        [JsonProperty("recoil")]
        public double Recoil { get; set; }

        [JsonProperty("repeat-delay")]
        public double RepeatDelay { get; set; }
    }
}
