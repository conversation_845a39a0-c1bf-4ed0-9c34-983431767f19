using MathNet.Numerics.Statistics;

namespace RecoilPatternDumper.Models
{
    public class WeaponStatistics
    {
        public string WeaponName { get; set; } = string.Empty;
        public int TotalRuns { get; set; }
        public RecoilStatistics RecoilStats { get; set; } = new();
        public double RepeatDelayMean { get; set; }
        public double RepeatDelayStdDev { get; set; }
        public double MovePenaltyMean { get; set; }
        public double MovePenaltyStdDev { get; set; }
        public List<double> OptimalPitchCurve { get; set; } = new();
        public List<double> OptimalYawCurve { get; set; } = new();
        public List<double> PitchCurveStdDev { get; set; } = new();
        public List<double> YawCurveStdDev { get; set; } = new();
        public double QualityScore { get; set; } // Overall quality metric
    }

    public class RecoilStatistics
    {
        public double YawMaxMean { get; set; }
        public double YawMaxStdDev { get; set; }
        public double YawMinMean { get; set; }
        public double YawMinStdDev { get; set; }
        public double PitchMaxMean { get; set; }
        public double PitchMaxStdDev { get; set; }
        public double PitchMinMean { get; set; }
        public double PitchMinStdDev { get; set; }
        public double TimeMinMean { get; set; }
        public double TimeMinStdDev { get; set; }
        public double TimeMaxMean { get; set; }
        public double TimeMaxStdDev { get; set; }
        public double AdsScaleMean { get; set; }
        public double AdsScaleStdDev { get; set; }
        public double MaxRadiusMean { get; set; }
        public double MaxRadiusStdDev { get; set; }
    }

    public class DumpRunResult
    {
        public int RunNumber { get; set; }
        public DateTime Timestamp { get; set; }
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public RecoilDump? Data { get; set; }
        public TimeSpan ExecutionTime { get; set; }
    }

    public class OptimizedRecoilPattern
    {
        public string WeaponName { get; set; } = string.Empty;
        public List<double> OptimalPitchPattern { get; set; } = new();
        public List<double> OptimalYawPattern { get; set; } = new();
        public double ConfidenceScore { get; set; }
        public int BasedOnRuns { get; set; }
        public DateTime GeneratedAt { get; set; }
        public RecoilProperties OptimalProperties { get; set; } = new();
    }

    public static class StatisticalAnalyzer
    {
        public static WeaponStatistics AnalyzeWeapon(string weaponName, List<Weapon> weaponData)
        {
            if (!weaponData.Any())
                return new WeaponStatistics { WeaponName = weaponName };

            var stats = new WeaponStatistics
            {
                WeaponName = weaponName,
                TotalRuns = weaponData.Count
            };

            // Calculate basic statistics
            var repeatDelays = weaponData.Select(w => w.RepeatDelay).ToArray();
            var movePenalties = weaponData.Select(w => w.MovePenalty).ToArray();

            stats.RepeatDelayMean = repeatDelays.Mean();
            stats.RepeatDelayStdDev = repeatDelays.StandardDeviation();
            stats.MovePenaltyMean = movePenalties.Mean();
            stats.MovePenaltyStdDev = movePenalties.StandardDeviation();

            // Calculate recoil statistics
            stats.RecoilStats = CalculateRecoilStatistics(weaponData);

            // Calculate optimal curves
            CalculateOptimalCurves(weaponData, stats);

            // Calculate quality score
            stats.QualityScore = CalculateQualityScore(stats);

            return stats;
        }

        private static RecoilStatistics CalculateRecoilStatistics(List<Weapon> weaponData)
        {
            var recoilStats = new RecoilStatistics();
            
            var yawMaxValues = weaponData.Select(w => w.Recoil.YawMax).ToArray();
            var yawMinValues = weaponData.Select(w => w.Recoil.YawMin).ToArray();
            var pitchMaxValues = weaponData.Select(w => w.Recoil.PitchMax).ToArray();
            var pitchMinValues = weaponData.Select(w => w.Recoil.PitchMin).ToArray();
            var timeMinValues = weaponData.Select(w => w.Recoil.TimeMin).ToArray();
            var timeMaxValues = weaponData.Select(w => w.Recoil.TimeMax).ToArray();
            var adsScaleValues = weaponData.Select(w => w.Recoil.AdsScale).ToArray();
            var maxRadiusValues = weaponData.Select(w => w.Recoil.MaxRadius).ToArray();

            recoilStats.YawMaxMean = yawMaxValues.Mean();
            recoilStats.YawMaxStdDev = yawMaxValues.StandardDeviation();
            recoilStats.YawMinMean = yawMinValues.Mean();
            recoilStats.YawMinStdDev = yawMinValues.StandardDeviation();
            recoilStats.PitchMaxMean = pitchMaxValues.Mean();
            recoilStats.PitchMaxStdDev = pitchMaxValues.StandardDeviation();
            recoilStats.PitchMinMean = pitchMinValues.Mean();
            recoilStats.PitchMinStdDev = pitchMinValues.StandardDeviation();
            recoilStats.TimeMinMean = timeMinValues.Mean();
            recoilStats.TimeMinStdDev = timeMinValues.StandardDeviation();
            recoilStats.TimeMaxMean = timeMaxValues.Mean();
            recoilStats.TimeMaxStdDev = timeMaxValues.StandardDeviation();
            recoilStats.AdsScaleMean = adsScaleValues.Mean();
            recoilStats.AdsScaleStdDev = adsScaleValues.StandardDeviation();
            recoilStats.MaxRadiusMean = maxRadiusValues.Mean();
            recoilStats.MaxRadiusStdDev = maxRadiusValues.StandardDeviation();

            return recoilStats;
        }

        private static void CalculateOptimalCurves(List<Weapon> weaponData, WeaponStatistics stats)
        {
            if (!weaponData.Any() || !weaponData.First().Recoil.PitchCurve.Any())
                return;

            int curveLength = weaponData.First().Recoil.PitchCurve.Count;
            
            // Calculate optimal pitch curve
            for (int i = 0; i < curveLength; i++)
            {
                var pitchValues = weaponData.Select(w => w.Recoil.PitchCurve[i]).ToArray();
                var yawValues = weaponData.Select(w => w.Recoil.YawCurve[i]).ToArray();

                stats.OptimalPitchCurve.Add(pitchValues.Mean());
                stats.OptimalYawCurve.Add(yawValues.Mean());
                stats.PitchCurveStdDev.Add(pitchValues.StandardDeviation());
                stats.YawCurveStdDev.Add(yawValues.StandardDeviation());
            }
        }

        private static double CalculateQualityScore(WeaponStatistics stats)
        {
            // Quality score based on consistency (lower standard deviation = higher quality)
            double avgStdDev = (stats.RecoilStats.YawMaxStdDev + stats.RecoilStats.PitchMaxStdDev + 
                               stats.RepeatDelayStdDev + stats.MovePenaltyStdDev) / 4.0;
            
            // Invert and normalize to 0-100 scale
            return Math.Max(0, 100 - (avgStdDev * 100));
        }
    }
}
