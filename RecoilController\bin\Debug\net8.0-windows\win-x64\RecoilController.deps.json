{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0/win-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {}, ".NETCoreApp,Version=v8.0/win-x64": {"RecoilController/1.0.0": {"dependencies": {"CommunityToolkit.Mvvm": "8.2.2", "Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.Hosting": "7.0.1", "Microsoft.Extensions.Logging": "7.0.0", "Microsoft.Web.WebView2": "1.0.2210.55", "Microsoft.Win32.Registry": "5.0.0", "Microsoft.Xaml.Behaviors.Wpf": "1.1.122", "ModernWpfUI": "0.9.6", "Newtonsoft.Json": "13.0.3", "System.Drawing.Common": "8.0.8", "System.IO.Ports": "7.0.0", "System.Management": "9.0.7", "System.Security.Cryptography.ProtectedData": "7.0.1", "System.Text.Json": "9.0.0", "runtimepack.Microsoft.NETCore.App.Runtime.win-x64": "8.0.17", "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64": "8.0.17"}, "runtime": {"RecoilController.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/8.0.17": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "********", "fileVersion": "13.0.1725.26602"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Formats.Tar.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.Http.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Private.Uri.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Runtime.InteropServices.JavaScript.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "netstandard.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}}, "native": {"Microsoft.DiaSymReader.Native.amd64.dll": {"fileVersion": "14.42.34436.0"}, "System.IO.Compression.Native.dll": {"fileVersion": "8.0.1725.26602"}, "clretwrc.dll": {"fileVersion": "8.0.1725.26602"}, "clrgc.dll": {"fileVersion": "8.0.1725.26602"}, "clrjit.dll": {"fileVersion": "8.0.1725.26602"}, "coreclr.dll": {"fileVersion": "8.0.1725.26602"}, "createdump.exe": {"fileVersion": "8.0.1725.26602"}, "hostfxr.dll": {"fileVersion": "8.0.1725.26602"}, "hostpolicy.dll": {"fileVersion": "8.0.1725.26602"}, "mscordaccore.dll": {"fileVersion": "8.0.1725.26602"}, "mscordaccore_amd64_amd64_8.0.1725.26602.dll": {"fileVersion": "8.0.1725.26602"}, "mscordbi.dll": {"fileVersion": "8.0.1725.26602"}, "mscorrc.dll": {"fileVersion": "8.0.1725.26602"}, "msquic.dll": {"fileVersion": "*******"}}}, "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64/8.0.17": {"runtime": {"Accessibility.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26604"}, "DirectWriteForwarder.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "Microsoft.VisualBasic.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26604"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1725.26604"}, "Microsoft.Win32.Registry.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "PresentationCore.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "PresentationFramework-SystemCore.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "PresentationFramework-SystemData.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "PresentationFramework-SystemDrawing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "PresentationFramework-SystemXml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "PresentationFramework-SystemXmlLinq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "PresentationFramework.Aero.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "PresentationFramework.Aero2.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "PresentationFramework.AeroLite.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "PresentationFramework.Classic.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "PresentationFramework.Luna.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "PresentationFramework.Royale.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "PresentationFramework.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "PresentationUI.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "ReachFramework.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Design.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26604"}, "System.Diagnostics.EventLog.Messages.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.DirectoryServices.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26604"}, "System.Drawing.Design.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26604"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26604"}, "System.IO.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Printing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "System.Resources.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Threading.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Windows.Controls.Ribbon.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Windows.Forms.Design.Editors.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26604"}, "System.Windows.Forms.Design.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26604"}, "System.Windows.Forms.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26604"}, "System.Windows.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26604"}, "System.Windows.Input.Manipulations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "System.Windows.Presentation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "System.Xaml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "UIAutomationClient.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "UIAutomationClientSideProviders.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "UIAutomationProvider.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "UIAutomationTypes.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "WindowsFormsIntegration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}}, "native": {"D3DCompiler_47_cor3.dll": {"fileVersion": "10.0.22621.3233"}, "PenImc_cor3.dll": {"fileVersion": "8.0.1725.26609"}, "PresentationNative_cor3.dll": {"fileVersion": "8.0.25.16802"}, "vcruntime140_cor3.dll": {"fileVersion": "14.44.34918.1"}, "wpfgfx_cor3.dll": {"fileVersion": "8.0.1725.26609"}}}, "CommunityToolkit.Mvvm/8.2.2": {"runtime": {"lib/net6.0/CommunityToolkit.Mvvm.dll": {"assemblyVersion": "8.2.0.0", "fileVersion": "8.2.2.1"}}}, "Microsoft.Extensions.Configuration/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Configuration.Binder/7.0.3": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.323.6910"}}}, "Microsoft.Extensions.Configuration.CommandLine/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Configuration.FileExtensions/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Physical": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Configuration.Json/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "7.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "7.0.0", "System.Text.Json": "9.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Configuration.UserSecrets/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Configuration.Json": "7.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Physical": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"runtime": {"lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.FileProviders.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.FileProviders.Physical/7.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "7.0.0", "Microsoft.Extensions.FileSystemGlobbing": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.FileSystemGlobbing/7.0.0": {"runtime": {"lib/net7.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Hosting/7.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Configuration.Binder": "7.0.3", "Microsoft.Extensions.Configuration.CommandLine": "7.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "7.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "7.0.0", "Microsoft.Extensions.Configuration.Json": "7.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "7.0.0", "Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Physical": "7.0.0", "Microsoft.Extensions.Hosting.Abstractions": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Configuration": "7.0.0", "Microsoft.Extensions.Logging.Console": "7.0.0", "Microsoft.Extensions.Logging.Debug": "7.0.0", "Microsoft.Extensions.Logging.EventLog": "7.0.0", "Microsoft.Extensions.Logging.EventSource": "7.0.0", "Microsoft.Extensions.Options": "7.0.1", "System.Diagnostics.DiagnosticSource": "7.0.1"}, "runtime": {"lib/net7.0/Microsoft.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.323.6910"}}}, "Microsoft.Extensions.Hosting.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Logging/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.1"}, "runtime": {"lib/net7.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {"runtime": {"lib/net7.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Logging.Configuration/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Configuration.Binder": "7.0.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.1", "Microsoft.Extensions.Options.ConfigurationExtensions": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Logging.Console/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Configuration": "7.0.0", "Microsoft.Extensions.Options": "7.0.1", "System.Text.Json": "9.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.Logging.Console.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Logging.Debug/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.Logging.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Logging.EventLog/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.1", "System.Diagnostics.EventLog": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.Logging.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Logging.EventSource/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.1", "Microsoft.Extensions.Primitives": "7.0.0", "System.Text.Json": "9.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.Logging.EventSource.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Options/7.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.323.6910"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Configuration.Binder": "7.0.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.1", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Primitives/7.0.0": {"runtime": {"lib/net7.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.NETCore.Platforms/5.0.0": {}, "Microsoft.Web.WebView2/1.0.2210.55": {"runtime": {"lib/netcoreapp3.0/Microsoft.Web.WebView2.Core.dll": {"assemblyVersion": "1.0.2210.55", "fileVersion": "1.0.2210.55"}, "lib/netcoreapp3.0/Microsoft.Web.WebView2.WinForms.dll": {"assemblyVersion": "1.0.2210.55", "fileVersion": "1.0.2210.55"}, "lib/netcoreapp3.0/Microsoft.Web.WebView2.Wpf.dll": {"assemblyVersion": "1.0.2210.55", "fileVersion": "1.0.2210.55"}}, "native": {"runtimes/win-x64/native/WebView2Loader.dll": {"fileVersion": "1.0.2210.55"}}}, "Microsoft.Win32.Registry/5.0.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "Microsoft.Win32.SystemEvents/8.0.0": {}, "Microsoft.Xaml.Behaviors.Wpf/1.1.122": {"runtime": {"lib/net6.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"assemblyVersion": "*******", "fileVersion": "1.1.122.28819"}}}, "ModernWpfUI/0.9.6": {"runtime": {"lib/net5.0-windows7.0/ModernWpf.Controls.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net5.0-windows7.0/ModernWpf.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "resources": {"lib/net5.0-windows7.0/af-ZA/ModernWpf.Controls.resources.dll": {"locale": "af-ZA"}, "lib/net5.0-windows7.0/af-ZA/ModernWpf.resources.dll": {"locale": "af-ZA"}, "lib/net5.0-windows7.0/am-ET/ModernWpf.Controls.resources.dll": {"locale": "am-ET"}, "lib/net5.0-windows7.0/am-ET/ModernWpf.resources.dll": {"locale": "am-ET"}, "lib/net5.0-windows7.0/ar-SA/ModernWpf.Controls.resources.dll": {"locale": "ar-SA"}, "lib/net5.0-windows7.0/ar-SA/ModernWpf.resources.dll": {"locale": "ar-SA"}, "lib/net5.0-windows7.0/az-Latn-AZ/ModernWpf.Controls.resources.dll": {"locale": "az-Latn-AZ"}, "lib/net5.0-windows7.0/az-Latn-AZ/ModernWpf.resources.dll": {"locale": "az-Latn-AZ"}, "lib/net5.0-windows7.0/be-BY/ModernWpf.Controls.resources.dll": {"locale": "be-BY"}, "lib/net5.0-windows7.0/be-BY/ModernWpf.resources.dll": {"locale": "be-BY"}, "lib/net5.0-windows7.0/bg-BG/ModernWpf.Controls.resources.dll": {"locale": "bg-BG"}, "lib/net5.0-windows7.0/bg-BG/ModernWpf.resources.dll": {"locale": "bg-BG"}, "lib/net5.0-windows7.0/bn-BD/ModernWpf.Controls.resources.dll": {"locale": "bn-BD"}, "lib/net5.0-windows7.0/bn-BD/ModernWpf.resources.dll": {"locale": "bn-BD"}, "lib/net5.0-windows7.0/bs-Latn-BA/ModernWpf.Controls.resources.dll": {"locale": "bs-Latn-BA"}, "lib/net5.0-windows7.0/bs-Latn-BA/ModernWpf.resources.dll": {"locale": "bs-Latn-BA"}, "lib/net5.0-windows7.0/ca-ES/ModernWpf.Controls.resources.dll": {"locale": "ca-ES"}, "lib/net5.0-windows7.0/ca-ES/ModernWpf.resources.dll": {"locale": "ca-ES"}, "lib/net5.0-windows7.0/cs-CZ/ModernWpf.Controls.resources.dll": {"locale": "cs-CZ"}, "lib/net5.0-windows7.0/cs-CZ/ModernWpf.resources.dll": {"locale": "cs-CZ"}, "lib/net5.0-windows7.0/da-DK/ModernWpf.Controls.resources.dll": {"locale": "da-DK"}, "lib/net5.0-windows7.0/da-DK/ModernWpf.resources.dll": {"locale": "da-DK"}, "lib/net5.0-windows7.0/de-DE/ModernWpf.Controls.resources.dll": {"locale": "de-DE"}, "lib/net5.0-windows7.0/de-DE/ModernWpf.resources.dll": {"locale": "de-DE"}, "lib/net5.0-windows7.0/el-GR/ModernWpf.Controls.resources.dll": {"locale": "el-GR"}, "lib/net5.0-windows7.0/el-GR/ModernWpf.resources.dll": {"locale": "el-GR"}, "lib/net5.0-windows7.0/en-GB/ModernWpf.Controls.resources.dll": {"locale": "en-GB"}, "lib/net5.0-windows7.0/en-GB/ModernWpf.resources.dll": {"locale": "en-GB"}, "lib/net5.0-windows7.0/es-ES/ModernWpf.Controls.resources.dll": {"locale": "es-ES"}, "lib/net5.0-windows7.0/es-ES/ModernWpf.resources.dll": {"locale": "es-ES"}, "lib/net5.0-windows7.0/es-MX/ModernWpf.Controls.resources.dll": {"locale": "es-MX"}, "lib/net5.0-windows7.0/es-MX/ModernWpf.resources.dll": {"locale": "es-MX"}, "lib/net5.0-windows7.0/et-EE/ModernWpf.Controls.resources.dll": {"locale": "et-EE"}, "lib/net5.0-windows7.0/et-EE/ModernWpf.resources.dll": {"locale": "et-EE"}, "lib/net5.0-windows7.0/eu-ES/ModernWpf.Controls.resources.dll": {"locale": "eu-ES"}, "lib/net5.0-windows7.0/eu-ES/ModernWpf.resources.dll": {"locale": "eu-ES"}, "lib/net5.0-windows7.0/fa-IR/ModernWpf.Controls.resources.dll": {"locale": "fa-IR"}, "lib/net5.0-windows7.0/fa-IR/ModernWpf.resources.dll": {"locale": "fa-IR"}, "lib/net5.0-windows7.0/fi-FI/ModernWpf.Controls.resources.dll": {"locale": "fi-FI"}, "lib/net5.0-windows7.0/fi-FI/ModernWpf.resources.dll": {"locale": "fi-FI"}, "lib/net5.0-windows7.0/fil-PH/ModernWpf.Controls.resources.dll": {"locale": "fil-PH"}, "lib/net5.0-windows7.0/fil-PH/ModernWpf.resources.dll": {"locale": "fil-PH"}, "lib/net5.0-windows7.0/fr-CA/ModernWpf.Controls.resources.dll": {"locale": "fr-CA"}, "lib/net5.0-windows7.0/fr-CA/ModernWpf.resources.dll": {"locale": "fr-CA"}, "lib/net5.0-windows7.0/fr-FR/ModernWpf.Controls.resources.dll": {"locale": "fr-FR"}, "lib/net5.0-windows7.0/fr-FR/ModernWpf.resources.dll": {"locale": "fr-FR"}, "lib/net5.0-windows7.0/gl-ES/ModernWpf.Controls.resources.dll": {"locale": "gl-ES"}, "lib/net5.0-windows7.0/gl-ES/ModernWpf.resources.dll": {"locale": "gl-ES"}, "lib/net5.0-windows7.0/he-IL/ModernWpf.Controls.resources.dll": {"locale": "he-IL"}, "lib/net5.0-windows7.0/he-IL/ModernWpf.resources.dll": {"locale": "he-IL"}, "lib/net5.0-windows7.0/hi-IN/ModernWpf.Controls.resources.dll": {"locale": "hi-IN"}, "lib/net5.0-windows7.0/hi-IN/ModernWpf.resources.dll": {"locale": "hi-IN"}, "lib/net5.0-windows7.0/hr-HR/ModernWpf.Controls.resources.dll": {"locale": "hr-HR"}, "lib/net5.0-windows7.0/hr-HR/ModernWpf.resources.dll": {"locale": "hr-HR"}, "lib/net5.0-windows7.0/hu-HU/ModernWpf.Controls.resources.dll": {"locale": "hu-HU"}, "lib/net5.0-windows7.0/hu-HU/ModernWpf.resources.dll": {"locale": "hu-HU"}, "lib/net5.0-windows7.0/id-ID/ModernWpf.Controls.resources.dll": {"locale": "id-ID"}, "lib/net5.0-windows7.0/id-ID/ModernWpf.resources.dll": {"locale": "id-ID"}, "lib/net5.0-windows7.0/is-IS/ModernWpf.Controls.resources.dll": {"locale": "is-IS"}, "lib/net5.0-windows7.0/is-IS/ModernWpf.resources.dll": {"locale": "is-IS"}, "lib/net5.0-windows7.0/it-IT/ModernWpf.Controls.resources.dll": {"locale": "it-IT"}, "lib/net5.0-windows7.0/it-IT/ModernWpf.resources.dll": {"locale": "it-IT"}, "lib/net5.0-windows7.0/ja-JP/ModernWpf.Controls.resources.dll": {"locale": "ja-<PERSON>"}, "lib/net5.0-windows7.0/ja-JP/ModernWpf.resources.dll": {"locale": "ja-<PERSON>"}, "lib/net5.0-windows7.0/ka-GE/ModernWpf.Controls.resources.dll": {"locale": "ka-GE"}, "lib/net5.0-windows7.0/ka-GE/ModernWpf.resources.dll": {"locale": "ka-GE"}, "lib/net5.0-windows7.0/kk-KZ/ModernWpf.Controls.resources.dll": {"locale": "kk-KZ"}, "lib/net5.0-windows7.0/kk-KZ/ModernWpf.resources.dll": {"locale": "kk-KZ"}, "lib/net5.0-windows7.0/km-KH/ModernWpf.Controls.resources.dll": {"locale": "km-KH"}, "lib/net5.0-windows7.0/km-KH/ModernWpf.resources.dll": {"locale": "km-KH"}, "lib/net5.0-windows7.0/kn-IN/ModernWpf.Controls.resources.dll": {"locale": "kn-IN"}, "lib/net5.0-windows7.0/kn-IN/ModernWpf.resources.dll": {"locale": "kn-IN"}, "lib/net5.0-windows7.0/ko-KR/ModernWpf.Controls.resources.dll": {"locale": "ko-KR"}, "lib/net5.0-windows7.0/ko-KR/ModernWpf.resources.dll": {"locale": "ko-KR"}, "lib/net5.0-windows7.0/lo-LA/ModernWpf.Controls.resources.dll": {"locale": "lo-LA"}, "lib/net5.0-windows7.0/lo-LA/ModernWpf.resources.dll": {"locale": "lo-LA"}, "lib/net5.0-windows7.0/lt-LT/ModernWpf.Controls.resources.dll": {"locale": "lt-LT"}, "lib/net5.0-windows7.0/lt-LT/ModernWpf.resources.dll": {"locale": "lt-LT"}, "lib/net5.0-windows7.0/lv-LV/ModernWpf.Controls.resources.dll": {"locale": "lv-LV"}, "lib/net5.0-windows7.0/lv-LV/ModernWpf.resources.dll": {"locale": "lv-LV"}, "lib/net5.0-windows7.0/mk-MK/ModernWpf.Controls.resources.dll": {"locale": "mk-MK"}, "lib/net5.0-windows7.0/mk-MK/ModernWpf.resources.dll": {"locale": "mk-MK"}, "lib/net5.0-windows7.0/ml-IN/ModernWpf.Controls.resources.dll": {"locale": "ml-IN"}, "lib/net5.0-windows7.0/ml-IN/ModernWpf.resources.dll": {"locale": "ml-IN"}, "lib/net5.0-windows7.0/ms-MY/ModernWpf.Controls.resources.dll": {"locale": "ms-MY"}, "lib/net5.0-windows7.0/ms-MY/ModernWpf.resources.dll": {"locale": "ms-MY"}, "lib/net5.0-windows7.0/nb-NO/ModernWpf.Controls.resources.dll": {"locale": "nb-NO"}, "lib/net5.0-windows7.0/nb-NO/ModernWpf.resources.dll": {"locale": "nb-NO"}, "lib/net5.0-windows7.0/nl-NL/ModernWpf.Controls.resources.dll": {"locale": "nl-NL"}, "lib/net5.0-windows7.0/nl-NL/ModernWpf.resources.dll": {"locale": "nl-NL"}, "lib/net5.0-windows7.0/nn-NO/ModernWpf.Controls.resources.dll": {"locale": "nn-NO"}, "lib/net5.0-windows7.0/nn-NO/ModernWpf.resources.dll": {"locale": "nn-NO"}, "lib/net5.0-windows7.0/pl-PL/ModernWpf.Controls.resources.dll": {"locale": "pl-PL"}, "lib/net5.0-windows7.0/pl-PL/ModernWpf.resources.dll": {"locale": "pl-PL"}, "lib/net5.0-windows7.0/pt-BR/ModernWpf.Controls.resources.dll": {"locale": "pt-BR"}, "lib/net5.0-windows7.0/pt-BR/ModernWpf.resources.dll": {"locale": "pt-BR"}, "lib/net5.0-windows7.0/pt-PT/ModernWpf.Controls.resources.dll": {"locale": "pt-PT"}, "lib/net5.0-windows7.0/pt-PT/ModernWpf.resources.dll": {"locale": "pt-PT"}, "lib/net5.0-windows7.0/ro-RO/ModernWpf.Controls.resources.dll": {"locale": "ro-RO"}, "lib/net5.0-windows7.0/ro-RO/ModernWpf.resources.dll": {"locale": "ro-RO"}, "lib/net5.0-windows7.0/ru-RU/ModernWpf.Controls.resources.dll": {"locale": "ru-RU"}, "lib/net5.0-windows7.0/ru-RU/ModernWpf.resources.dll": {"locale": "ru-RU"}, "lib/net5.0-windows7.0/sk-SK/ModernWpf.Controls.resources.dll": {"locale": "sk-SK"}, "lib/net5.0-windows7.0/sk-SK/ModernWpf.resources.dll": {"locale": "sk-SK"}, "lib/net5.0-windows7.0/sl-SI/ModernWpf.Controls.resources.dll": {"locale": "sl-SI"}, "lib/net5.0-windows7.0/sl-SI/ModernWpf.resources.dll": {"locale": "sl-SI"}, "lib/net5.0-windows7.0/sq-AL/ModernWpf.Controls.resources.dll": {"locale": "sq-AL"}, "lib/net5.0-windows7.0/sq-AL/ModernWpf.resources.dll": {"locale": "sq-AL"}, "lib/net5.0-windows7.0/sr-Latn-RS/ModernWpf.Controls.resources.dll": {"locale": "sr-Latn-RS"}, "lib/net5.0-windows7.0/sr-Latn-RS/ModernWpf.resources.dll": {"locale": "sr-Latn-RS"}, "lib/net5.0-windows7.0/sv-SE/ModernWpf.Controls.resources.dll": {"locale": "sv-SE"}, "lib/net5.0-windows7.0/sv-SE/ModernWpf.resources.dll": {"locale": "sv-SE"}, "lib/net5.0-windows7.0/sw-KE/ModernWpf.Controls.resources.dll": {"locale": "sw-KE"}, "lib/net5.0-windows7.0/sw-KE/ModernWpf.resources.dll": {"locale": "sw-KE"}, "lib/net5.0-windows7.0/ta-IN/ModernWpf.Controls.resources.dll": {"locale": "ta-IN"}, "lib/net5.0-windows7.0/ta-IN/ModernWpf.resources.dll": {"locale": "ta-IN"}, "lib/net5.0-windows7.0/te-IN/ModernWpf.Controls.resources.dll": {"locale": "te-IN"}, "lib/net5.0-windows7.0/te-IN/ModernWpf.resources.dll": {"locale": "te-IN"}, "lib/net5.0-windows7.0/th-TH/ModernWpf.Controls.resources.dll": {"locale": "th-TH"}, "lib/net5.0-windows7.0/th-TH/ModernWpf.resources.dll": {"locale": "th-TH"}, "lib/net5.0-windows7.0/tr-TR/ModernWpf.Controls.resources.dll": {"locale": "tr-TR"}, "lib/net5.0-windows7.0/tr-TR/ModernWpf.resources.dll": {"locale": "tr-TR"}, "lib/net5.0-windows7.0/uk-UA/ModernWpf.Controls.resources.dll": {"locale": "uk-UA"}, "lib/net5.0-windows7.0/uk-UA/ModernWpf.resources.dll": {"locale": "uk-UA"}, "lib/net5.0-windows7.0/uz-Latn-UZ/ModernWpf.Controls.resources.dll": {"locale": "uz-Latn-UZ"}, "lib/net5.0-windows7.0/uz-Latn-UZ/ModernWpf.resources.dll": {"locale": "uz-Latn-UZ"}, "lib/net5.0-windows7.0/vi-VN/ModernWpf.Controls.resources.dll": {"locale": "vi-VN"}, "lib/net5.0-windows7.0/vi-VN/ModernWpf.resources.dll": {"locale": "vi-VN"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}, "runtime.linux-arm.runtime.native.System.IO.Ports/7.0.0": {}, "runtime.linux-arm64.runtime.native.System.IO.Ports/7.0.0": {}, "runtime.linux-x64.runtime.native.System.IO.Ports/7.0.0": {}, "runtime.native.System.IO.Ports/7.0.0": {"dependencies": {"runtime.linux-arm.runtime.native.System.IO.Ports": "7.0.0", "runtime.linux-arm64.runtime.native.System.IO.Ports": "7.0.0", "runtime.linux-x64.runtime.native.System.IO.Ports": "7.0.0", "runtime.osx-arm64.runtime.native.System.IO.Ports": "7.0.0", "runtime.osx-x64.runtime.native.System.IO.Ports": "7.0.0"}}, "runtime.osx-arm64.runtime.native.System.IO.Ports/7.0.0": {}, "runtime.osx-x64.runtime.native.System.IO.Ports/7.0.0": {}, "System.CodeDom/9.0.7": {"runtime": {"lib/net8.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "System.Diagnostics.DiagnosticSource/7.0.1": {}, "System.Diagnostics.EventLog/7.0.0": {}, "System.Drawing.Common/8.0.8": {"dependencies": {"Microsoft.Win32.SystemEvents": "8.0.0"}}, "System.IO.Pipelines/9.0.0": {"runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.IO.Ports/7.0.0": {"dependencies": {"runtime.native.System.IO.Ports": "7.0.0"}, "runtime": {"runtimes/win/lib/net7.0/System.IO.Ports.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Management/9.0.7": {"dependencies": {"System.CodeDom": "9.0.7"}, "runtime": {"runtimes/win/lib/net8.0/System.Management.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "System.Security.AccessControl/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Security.Cryptography.ProtectedData/7.0.1": {}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encodings.Web/9.0.0": {"runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Text.Json/9.0.0": {"dependencies": {"System.IO.Pipelines": "9.0.0", "System.Text.Encodings.Web": "9.0.0"}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}}}, "libraries": {"RecoilController/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/8.0.17": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64/8.0.17": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "CommunityToolkit.Mvvm/8.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-r0g0k9tGYdrnz8R7T3x5UiokDffeevzK/2P/9SBL6fqLgN8B157MIi/bVUWI1KAz6ZorZrK9AdABCWUeXZZsvA==", "path": "communitytoolkit.mvvm/8.2.2", "hashPath": "communitytoolkit.mvvm.8.2.2.nupkg.sha512"}, "Microsoft.Extensions.Configuration/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tldQUBWt/xeH2K7/hMPPo5g8zuLc3Ro9I5d4o/XrxvxOCA2EZBtW7bCHHTc49fcBtvB8tLAb/Qsmfrq+2SJ4vA==", "path": "microsoft.extensions.configuration/7.0.0", "hashPath": "microsoft.extensions.configuration.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-f34u2eaqIjNO9YLHBz8rozVZ+TcFiFs0F3r7nUJd7FRkVSxk8u4OpoK226mi49MwexHOR2ibP9MFvRUaLilcQQ==", "path": "microsoft.extensions.configuration.abstractions/7.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/7.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-1eRFwJBrkkncTpvh6mivB8zg4uBVm6+Y6stEJERrVEqZZc8Hvf+N1iIgj2ySYDUQko4J1Gw1rLf1M8bG83F0eA==", "path": "microsoft.extensions.configuration.binder/7.0.3", "hashPath": "microsoft.extensions.configuration.binder.7.0.3.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-a8Iq8SCw5m8W5pZJcPCgBpBO4E89+NaObPng+ApIhrGSv9X4JPrcFAaGM4sDgR0X83uhLgsNJq8VnGP/wqhr8A==", "path": "microsoft.extensions.configuration.commandline/7.0.0", "hashPath": "microsoft.extensions.configuration.commandline.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-RIkfqCkvrAogirjsqSrG1E1FxgrLsOZU2nhRbl07lrajnxzSU2isj2lwQah0CtCbLWo/pOIukQzM1GfneBUnxA==", "path": "microsoft.extensions.configuration.environmentvariables/7.0.0", "hashPath": "microsoft.extensions.configuration.environmentvariables.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xk2lRJ1RDuqe57BmgvRPyCt6zyePKUmvT6iuXqiHR+/OIIgWVR8Ff5k2p6DwmqY8a17hx/OnrekEhziEIeQP6Q==", "path": "microsoft.extensions.configuration.fileextensions/7.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LDNYe3uw76W35Jci+be4LDf2lkQZe0A7EEYQVChFbc509CpZ4Iupod8li4PUXPBhEUOFI/rlQNf5xkzJRQGvtA==", "path": "microsoft.extensions.configuration.json/7.0.0", "hashPath": "microsoft.extensions.configuration.json.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-33HPW1PmB2RS0ietBQyvOxjp4O3wlt+4tIs8KPyMn1kqp04goiZGa7+3mc69NRLv6bphkLDy0YR7Uw3aZyf8Zw==", "path": "microsoft.extensions.configuration.usersecrets/7.0.0", "hashPath": "microsoft.extensions.configuration.usersecrets.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-elNeOmkeX3eDVG6pYVeV82p29hr+UKDaBhrZyWvWLw/EVZSYEkZlQdkp0V39k/Xehs2Qa0mvoCvkVj3eQxNQ1Q==", "path": "microsoft.extensions.dependencyinjection/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-h3j/QfmFN4S0w4C2A6X7arXij/M/OVw3uQHSOFxnND4DyAzO1F9eMX7Eti7lU/OkSthEE0WzRsfT/Dmx86jzCw==", "path": "microsoft.extensions.dependencyinjection.abstractions/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NyawiW9ZT/liQb34k9YqBSNPLuuPkrjMgQZ24Y/xXX1RoiBkLUdPMaQTmxhZ5TYu8ZKZ9qayzil75JX95vGQUg==", "path": "microsoft.extensions.fileproviders.abstractions/7.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-K8D2MTR+EtzkbZ8z80LrG7Ur64R7ZZdRLt1J5cgpc/pUWl0C6IkAUapPuK28oionHueCPELUqq0oYEvZfalNdg==", "path": "microsoft.extensions.fileproviders.physical/7.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2jONjKHiF+E92ynz2ZFcr9OvxIw+rTGMPEH+UZGeHTEComVav93jQUWGkso8yWwVBcEJGcNcZAaqY01FFJcj7w==", "path": "microsoft.extensions.filesystemglobbing/7.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-aoeMou6XSW84wiqd895OdaGyO9PfH6nohQJ0XBcshRDafbdIU6PQIVl8TpOCssPYq3ciRseP5064hbFyCR9J9w==", "path": "microsoft.extensions.hosting/7.0.1", "hashPath": "microsoft.extensions.hosting.7.0.1.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-43n9Je09z0p/7ViPxfRqs5BUItRLNVh5b6JH40F2Agkh2NBsY/jpNYTtbCcxrHCsA3oRmbR6RJBzUutB4VZvNQ==", "path": "microsoft.extensions.hosting.abstractions/7.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nw2muoNrOG5U5qa2ZekXwudUn2BJcD41e65zwmDHb1fQegTX66UokLWZkJRpqSSHXDOWZ5V0iqhbxOEky91atA==", "path": "microsoft.extensions.logging/7.0.0", "hashPath": "microsoft.extensions.logging.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-kmn78+LPVMOWeITUjIlfxUPDsI0R6G0RkeAMBmQxAJ7vBJn4q2dTva7pWi65ceN5vPGjJ9q/Uae2WKgvfktJAw==", "path": "microsoft.extensions.logging.abstractions/7.0.0", "hashPath": "microsoft.extensions.logging.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FLDA0HcffKA8ycoDQLJuCNGIE42cLWPxgdQGRBaSzZrYTkMBjnf9zrr8pGT06psLq9Q+RKWmmZczQ9bCrXEBcA==", "path": "microsoft.extensions.logging.configuration/7.0.0", "hashPath": "microsoft.extensions.logging.configuration.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qt5n8bHLZPUfuRnFxJKW5q9ZwOTncdh96rtWzWpX3Y/064MlxzCSw2ELF5Jlwdo+Y4wK3I47NmUTFsV7Sg8rqg==", "path": "microsoft.extensions.logging.console/7.0.0", "hashPath": "microsoft.extensions.logging.console.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tFGGyPDpJ8ZdQdeckCArP7nZuoY3am9zJWuvp4OD1bHq65S0epW9BNHzAWeaIO4eYwWnGm1jRNt3vRciH8H6MA==", "path": "microsoft.extensions.logging.debug/7.0.0", "hashPath": "microsoft.extensions.logging.debug.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Rp7cYL9xQRVTgjMl77H5YDxszAaO+mlA+KT0BnLSVhuCoKQQOOs1sSK2/x8BK2dZ/lKeAC/CVF+20Ef2dpKXwg==", "path": "microsoft.extensions.logging.eventlog/7.0.0", "hashPath": "microsoft.extensions.logging.eventlog.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MxQXndQFviIyOPqyMeLNshXnmqcfzEHE2wWcr7BF1unSisJgouZ3tItnq+aJLGPojrW8OZSC/ZdRoR6wAq+c7w==", "path": "microsoft.extensions.logging.eventsource/7.0.0", "hashPath": "microsoft.extensions.logging.eventsource.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-pZRDYdN1FpepOIfHU62QoBQ6zdAoTvnjxFfqAzEd9Jhb2dfhA5i6jeTdgGgcgTWFRC7oT0+3XrbQu4LjvgX1Nw==", "path": "microsoft.extensions.options/7.0.1", "hashPath": "microsoft.extensions.options.7.0.1.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-95UnxZkkFdXxF6vSrtJsMHCzkDeSMuUWGs2hDT54cX+U5eVajrCJ3qLyQRW+CtpTt5OJ8bmTvpQVHu1DLhH+cA==", "path": "microsoft.extensions.options.configurationextensions/7.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-um1KU5kxcRp3CNuI8o/GrZtD4AIOXDk+RLsytjZ9QPok3ttLUelLKpilVPuaFT3TFjOhSibUAso0odbOaCDj3Q==", "path": "microsoft.extensions.primitives/7.0.0", "hashPath": "microsoft.extensions.primitives.7.0.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "path": "microsoft.netcore.platforms/5.0.0", "hashPath": "microsoft.netcore.platforms.5.0.0.nupkg.sha512"}, "Microsoft.Web.WebView2/1.0.2210.55": {"type": "package", "serviceable": true, "sha512": "sha512-pb5kuzl8TQm+ztzOnOm/qX75DXXjOQMdKlcs2hyizbZobiGzJcMcIhpmMmgg1cTGYwof2fVhlC809YScvDbm3w==", "path": "microsoft.web.webview2/1.0.2210.55", "hashPath": "microsoft.web.webview2.1.0.2210.55.nupkg.sha512"}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "path": "microsoft.win32.registry/5.0.0", "hashPath": "microsoft.win32.registry.5.0.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9opKRyOKMCi2xJ7Bj7kxtZ1r9vbzosMvRrdEhVhDz8j8MoBGgB+WmC94yH839NPH+BclAjtQ/pyagvi/8gDLkw==", "path": "microsoft.win32.systemevents/8.0.0", "hashPath": "microsoft.win32.systemevents.8.0.0.nupkg.sha512"}, "Microsoft.Xaml.Behaviors.Wpf/1.1.122": {"type": "package", "serviceable": true, "sha512": "sha512-SgcafT189u4qX++vSCV9FLQ4BsRXU9J2esnHA9IF8GOSgnPBulFw1CW4X/FYoOXvZwdDZxlSObJUGUg1U1wSyg==", "path": "microsoft.xaml.behaviors.wpf/1.1.122", "hashPath": "microsoft.xaml.behaviors.wpf.1.1.122.nupkg.sha512"}, "ModernWpfUI/0.9.6": {"type": "package", "serviceable": true, "sha512": "sha512-eWe90HciyudQQtZVbZ9QFssS74BwK69W7cBd52AEZHmrsS4hrSA+/BpUbAFWzzFDH6FBE1xsHPiRC+wHS7xgJw==", "path": "modernwpfui/0.9.6", "hashPath": "modernwpfui.0.9.6.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "runtime.linux-arm.runtime.native.System.IO.Ports/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CBvgRaF+M0xGLDv2Geb/0v0LEADheH8aK72GRAUJdnqnJVsQO60ki1XO8M3keEhnjm+T5NvLm41pNXAVYAPiSg==", "path": "runtime.linux-arm.runtime.native.system.io.ports/7.0.0", "hashPath": "runtime.linux-arm.runtime.native.system.io.ports.7.0.0.nupkg.sha512"}, "runtime.linux-arm64.runtime.native.System.IO.Ports/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-5VCyRCtCIYU8FR/W8oo7ouFuJ8tmAg9ddsuXhfCKZfZrbaVZSKxkmNBa6fxkfYPueD0jQfOvwFBmE5c6zalCSw==", "path": "runtime.linux-arm64.runtime.native.system.io.ports/7.0.0", "hashPath": "runtime.linux-arm64.runtime.native.system.io.ports.7.0.0.nupkg.sha512"}, "runtime.linux-x64.runtime.native.System.IO.Ports/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-DV9dWDUs23OoZqMWl5IhLr3D+b9koDiSHQxFKdYgWnQbnthv8c/yDjrlrI8nMrDc71RAKCO8jlUojzuPMX04gg==", "path": "runtime.linux-x64.runtime.native.system.io.ports/7.0.0", "hashPath": "runtime.linux-x64.runtime.native.system.io.ports.7.0.0.nupkg.sha512"}, "runtime.native.System.IO.Ports/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-L4Ivegqc3B0Fee7VifFy2JST9nndm+uvJ0viLIZUaImDfnr+JmRin9Tbqd56KuMtm0eVxHpNOWZBPtKrA/1h5Q==", "path": "runtime.native.system.io.ports/7.0.0", "hashPath": "runtime.native.system.io.ports.7.0.0.nupkg.sha512"}, "runtime.osx-arm64.runtime.native.System.IO.Ports/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jFwh4sKSXZ7al5XrItEO4GdGWa6XNxvNx+LhEHjrSzOwawO1znwJ+Dy+VjnrkySX9Qi4bnHNLoiqOXbqMuka4g==", "path": "runtime.osx-arm64.runtime.native.system.io.ports/7.0.0", "hashPath": "runtime.osx-arm64.runtime.native.system.io.ports.7.0.0.nupkg.sha512"}, "runtime.osx-x64.runtime.native.System.IO.Ports/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4LrHEfke/z9+z+iuVr35NlkhdZldY8JGNMYUN+sfPK/U/6TcE+vP44I0Yv0ir1v0bqIzq3v6Qdv1c1vmp8s4g==", "path": "runtime.osx-x64.runtime.native.system.io.ports/7.0.0", "hashPath": "runtime.osx-x64.runtime.native.system.io.ports.7.0.0.nupkg.sha512"}, "System.CodeDom/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-skI5aEl6XbZelP1hZvSmzzm3mM98k22x19Zu1Lf4rmuYoFEMJr7s7Te/MWUk9twjz4utyXt3q3pYXxGxI/Y+zA==", "path": "system.codedom/9.0.7", "hashPath": "system.codedom.9.0.7.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-T9SLFxzDp0SreCffRDXSAS5G+lq6E8qP4knHS2IBjwCdx2KEvGnGZsq7gFpselYOda7l6gXsJMD93TQsFj/URA==", "path": "system.diagnostics.diagnosticsource/7.0.1", "hashPath": "system.diagnostics.diagnosticsource.7.0.1.nupkg.sha512"}, "System.Diagnostics.EventLog/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eUDP47obqQm3SFJfP6z+Fx2nJ4KKTQbXB4Q9Uesnzw9SbYdhjyoGXuvDn/gEmFY6N5Z3bFFbpAQGA7m6hrYJCw==", "path": "system.diagnostics.eventlog/7.0.0", "hashPath": "system.diagnostics.eventlog.7.0.0.nupkg.sha512"}, "System.Drawing.Common/8.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-4ZM1wvLjz9nVVExsfPAaSl/qOvU+QNedJL5rQ+2Wbow+iGeyO0e7XN07707rMBgaffEeeLrCZBwC0oHUuvRdPw==", "path": "system.drawing.common/8.0.8", "hashPath": "system.drawing.common.8.0.8.nupkg.sha512"}, "System.IO.Pipelines/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eA3cinogwaNB4jdjQHOP3Z3EuyiDII7MT35jgtnsA4vkn0LUrrSHsU0nzHTzFzmaFYeKV7MYyMxOocFzsBHpTw==", "path": "system.io.pipelines/9.0.0", "hashPath": "system.io.pipelines.9.0.0.nupkg.sha512"}, "System.IO.Ports/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0nWQjM5IofaIGpvkifN+LLuYwBG6BHlpmphLhhOJepcW12G8qToGuNDRgBzeTVBZzp33wVsESSZ8hUOCfq+8QA==", "path": "system.io.ports/7.0.0", "hashPath": "system.io.ports.7.0.0.nupkg.sha512"}, "System.Management/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-9wHNgKnZRFLZ/vSQ7+Ai9LrKkxEKh9LvC+Ta5dwQgPsyni0ET5igPATg01WW4bx/E5Q3VtRgEGithhOXaKIh0A==", "path": "system.management/9.0.7", "hashPath": "system.management.9.0.7.nupkg.sha512"}, "System.Security.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "path": "system.security.accesscontrol/5.0.0", "hashPath": "system.security.accesscontrol.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-3evI3sBfKqwYSwuBcYgShbmEgtXcg8N5Qu+jExLdkBXPty2yGDXq5m1/4sx9Exb8dqdeMPUs/d9DQ0wy/9Adwg==", "path": "system.security.cryptography.protecteddata/7.0.1", "hashPath": "system.security.cryptography.protecteddata.7.0.1.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-e2hMgAErLbKyUUwt18qSBf9T5Y+SFAL3ZedM8fLupkVj8Rj2PZ9oxQ37XX2LF8fTO1wNIxvKpihD7Of7D/NxZw==", "path": "system.text.encodings.web/9.0.0", "hashPath": "system.text.encodings.web.9.0.0.nupkg.sha512"}, "System.Text.Json/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-js7+qAu/9mQvnhA4EfGMZNEzXtJCDxgkgj8ohuxq/Qxv+R56G+ljefhiJHOxTNiw54q8vmABCWUwkMulNdlZ4A==", "path": "system.text.json/9.0.0", "hashPath": "system.text.json.9.0.0.nupkg.sha512"}}, "runtimes": {"win-x64": ["win", "any", "base"]}}