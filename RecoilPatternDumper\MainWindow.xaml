<Window x:Class="RecoilPatternDumper.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Recoil Pattern Dumper - Advanced Analysis Tool" 
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        Background="#FF2D2D30">

    <Window.Resources>
        <Style TargetType="Button">
            <Setter Property="Background" Value="#FF3F3F46"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="#FF007ACC"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="12"/>
        </Style>
        
        <Style TargetType="TextBox">
            <Setter Property="Background" Value="#FF3F3F46"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="#FF007ACC"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="5"/>
            <Setter Property="Margin" Value="5"/>
        </Style>
        
        <Style TargetType="Label">
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="12"/>
        </Style>
        
        <Style TargetType="TextBlock">
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="12"/>
        </Style>
        
        <Style TargetType="GroupBox">
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="#FF007ACC"/>
            <Setter Property="Margin" Value="5"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#FF007ACC" Padding="10">
            <TextBlock Text="Recoil Pattern Dumper - Advanced Statistical Analysis" 
                       FontSize="18" FontWeight="Bold" 
                       HorizontalAlignment="Center" Foreground="White"/>
        </Border>

        <!-- Main Content -->
        <TabControl Grid.Row="1" Background="#FF2D2D30" BorderBrush="#FF007ACC">
            
            <!-- Configuration Tab -->
            <TabItem Header="Configuration" Foreground="White">
                <ScrollViewer>
                    <StackPanel Margin="10">
                        
                        <GroupBox Header="Python Environment">
                            <StackPanel>
                                <Label Content="Python Executable Path:"/>
                                <DockPanel>
                                    <Button Name="BrowsePythonButton" Content="Browse..." DockPanel.Dock="Right" Click="BrowsePythonButton_Click"/>
                                    <TextBox Name="PythonPathTextBox" Text="python" TextChanged="PythonPathTextBox_TextChanged"/>
                                </DockPanel>
                                <TextBlock Name="PythonValidationText" Margin="5,0" FontStyle="Italic"/>
                            </StackPanel>
                        </GroupBox>

                        <GroupBox Header="Dumper Script">
                            <StackPanel>
                                <Label Content="Main.py Script Path:"/>
                                <DockPanel>
                                    <Button Name="BrowseScriptButton" Content="Browse..." DockPanel.Dock="Right" Click="BrowseScriptButton_Click"/>
                                    <TextBox Name="ScriptPathTextBox" TextChanged="ScriptPathTextBox_TextChanged"/>
                                </DockPanel>
                                <TextBlock Name="ScriptValidationText" Margin="5,0" FontStyle="Italic"/>
                            </StackPanel>
                        </GroupBox>

                        <GroupBox Header="Rust Game Directory">
                            <StackPanel>
                                <Label Content="Rust Installation Path:"/>
                                <DockPanel>
                                    <Button Name="BrowseGameButton" Content="Browse..." DockPanel.Dock="Right" Click="BrowseGameButton_Click"/>
                                    <TextBox Name="GamePathTextBox" TextChanged="GamePathTextBox_TextChanged"/>
                                </DockPanel>
                                <TextBlock Name="GameValidationText" Margin="5,0" FontStyle="Italic"/>
                            </StackPanel>
                        </GroupBox>

                        <GroupBox Header="Analysis Settings">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Column="0">
                                    <Label Content="Number of Dump Runs:"/>
                                    <Slider Name="RunCountSlider" Minimum="5" Maximum="50" Value="10" 
                                            TickFrequency="5" IsSnapToTickEnabled="True"
                                            ValueChanged="RunCountSlider_ValueChanged"/>
                                    <TextBlock Name="RunCountText" Text="10 runs" HorizontalAlignment="Center"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Column="1" Margin="20,0,0,0">
                                    <CheckBox Name="EnableDetailedLoggingCheckBox" Content="Enable Detailed Logging" 
                                              Foreground="White" IsChecked="True"/>
                                    <CheckBox Name="AutoExportCheckBox" Content="Auto-Export Results" 
                                              Foreground="White" IsChecked="True"/>
                                    <CheckBox Name="GenerateESP32CodeCheckBox" Content="Generate ESP32 Code" 
                                              Foreground="White" IsChecked="True"/>
                                </StackPanel>
                            </Grid>
                        </GroupBox>

                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- Analysis Tab -->
            <TabItem Header="Analysis" Foreground="White">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Control Panel -->
                    <Border Grid.Row="0" Background="#FF3F3F46" Padding="10">
                        <StackPanel Orientation="Horizontal">
                            <Button Name="StartAnalysisButton" Content="Start Analysis" 
                                    Click="StartAnalysisButton_Click" IsEnabled="False"/>
                            <Button Name="StopAnalysisButton" Content="Stop Analysis" 
                                    Click="StopAnalysisButton_Click" IsEnabled="False"/>
                            <Button Name="ClearLogButton" Content="Clear Log" 
                                    Click="ClearLogButton_Click"/>
                            
                            <Separator Margin="10,0"/>
                            
                            <TextBlock Text="Progress:" VerticalAlignment="Center" Margin="10,0,5,0"/>
                            <ProgressBar Name="AnalysisProgressBar" Width="200" Height="20" 
                                         VerticalAlignment="Center"/>
                            <TextBlock Name="ProgressText" Text="Ready" VerticalAlignment="Center" Margin="10,0"/>
                        </StackPanel>
                    </Border>

                    <!-- Log Output -->
                    <ScrollViewer Grid.Row="1" Margin="10">
                        <TextBox Name="LogTextBox" 
                                 IsReadOnly="True" 
                                 TextWrapping="Wrap" 
                                 VerticalScrollBarVisibility="Auto"
                                 FontFamily="Consolas"
                                 FontSize="11"
                                 Background="#FF1E1E1E"
                                 Foreground="#FFCCCCCC"/>
                    </ScrollViewer>

                    <!-- Status Bar -->
                    <Border Grid.Row="2" Background="#FF007ACC" Padding="5">
                        <DockPanel>
                            <TextBlock Name="StatusText" Text="Ready to start analysis" 
                                       Foreground="White" VerticalAlignment="Center"/>
                            <TextBlock Name="TimerText" Text="" 
                                       Foreground="White" VerticalAlignment="Center" 
                                       HorizontalAlignment="Right"/>
                        </DockPanel>
                    </Border>
                </Grid>
            </TabItem>

            <!-- Results Tab -->
            <TabItem Header="Results" Foreground="White">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="300"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Weapon List -->
                    <GroupBox Grid.Column="0" Header="Analyzed Weapons">
                        <ListBox Name="WeaponListBox" 
                                 Background="#FF3F3F46" 
                                 Foreground="White"
                                 SelectionChanged="WeaponListBox_SelectionChanged"/>
                    </GroupBox>

                    <!-- Weapon Details -->
                    <GroupBox Grid.Column="1" Header="Weapon Statistics" Margin="5,0,0,0">
                        <ScrollViewer>
                            <StackPanel Name="WeaponDetailsPanel" Margin="10">
                                <TextBlock Text="Select a weapon to view detailed statistics" 
                                           FontStyle="Italic" HorizontalAlignment="Center" 
                                           VerticalAlignment="Center" Margin="50"/>
                            </StackPanel>
                        </ScrollViewer>
                    </GroupBox>
                </Grid>
            </TabItem>

            <!-- Export Tab -->
            <TabItem Header="Export" Foreground="White">
                <StackPanel Margin="20">
                    <GroupBox Header="Export Options">
                        <StackPanel>
                            <CheckBox Name="ExportJsonCheckBox" Content="JSON Format (Optimized Patterns)" 
                                      Foreground="White" IsChecked="True"/>
                            <CheckBox Name="ExportCsvCheckBox" Content="CSV Format (Statistical Data)" 
                                      Foreground="White" IsChecked="True"/>
                            <CheckBox Name="ExportReportCheckBox" Content="Detailed Text Report" 
                                      Foreground="White" IsChecked="True"/>
                            <CheckBox Name="ExportESP32CheckBox" Content="ESP32 C++ Header File" 
                                      Foreground="White" IsChecked="True"/>
                            <CheckBox Name="ExportChartDataCheckBox" Content="Chart Data (JSON)" 
                                      Foreground="White" IsChecked="True"/>
                        </StackPanel>
                    </GroupBox>

                    <GroupBox Header="Export Location">
                        <DockPanel>
                            <Button Name="BrowseExportButton" Content="Browse..." DockPanel.Dock="Right" 
                                    Click="BrowseExportButton_Click"/>
                            <TextBox Name="ExportPathTextBox"/>
                        </DockPanel>
                    </GroupBox>

                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="20">
                        <Button Name="ExportButton" Content="Export Results" 
                                Click="ExportButton_Click" IsEnabled="False"/>
                        <Button Name="CreatePackageButton" Content="Create Complete Package" 
                                Click="CreatePackageButton_Click" IsEnabled="False"/>
                    </StackPanel>

                    <TextBlock Name="ExportStatusText" HorizontalAlignment="Center" 
                               Margin="10" FontStyle="Italic"/>
                </StackPanel>
            </TabItem>

        </TabControl>
    </Grid>
</Window>
