#ifndef COMMAND_PROCESSOR_H
#define COMMAND_PROCESSOR_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <freertos/FreeRTOS.h>
#include <freertos/queue.h>
#include "config.h"

// ===== COMMAND PROCESSOR CLASS =====
class CommandProcessor {
private:
    QueueHandle_t commandQueue;
    String commandBuffer;
    uint32_t commandsProcessed;
    uint32_t lastHeartbeat;

public:
    CommandProcessor();
    void begin();
    void processSerialCommands();
    void processCommandQueue();
    void sendHeartbeat();
    uint32_t getCommandsProcessed() const;
    void executeSmoothRecoil(float totalX, float totalY, int steps, int delayPerStep);

private:
    bool parseCommand(const String& command);
    bool parseJSONCommand(const String& jsonStr);
    bool parseSimpleCommand(const String& command);
    bool queueCommand(const HIDCommand& cmd);
    void executeCommand(const HIDCommand& cmd);
    void sendStatusResponse();
    void sendSystemInfo();
};

// Global command processor instance
extern CommandProcessor commandProcessor;

#endif // COMMAND_PROCESSOR_H
