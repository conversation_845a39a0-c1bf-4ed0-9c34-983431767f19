/*
 * Command Processor Implementation
 * Handles all command parsing, queue management, and execution
 */

#include "command_processor.h"
#include "../hardware/hardware_manager.h"
#include "../security/security_manager.h"
#include "../recoil/recoil_engine.h"

CommandProcessor::CommandProcessor() {
    commandQueue = nullptr;
    commandBuffer = "";
    commandsProcessed = 0;
    commandsSuccessful = 0;
    commandsFailed = 0;
    lastHeartbeat = 0;
    bufferIndex = 0;
    hardware = nullptr;
    security = nullptr;
    recoilEngine = nullptr;
    
    // Initialize metrics
    memset(&metrics, 0, sizeof(SystemMetrics));
    metrics.boot_time_ms = millis();
}

bool CommandProcessor::initialize() {
    Serial.println("🎮 Initializing Command Processor...");
    
    // Create command queue
    commandQueue = xQueueCreate(QUEUE_SIZE, sizeof(Command));
    
    if (commandQueue == NULL) {
        Serial.println("❌ Failed to create command queue");
        return false;
    }
    
    // Initialize command buffer
    memset(serialBuffer, 0, COMMAND_BUFFER_SIZE);
    bufferIndex = 0;
    
    lastHeartbeat = millis();
    
    Serial.println("✅ Command Processor initialized successfully");
    Serial.println("📊 Queue size: " + String(QUEUE_SIZE) + " commands");
    Serial.println("📦 Buffer size: " + String(COMMAND_BUFFER_SIZE) + " bytes");
    
    return true;
}

void CommandProcessor::setManagers(HardwareManager* hw, SecurityManager* sec, RecoilEngine* recoil) {
    hardware = hw;
    security = sec;
    recoilEngine = recoil;
    Serial.println("🔗 Command Processor linked to managers");
}

void CommandProcessor::processSerialCommands() {
    while (Serial.available()) {
        char c = Serial.read();
        
        if (c == '\n' || c == '\r') {
            if (bufferIndex > 0) {
                serialBuffer[bufferIndex] = '\0';
                String command = String(serialBuffer);
                command.trim();
                
                if (command.length() > 0) {
                    if (parseCommand(command)) {
                        commandsSuccessful++;
                        if (hardware) {
                            hardware->setLEDState(LED_ACTIVE);
                        }
                    } else {
                        commandsFailed++;
                    }
                }
                
                // Reset buffer
                bufferIndex = 0;
                memset(serialBuffer, 0, COMMAND_BUFFER_SIZE);
            }
        } else if (c >= 32 && c <= 126) { // Printable characters only
            if (bufferIndex < COMMAND_BUFFER_SIZE - 1) {
                serialBuffer[bufferIndex++] = c;
            } else {
                // Buffer overflow - reset
                Serial.println("❌ Command buffer overflow");
                bufferIndex = 0;
                memset(serialBuffer, 0, COMMAND_BUFFER_SIZE);
                commandsFailed++;
            }
        }
    }
}

void CommandProcessor::processCommandQueue() {
    Command cmd;
    
    // Process all available commands for zero delay
    while (xQueueReceive(commandQueue, &cmd, 0) == pdTRUE) {
        if (!cmd.processed) {
            // Check if this is a high priority command
            if (isHighPriorityCommand(cmd.type)) {
                executeZeroDelayCommand(cmd);
            } else {
                executeCommand(cmd);
            }
            commandsProcessed++;
        }
    }
}

bool CommandProcessor::parseCommand(const String& command) {
    String lowerCommand = command;
    lowerCommand.toLowerCase();
    
    // Handle simple commands first (most common)
    if (lowerCommand == "ping") {
        handlePingCommand();
        return true;
    }
    
    if (lowerCommand == "status") {
        handleStatusCommand();
        return true;
    }
    
    if (lowerCommand == "info") {
        handleSystemInfoCommand();
        return true;
    }

    if (lowerCommand == "calibrate") {
        handleCalibrationCommand();
        return true;
    }

    if (lowerCommand == "calibration_status") {
        handleCalibrationStatusCommand();
        return true;
    }

    if (lowerCommand == "get_calibration_data") {
        handleGetCalibrationDataCommand();
        return true;
    }

    // Handle parameterized commands
    if (lowerCommand.startsWith("mouse_move ")) {
        handleMouseMoveOptimized(command);
        return true;
    }
    
    if (lowerCommand.startsWith("recoil_smooth ")) {
        handleRecoilSmoothOptimized(command);
        return true;
    }
    
    if (lowerCommand.startsWith("template_recoil ")) {
        handleTemplateRecoilCommand(command);
        return true;
    }
    
    if (lowerCommand.startsWith("move ")) {
        handleMouseMoveCommand(command.substring(5));
        return true;
    }
    
    if (lowerCommand.startsWith("click_down ")) {
        handleClickCommand(command.substring(11), true);
        return true;
    }
    
    if (lowerCommand.startsWith("click_up ")) {
        handleClickCommand(command.substring(9), false);
        return true;
    }
    
    if (lowerCommand == "stealth_on") {
        handleStealthModeCommand("on");
        return true;
    }
    
    if (lowerCommand == "stealth_off") {
        handleStealthModeCommand("off");
        return true;
    }
    
    if (lowerCommand == "identify") {
        handleIdentifyCommand();
        return true;
    }
    
    // Try JSON parsing for complex commands
    if (command.startsWith("{") && command.endsWith("}")) {
        return parseJSONCommand(command);
    }
    
    // Unknown command
    sendErrorResponse("UNKNOWN_COMMAND:" + command);
    return false;
}

void CommandProcessor::handlePingCommand() {
    if (security) {
        sendResponse("PONG_ESP32_OCTANE_ENHANCED_" + security->getDeviceId());
    } else {
        sendResponse("PONG_ESP32_OCTANE_ENHANCED");
    }
}

void CommandProcessor::handleStatusCommand() {
    sendStatusResponse();
}

void CommandProcessor::handleMouseMoveOptimized(const String& command) {
    // Parse: "MOUSE_MOVE x,y"
    int spaceIndex = command.indexOf(' ');
    int commaIndex = command.indexOf(',');
    
    if (spaceIndex > 0 && commaIndex > spaceIndex) {
        int x = command.substring(spaceIndex + 1, commaIndex).toInt();
        int y = command.substring(commaIndex + 1).toInt();
        
        // DIRECT EXECUTION - NO QUEUE DELAY for zero delay optimization
        if (hardware) {
            hardware->sendMouseMove(x, y);
            sendResponse("MOVE_EXECUTED:" + String(x) + "," + String(y));
        }
    } else {
        sendErrorResponse("INVALID_MOUSE_MOVE_FORMAT");
    }
}

void CommandProcessor::handleRecoilSmoothOptimized(const String& command) {
    // Parse: "RECOIL_SMOOTH x,y,steps,delay"
    int spaceIndex = command.indexOf(' ');
    if (spaceIndex > 0) {
        String params = command.substring(spaceIndex + 1);
        int comma1 = params.indexOf(',');
        int comma2 = params.indexOf(',', comma1 + 1);
        int comma3 = params.indexOf(',', comma2 + 1);
        
        if (comma1 > 0 && comma2 > comma1 && comma3 > comma2) {
            int x = params.substring(0, comma1).toInt();
            int y = params.substring(comma1 + 1, comma2).toInt();
            int steps = params.substring(comma2 + 1, comma3).toInt();
            int delay = params.substring(comma3 + 1).toInt();
            
            // DIRECT EXECUTION - NO QUEUE DELAY for template precision
            if (recoilEngine) {
                recoilEngine->executeTemplateRecoil(x, y, steps, delay);
                sendResponse("RECOIL_EXECUTED:" + String(x) + "," + String(y) + "," + String(steps) + "," + String(delay));
            }
        } else {
            sendErrorResponse("INVALID_RECOIL_SMOOTH_FORMAT");
        }
    }
}

void CommandProcessor::executeCommand(const Command& cmd) {
    switch (cmd.type) {
        case CMD_MOUSE_MOVE:
            if (hardware) {
                hardware->sendMouseMove(cmd.x, cmd.y);
            }
            break;
            
        case CMD_RECOIL_SMOOTH:
            if (recoilEngine) {
                recoilEngine->executeTemplateRecoil(cmd.x, cmd.y, cmd.steps, cmd.delay);
            }
            break;
            
        case CMD_CLICK_DOWN:
            if (hardware) {
                hardware->sendMouseClick(cmd.button, true);
            }
            break;
            
        case CMD_CLICK_UP:
            if (hardware) {
                hardware->sendMouseClick(cmd.button, false);
            }
            break;
            
        case CMD_STATUS:
            sendStatusResponse();
            break;
            
        case CMD_PING:
            handlePingCommand();
            break;
            
        default:
            sendErrorResponse("UNSUPPORTED_COMMAND_TYPE:" + String(cmd.type));
            break;
    }
}

void CommandProcessor::executeZeroDelayCommand(const Command& cmd) {
    // Zero delay execution for high priority commands
    switch (cmd.type) {
        case CMD_MOUSE_MOVE:
        case CMD_ZERO_DELAY_MOVE:
            if (hardware) {
                hardware->sendMouseMove(cmd.x, cmd.y);
            }
            break;
            
        case CMD_RECOIL_SMOOTH:
        case CMD_TEMPLATE_RECOIL:
            if (recoilEngine) {
                recoilEngine->executeTemplateRecoil(cmd.x, cmd.y, cmd.steps, cmd.delay);
            }
            break;
            
        default:
            executeCommand(cmd); // Fall back to normal execution
            break;
    }
}

bool CommandProcessor::isHighPriorityCommand(CommandType type) {
    return (type == CMD_MOUSE_MOVE || 
            type == CMD_RECOIL_SMOOTH || 
            type == CMD_TEMPLATE_RECOIL || 
            type == CMD_ZERO_DELAY_MOVE);
}

void CommandProcessor::sendResponse(const String& response) {
    Serial.println(response);
}

void CommandProcessor::sendStatusResponse() {
    if (hardware) {
        DeviceStatus status = hardware->getHardwareStatus();
        Serial.println("STATUS|OK|" + String(status.uptime_ms) + "|" + 
                      String(commandsProcessed) + "|" + String(status.free_heap));
    } else {
        Serial.println("STATUS|OK|" + String(millis()) + "|" + 
                      String(commandsProcessed) + "|" + String(ESP.getFreeHeap()));
    }
}

void CommandProcessor::sendErrorResponse(const String& error) {
    Serial.println("ERROR:" + error);
}

uint32_t CommandProcessor::getCommandsProcessed() const {
    return commandsProcessed;
}

uint32_t CommandProcessor::getSuccessfulCommands() const {
    return commandsSuccessful;
}

uint32_t CommandProcessor::getFailedCommands() const {
    return commandsFailed;
}

// ===== CALIBRATION COMMAND HANDLERS =====

void CommandProcessor::handleCalibrationCommand() {
    Serial.println("🔧 Calibration command received");

    if (!recoilEngine) {
        sendErrorResponse("Recoil engine not available");
        return;
    }

    // Trigger auto-calibration
    recoilEngine->performAutoCalibration();

    sendResponse("CALIBRATION_STARTED");
    Serial.println("✅ Auto-calibration initiated");
}

void CommandProcessor::handleCalibrationStatusCommand() {
    Serial.println("📊 Calibration status requested");

    if (!recoilEngine) {
        sendErrorResponse("Recoil engine not available");
        return;
    }

    // Get calibration status from recoil engine
    String status = "CALIBRATION_STATUS:";

    if (recoilEngine->isCalibrationComplete()) {
        status += "COMPLETE";
        Serial.println("📊 Calibration status: COMPLETE");
    } else {
        status += "PENDING";
        Serial.println("📊 Calibration status: PENDING");
    }

    sendResponse(status);
}

void CommandProcessor::handleGetCalibrationDataCommand() {
    Serial.println("📋 Calibration data requested");

    if (!recoilEngine) {
        sendErrorResponse("Recoil engine not available");
        return;
    }

    // Send basic calibration info
    String response = "CALIBRATION_DATA:{";
    response += "\"complete\":" + String(recoilEngine->isCalibrationComplete() ? "true" : "false");
    response += ",\"timestamp\":" + String(millis());
    response += "}";

    sendResponse(response);
    Serial.println("📋 Calibration data sent");
}
