# ESP32-S2 Auto-Calibration System

## Overview

The ESP32-S2 firmware now includes a comprehensive auto-calibration system designed to achieve **absolute best position possible** with perfect timing accuracy. The system automatically measures and compensates for hardware-specific delays, timing variations, and positioning errors to ensure optimal performance.

## Features

### 🎯 Perfect Positioning
- **Sub-pixel precision**: Targets 0.1 pixel accuracy
- **Movement consistency**: 99%+ repeatability
- **Drift compensation**: Automatic X/Y axis drift correction
- **Sensitivity calibration**: Perfect 1:1 mouse movement mapping

### ⏱️ Perfect Timing
- **Template timing accuracy**: 99.9%+ precision
- **Microsecond compensation**: Hardware-specific timing offsets
- **Zero delay optimization**: Minimized command processing latency
- **Shot timing precision**: Exact 133ms template delays

### 🔧 Automatic Calibration
- **System-specific optimization**: Adapts to individual hardware
- **Real-time measurement**: Live timing and positioning analysis
- **Validation system**: Ensures calibration meets quality standards
- **Confidence scoring**: Quantifies calibration reliability

## How It Works

### 1. Timing System Calibration
```cpp
void calibrateTimingSystem() {
    // Tests template accurate sleep function
    // Measures actual vs expected delays
    // Calculates compensation factors
    // Optimizes for 133ms shot timing
}
```

**Measurements:**
- Average timing error (target: <100μs)
- Maximum timing error detection
- Delay compensation factor calculation
- Template timing accuracy percentage

### 2. Mouse Movement Calibration
```cpp
void calibrateMouseMovement() {
    // Tests HID mouse movement precision
    // Measures USB latency
    // Validates sub-pixel accumulator
    // Calculates sensitivity factors
}
```

**Measurements:**
- USB HID latency (target: <1ms)
- Mouse sensitivity X/Y calibration
- Sub-pixel precision validation
- Movement drift compensation

### 3. Recoil Accuracy Calibration
```cpp
void calibrateRecoilAccuracy() {
    // Tests template recoil execution
    // Measures interpolation accuracy
    // Validates shot timing precision
    // Optimizes recoil compensation
}
```

**Measurements:**
- Recoil timing accuracy (target: 95%+)
- Interpolation precision validation
- Shot delay precision measurement
- Template execution consistency

### 4. Validation System
```cpp
bool validateCalibration() {
    // Validates timing accuracy ≥95%
    // Validates position accuracy ≤0.5px
    // Validates recoil accuracy ≥90%
    // Validates USB latency ≤1ms
}
```

## Usage

### Automatic Calibration (Default)
The system automatically calibrates on startup:
```cpp
RecoilEngine recoilEngine;
recoilEngine.initialize(); // Auto-calibration runs here
```

### Manual Calibration
Trigger calibration via serial command:
```
calibrate
```

### Check Calibration Status
```
calibration_status
```

Response format:
```
CALIBRATION_STATUS:COMPLETE,CONFIDENCE:98.5%,TIMING:99.2%,POSITION:0.08px
```

### Get Detailed Calibration Data
```
get_calibration_data
```

Response format (JSON):
```json
{
  "valid": true,
  "timestamp": 12345678,
  "confidence": 0.985,
  "timing": {
    "accuracy": 99.2,
    "offset_us": 45.3,
    "compensation_factor": 0.9955,
    "shot_delay_precision": 0.9992
  },
  "movement": {
    "pixel_accuracy": 0.08,
    "sensitivity_x": 1.0000,
    "sensitivity_y": 1.0000,
    "drift_x": 0.0000,
    "drift_y": 0.0000
  },
  "system": {
    "usb_latency_us": 234.5,
    "serial_latency_us": 123.4,
    "cpu_frequency_factor": 1.0000,
    "interrupt_latency_us": 56.7
  },
  "recoil": {
    "interpolation_accuracy": 98.7,
    "interpolation_precision": 0.9987
  }
}
```

## Testing

### Python Test Script
Use the included test script to validate calibration:
```bash
python test_calibration.py [COM_PORT]
```

The script will:
1. Auto-detect ESP32 device
2. Trigger calibration
3. Test timing accuracy
4. Test recoil precision
5. Test movement consistency
6. Generate performance report

### Expected Results
- **Overall Score**: 90%+ (Excellent: 90%+, Good: 80%+, Fair: 70%+)
- **Timing Consistency**: 95%+
- **Recoil Accuracy**: 90%+
- **Movement Consistency**: 95%+

## Integration with Template System

The auto-calibration system is fully integrated with the template precision timing:

### Template Accurate Sleep (Enhanced)
```cpp
void templateAccurateSleep(int milliseconds) {
    // Apply calibration compensation
    float compensated = milliseconds * calibrationData.delay_compensation_factor;
    compensated -= (calibrationData.timing_offset_us / 1000.0f);
    
    // Execute with template precision
    // ... (template timing code)
}
```

### Template Movement (Enhanced)
```cpp
void executeTemplateMovement(int deltaX, int deltaY) {
    // Apply calibration compensation
    float calibratedX = deltaX * calibrationData.mouse_sensitivity_x;
    float calibratedY = deltaY * calibrationData.mouse_sensitivity_y;
    
    // Execute with sub-pixel precision
    // ... (template movement code)
}
```

## Performance Impact

- **Calibration Time**: ~5-10 seconds on startup
- **Memory Usage**: ~200 bytes for calibration data
- **Runtime Overhead**: <1μs per movement (negligible)
- **Accuracy Improvement**: 10-50% better positioning consistency

## Quality Standards

### Timing Requirements
- Template timing accuracy: ≥95%
- Timing offset compensation: <100μs
- Shot delay precision: ≥99%
- USB latency: ≤1ms

### Movement Requirements
- Pixel accuracy: ≤0.5px (target: 0.1px)
- Movement consistency: ≥95%
- Sensitivity accuracy: ±0.01%
- Drift compensation: <0.1px/minute

### Recoil Requirements
- Interpolation accuracy: ≥90%
- Template execution consistency: ≥95%
- Shot timing precision: ≥99%
- Overall recoil accuracy: ≥90%

## Troubleshooting

### Calibration Failed
If calibration validation fails:
1. Check USB connection stability
2. Verify hardware is not overloaded
3. Ensure adequate power supply
4. Re-run calibration in clean environment

### Poor Performance
If calibration scores are low:
1. Check for electromagnetic interference
2. Verify USB cable quality
3. Test with different USB port
4. Check system load during calibration

### Debug Mode
Enable debug output for detailed calibration information:
```cpp
#define CALIBRATION_DEBUG 1
```

## Configuration

### Calibration Thresholds
Adjust in `firmware_config.h`:
```cpp
#define CALIBRATION_TIMING_THRESHOLD 95.0f    // 95% timing accuracy
#define CALIBRATION_POSITION_THRESHOLD 0.5f   // 0.5px position accuracy
#define CALIBRATION_RECOIL_THRESHOLD 90.0f    // 90% recoil accuracy
#define CALIBRATION_LATENCY_THRESHOLD 1000.0f // 1ms USB latency
```

### Auto-Calibration Control
```cpp
#define AUTO_CALIBRATION_ENABLED true         // Enable auto-calibration
#define CALIBRATION_ON_STARTUP true           // Calibrate on startup
#define CALIBRATION_INTERVAL_HOURS 24         // Re-calibrate every 24 hours
```

## Future Enhancements

- **Adaptive calibration**: Continuous micro-adjustments during operation
- **Environmental compensation**: Temperature and voltage drift correction
- **Machine learning**: Pattern recognition for optimal settings
- **Remote calibration**: Cloud-based calibration data sharing
- **Predictive maintenance**: Early detection of hardware degradation

---

**Note**: The auto-calibration system ensures that every ESP32 device achieves its absolute best positioning performance, regardless of individual hardware variations or environmental conditions.
